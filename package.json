{"name": "e-closion-v3-frontend", "version": "3.13.0", "license": "MIT", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build"}, "private": true, "dependencies": {"@angular/animations": "13.2.2", "@angular/cdk": "^7.1.0", "@angular/common": "13.2.2", "@angular/compiler": "13.2.2", "@angular/core": "13.2.2", "@angular/forms": "13.2.2", "@angular/google-maps": "^13.2.0", "@angular/platform-browser": "13.2.2", "@angular/platform-browser-dynamic": "13.2.2", "@angular/platform-server": "13.2.2", "@angular/router": "13.2.2", "@fortawesome/free-solid-svg-icons": "^6.0.0", "@ng-select/ng-select": "^8.1.1", "@ngx-share/core": "^7.1.4", "@ngx-translate/core": "^14.0.0", "@ngx-translate/http-loader": "^7.0.0", "angular-mydatepicker": "^0.11.5", "chart.js": "^2.7.2", "core-js": "^3.18.1", "gsap": "^1.20.3", "instafeed.js": "^2.0.0", "mapbox-gl": "^2.5.0", "moment": "^2.22.0", "ng-recaptcha": "^9.0.0", "ng2-charts": "^2.4.3", "ng2-file-upload": "^1.3.0", "ngx-mask": "^13.1.1", "ngx-page-scroll": "^8.0.0", "ngx-page-scroll-core": "^8.0.0", "ngx-pagination": "^5.1.1", "ngx-pipes": "^3.0.0", "ngx-webstorage": "^9.0.0", "nouislider": "^15.5.0", "numeral": "^2.0.6", "rxjs": "^6.6.7", "rxjs-compat": "^6.3.3", "sw-precache": "^5.2.1", "swiper": "7.4.1", "tslib": "^2.0.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~13.2.2", "@angular/cli": "^13.2.2", "@angular/compiler-cli": "13.2.2", "@angular/language-service": "^13.2.2", "@types/node": "^17.0.9", "@typescript-eslint/eslint-plugin": "^5.10.0", "@typescript-eslint/parser": "^5.10.0", "eslint": "^7.12.1", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "ts-node": "~3.0.4", "typescript": "4.5.5"}}