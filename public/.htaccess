
<IfModule mod_rewrite.c>
    Options Indexes FollowSymLinks
    RewriteEngine On
    RewriteBase /

    RewriteCond %{HTTP_HOST} !^www\.
    RewriteRule ^(.*)$ http://www.%{HTTP_HOST}/$1 [R=301,L]
   
    <IfModule mod_proxy_http.c>
        RewriteCond %{HTTP_USER_AGENT} kryzabot|woobot|XML\ Sitemaps\ Generator|googlebot|baiduspider|facebookexternalhit|twitterbot|rogerbot|linkedinbot|embedly|quora\ link\ preview|showyoubot|outbrain|pinterest|slackbot|vkShare|W3C_Validator|screaming [NC,OR]
        RewriteCond %{QUERY_STRING} _escaped_fragment_
        #eclosion prerender
        RewriteRule ^(?!.*?(\.svg|\.js|\.css|\.xml|\.less|\.png|\.jpg|\.jpeg|\.gif|\.pdf|\.doc|\.txt|\.ico|\.rss|\.zip|\.mp3|\.rar|\.exe|\.wmv|\.doc|\.avi|\.ppt|\.mpg|\.mpeg|\.tif|\.wav|\.mov|\.psd|\.ai|\.xls|\.mp4|\.m4a|\.swf|\.dat|\.dmg|\.iso|\.flv|\.m4v|\.torrent|\.ttf|\.woff))(.*) http://prerender.e-closion.ca:3000/render/https://%{HTTP_HOST}/$2 [P,L]
    </IfModule>

    ### SEO 301S START HERE
    ### SEO 301S END HERE
    RewriteRule ^$ /fr [R=301,L]
    RewriteRule ^index\.html$ - [L]
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule . /index.html [L]
</IfModule>