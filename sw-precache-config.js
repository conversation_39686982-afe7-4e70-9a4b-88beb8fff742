module.exports = {
  navigateFallback: '/index.html',
  stripPrefix: 'dist',
  root: 'dist/',
  globPatterns: [
    'dist/**.js',
    'dist/**.css',
    'dist/assets/fonts/**.woff2',
    'dist/assets/images/common/logo-*.svg',
    'dist/assets/images/SVG/icons/**.svg',
    'dist/assets/images/SVG/UI/**.svg',
    'dist/assets/images/placeholder/**.jpg'
  ],
  staticFileGlobs: [
    'dist/assets/fonts/**.woff2',
    'dist/assets/images/common/logo-*.svg',
    'dist/assets/images/SVG/icons/**.svg',
    'dist/assets/images/SVG/UI/**.svg',
    'dist/assets/images/placeholder/**.jpg'
  ],
  runtimeCaching: [
    {
      urlPattern: /^https:\/\/fonts\.googleapis\.com\//,
      handler: 'cacheFirst',
      options: {
        cache: {
          maxEntries: 10,
          name: 'google-fonts-cache'
        }
      }
    },
    {
      urlPattern: /^https:\/\/fonts\.gstatic\.com\//,
      handler: 'cacheFirst',
      options: {
        cache: {
          maxEntries: 30,
          name: 'google-fonts-cache'
        }
      }
    },
    {
      urlPattern: /\.(?:png|jpg|jpeg|svg|webp)$/,
      handler: 'cacheFirst',
      options: {
        cache: {
          maxEntries: 100,
          name: 'images-cache'
        }
      }
    },
    {
      urlPattern: /\/api\//,
      handler: 'networkFirst',
      options: {
        cache: {
          maxEntries: 50,
          name: 'api-cache'
        },
        networkTimeoutSeconds: 3
      }
    }
  ]
};
