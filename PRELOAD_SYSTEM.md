# Système de Preload - <PERSON><PERSON><PERSON> document décrit le système de preload complet implémenté pour optimiser les performances de chargement du site.

## Vue d'ensemble

Le système de preload est conçu pour améliorer l'expérience utilisateur en préchargeant les ressources critiques et les données importantes avant qu'elles ne soient nécessaires.

## Composants du système

### 1. Preload des ressources statiques (index.html)

**Localisation**: `src/index.html`

**Fonctionnalités**:
- Preconnect vers les domaines externes (Google Fonts, Cookiebot, etc.)
- Preload des fonts critiques (Roboto Regular, Medium, Bold)
- Preload des images critiques (logos, placeholders)
- Preload des icônes SVG essentielles
- Preload du CSS critique avec chargement asynchrone

### 2. Service de preload des données (PreloadService)

**Localisation**: `src/app/services/v3/preload/preload.service.ts`

**Fonctionnalités**:
- Préchargement des données API critiques (page d'accueil, propriétés vedettes, etc.)
- Cache avec `shareReplay` pour éviter les appels API multiples
- Gestion des erreurs avec fallbacks
- Observable pour suivre l'état du preload

**Données préchargées**:
- Contenu de la page d'accueil
- Propriétés vedettes
- Nouvelles propriétés
- Propriétés avec visites libres
- Secteurs (neighborhoods)
- Articles de blog récents
- Témoignages

### 3. Service de preload des images (ImagePreloadService)

**Localisation**: `src/app/services/v3/preload/image-preload.service.ts`

**Fonctionnalités**:
- Preload intelligent des images avec file d'attente
- Limitation du nombre de chargements concurrents
- Priorisation des images critiques
- Preload automatique des images de propriétés
- Suivi du statut de chargement

### 4. Directive de lazy loading améliorée (LazyImageDirective)

**Localisation**: `src/app/directives/lazy-image.directive.ts`

**Fonctionnalités**:
- Détection automatique des images "above the fold"
- Lazy loading natif pour les navigateurs compatibles
- Fallback avec Intersection Observer
- Preload prioritaire pour les images critiques
- Placeholder SVG pour les images en attente

### 5. Stratégie de preload des routes (CustomPreloadingStrategy)

**Localisation**: `src/app/services/v3/preload/custom-preloading-strategy.service.ts`

**Fonctionnalités**:
- Preload immédiat des routes haute priorité
- Preload différé des routes moyenne priorité
- Configuration basée sur l'importance des pages

**Routes haute priorité**:
- Pages d'accueil (fr/en)
- Recherche de propriétés
- Pages d'achat et de vente

**Routes moyenne priorité**:
- Secteurs
- Blog
- Équipe
- Spécialistes

### 6. Configuration centralisée (PreloadConfig)

**Localisation**: `src/app/config/preload.config.ts`

**Fonctionnalités**:
- Configuration centralisée de tous les paramètres
- Listes des ressources critiques
- Paramètres de timing et de cache
- Feature flags pour activer/désactiver des fonctionnalités

### 7. Service Worker optimisé

**Localisation**: `sw-precache-config.js`

**Fonctionnalités**:
- Cache des ressources statiques critiques
- Cache runtime pour les fonts Google
- Cache des images avec stratégie cache-first
- Cache des API avec stratégie network-first

### 8. Composant de statut (PreloadStatusComponent)

**Localisation**: `src/app/components/preload-status/preload-status.component.ts`

**Fonctionnalités** (mode développement uniquement):
- Affichage en temps réel du statut de preload
- Indicateur de progression pour les images
- Liste détaillée des ressources chargées

## Utilisation

### Initialisation automatique

Le système se lance automatiquement au démarrage de l'application via `AppComponent`:

```typescript
ngOnInit() {
  // Démarrage du preload des données critiques
  this.preloadService.preloadCriticalData().subscribe();
}
```

### Utilisation dans les composants

```typescript
// Utiliser les données préchargées
this.preloadService.getFeaturedProperties().subscribe(data => {
  this.properties = data;
});

// Précharger des images de propriétés
this.preloadService.preloadPropertyImages(properties);
```

### Configuration des images critiques

```html
<!-- Marquer une image comme critique -->
<img src="hero-image.jpg" data-critical="true" alt="Hero">

<!-- Empêcher le lazy loading -->
<img src="logo.svg" data-unlazy="true" alt="Logo">
```

## Performance

### Métriques améliorées

- **First Contentful Paint (FCP)**: Réduction grâce au preload des fonts et CSS
- **Largest Contentful Paint (LCP)**: Amélioration via le preload des images hero
- **Cumulative Layout Shift (CLS)**: Stabilisation avec les placeholders
- **Time to Interactive (TTI)**: Optimisation via le preload des données critiques

### Stratégies de cache

1. **Ressources statiques**: Cache agressif avec service worker
2. **Images**: Cache-first avec fallback réseau
3. **API**: Network-first avec cache de secours
4. **Fonts**: Cache permanent avec preconnect

## Configuration

### Variables d'environnement

Le système s'adapte automatiquement selon l'environnement:

- **Production**: Toutes les fonctionnalités activées
- **Développement**: Composant de statut visible, certaines optimisations désactivées

### Personnalisation

Modifiez `src/app/config/preload.config.ts` pour:
- Ajouter/supprimer des ressources critiques
- Ajuster les priorités des routes
- Modifier les paramètres de timing
- Activer/désactiver des fonctionnalités

## Monitoring

### En développement

Le composant `PreloadStatusComponent` affiche:
- État du preload en temps réel
- Progression du chargement des images
- Liste des données préchargées

### En production

Utilisez les DevTools pour monitorer:
- Network tab: Vérifier les preloads
- Performance tab: Mesurer l'impact sur les métriques
- Application tab: Vérifier le cache du service worker

## Maintenance

### Ajout de nouvelles ressources critiques

1. Ajouter l'URL dans `PreloadConfig.criticalResources`
2. Mettre à jour `sw-precache-config.js` si nécessaire
3. Tester le preload en développement

### Optimisation continue

- Analyser les métriques de performance régulièrement
- Ajuster les priorités selon l'usage réel
- Mettre à jour les ressources critiques selon les changements de design

## Dépannage

### Images non préchargées

- Vérifier que l'URL est dans la configuration
- Contrôler la console pour les erreurs de chargement
- Vérifier les limites de concurrence

### Données non préchargées

- Vérifier la connectivité API
- Contrôler les erreurs dans la console
- Vérifier les timeouts de cache

### Service Worker

- Vérifier l'enregistrement dans la console
- Forcer la mise à jour du cache si nécessaire
- Contrôler les patterns de cache dans la configuration
