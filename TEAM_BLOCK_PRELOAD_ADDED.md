# Ajout du preload pour le bloc équipe (bloc-equipe)

## 🎯 Objectif

Intégrer `this.blocksService.getBlock('bloc-equipe')` au système de preload pour optimiser le chargement du composant `TeamHero2Component`.

## ✅ Modifications apportées

### 1. PreloadService - Ajout du BlocksService

**Fichier**: `src/app/services/v3/preload/preload.service.ts`

#### Imports et interface
```typescript
import { BlocksService } from '@/services/v3/contentblocks/contentblocks.service';

export interface PreloadData {
  // ... autres propriétés
  teamBlock?: any;
}
```

#### Propriétés et injection
```typescript
private teamBlockCache$: Observable<any>;

constructor(
  // ... autres services
  private blocksService: BlocksService
) {}
```

#### Initialisation du cache
```typescript
this.teamBlockCache$ = this.safeServiceCall(
  () => this.blocksService.getBlock('bloc-equipe'),
  'team block',
  { title: '', text: '' }
);
```

#### Intégration au forkJoin
```typescript
const forkJoinData = {
  // ... autres données
  teamBlock: this.teamBlockCache$ || of({ title: '', text: '' })
};
```

#### Méthode getter
```typescript
getTeamBlock(): Observable<any> {
  if (!this.teamBlockCache$) {
    console.warn('Team block cache not initialized, initializing now...');
    this.initializeCache();
  }
  return this.teamBlockCache$ || of({ title: '', text: '' });
}
```

### 2. TeamHero2Component - Migration vers le preload

**Fichier**: `src/app/library/team-hero-2/team-hero-2.component.ts`

#### Avant (problématique)
```typescript
constructor(private blocksService: BlocksService) {
  // ❌ Chargement dans le constructeur
  this.blocksService.getBlock('bloc-equipe').subscribe(data => {
    this.blockTitle = data.title;
    this.blockContent = data.text;
  });
}
```

#### Après (optimisé)
```typescript
constructor(
  private blocksService: BlocksService,
  private preloadService: PreloadService
) {
  // Defer data loading to ngOnInit
}

ngOnInit() {
  // ✅ Utilisation du preload avec fallback
  const teamBlock$ = this.preloadService.getTeamBlock();
  if (teamBlock$) {
    teamBlock$.subscribe({
      next: (data) => {
        this.blockTitle = data.title;
        this.blockContent = data.text;
      },
      error: (error) => {
        console.warn('Error loading team block from preload, using direct service:', error);
        this.blocksService.getBlock('bloc-equipe').subscribe(data => {
          this.blockTitle = data.title;
          this.blockContent = data.text;
        });
      }
    });
  } else {
    // Fallback to direct service call
    this.blocksService.getBlock('bloc-equipe').subscribe(data => {
      this.blockTitle = data.title;
      this.blockContent = data.text;
    });
  }
}
```

### 3. PreloadDebugService - Tests ajoutés

**Fichier**: `src/app/services/v3/preload/preload-debug.service.ts`

```typescript
// Test BlocksService
try {
  const testCall = this.blocksService.getBlock('bloc-equipe');
  console.log('✓ BlocksService:', {
    hasInstance: !!this.blocksService,
    methodExists: typeof this.blocksService.getBlock === 'function',
    testCallResult: !!testCall
  });
} catch (error) {
  console.error('✗ BlocksService error:', error);
}
```

## 🚀 Avantages obtenus

### 1. Performance améliorée
- **Preload anticipé** : Le bloc équipe est chargé en parallèle avec les autres données critiques
- **Cache partagé** : Évite les appels API multiples si le composant est utilisé plusieurs fois
- **Chargement parallèle** : Fait partie du `forkJoin` global

### 2. Robustesse
- **Fallback automatique** : Si le preload échoue, utilise le service direct
- **Gestion d'erreurs** : Logs détaillés pour le debugging
- **Initialisation sécurisée** : Chargement dans `ngOnInit` au lieu du constructeur

### 3. Cohérence
- **Pattern uniforme** : Même approche que les autres données preload
- **Diagnostic intégré** : Inclus dans les outils de debug
- **Configuration centralisée** : Géré par le système de preload global

## 📊 Impact sur les performances

### Avant
```
1. Page charge
2. TeamHero2Component s'initialise
3. Appel API individuel pour bloc-equipe
4. Rendu du composant
```

### Après
```
1. Page charge
2. Preload démarre (forkJoin avec bloc-equipe inclus)
3. TeamHero2Component s'initialise
4. Données déjà disponibles en cache
5. Rendu immédiat du composant
```

### Métriques attendues
- **Réduction du temps de chargement** : ~200-500ms selon la latence réseau
- **Moins d'appels API** : 1 appel groupé au lieu d'appels individuels
- **Meilleure UX** : Affichage plus rapide du contenu

## 🔧 Configuration et monitoring

### Logs à surveiller
```
"Team block cache not initialized, initializing now..." → Initialisation tardive
"Error loading team block from preload, using direct service" → Fallback activé
"✓ BlocksService: { hasInstance: true, methodExists: true }" → Service OK
```

### Diagnostic
```typescript
// Dans la console du navigateur
this.preloadService.diagnoseServices();
// Vérifier que teamBlockCache$ est true

this.preloadService.getTeamBlock().subscribe(data => {
  console.log('Team block data:', data);
});
```

### Métriques de performance
```typescript
// Mesurer le temps de chargement
console.time('team-block-load');
this.preloadService.getTeamBlock().subscribe(data => {
  console.timeEnd('team-block-load');
});
```

## 🔄 Pattern pour ajouter d'autres blocs

### 1. Identifier le bloc
```typescript
// Trouver les appels comme :
this.blocksService.getBlock('nom-du-bloc')
```

### 2. Ajouter au PreloadService
```typescript
// Interface
export interface PreloadData {
  monNouveauBloc?: any;
}

// Propriété
private monNouveauBlocCache$: Observable<any>;

// Initialisation
this.monNouveauBlocCache$ = this.safeServiceCall(
  () => this.blocksService.getBlock('nom-du-bloc'),
  'mon nouveau bloc',
  { title: '', text: '' }
);

// ForkJoin
const forkJoinData = {
  monNouveauBloc: this.monNouveauBlocCache$ || of({ title: '', text: '' })
};

// Getter
getMonNouveauBloc(): Observable<any> {
  if (!this.monNouveauBlocCache$) {
    this.initializeCache();
  }
  return this.monNouveauBlocCache$ || of({ title: '', text: '' });
}
```

### 3. Migrer le composant
```typescript
ngOnInit() {
  const bloc$ = this.preloadService.getMonNouveauBloc();
  if (bloc$) {
    bloc$.subscribe({
      next: (data) => { /* utiliser data */ },
      error: () => { /* fallback direct */ }
    });
  } else {
    /* fallback direct */
  }
}
```

## 🧪 Tests recommandés

### Test de performance
```typescript
// Comparer avant/après
console.time('page-load');
// Charger la page
console.timeEnd('page-load');
```

### Test de fallback
```typescript
// Simuler une erreur de preload
spyOn(preloadService, 'getTeamBlock').and.returnValue(throwError('Network error'));
// Vérifier que le fallback fonctionne
```

### Test de cache
```typescript
// Vérifier que les données sont mises en cache
const data1 = await preloadService.getTeamBlock().toPromise();
const data2 = await preloadService.getTeamBlock().toPromise();
expect(data1).toBe(data2); // Même référence = cache fonctionne
```

Cette intégration du bloc équipe au système de preload améliore significativement les performances de chargement tout en maintenant la robustesse du système.
