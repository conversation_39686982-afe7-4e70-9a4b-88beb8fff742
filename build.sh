#!/bin/bash

echo "🏗️  Building Angular application..."
node_modules/@angular/cli/bin/ng.js build --configuration production

echo "📁 Copying static files..."
cp public/.htaccess dist/
cp wwww/sitemap.xml dist/
cp public/robots.txt dist/

echo "⚡ Optimizing preload resources..."
node scripts/optimize-preload.js

echo "🔧 Generating service worker..."
node_modules/.bin/sw-precache --config=sw-precache-config.js

echo "✅ Build completed successfully!"
echo ""
echo "⚠️  IMPORTANT REMINDERS:"
echo "!!!!!!!EDIT ROBOTS.TXT IF PROD!!!!!!!"
echo "!!!!!!!EDIT ROBOTS.TXT IF PROD!!!!!!!"
echo "!!!!!!!EDIT ROBOTS.TXT IF PROD!!!!!!!"
echo "!!!!!!!EDIT ROBOTS.TXT IF PROD!!!!!!!"
echo "!!!!!!!EDIT ROBOTS.TXT IF PROD!!!!!!!"
