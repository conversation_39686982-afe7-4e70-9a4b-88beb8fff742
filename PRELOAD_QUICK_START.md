# Guide de démarrage rapide - Système de Preload

## 🚀 Mise en route

Le système de preload est maintenant configuré et fonctionne automatiquement. Voici ce que vous devez savoir :

## ✅ Ce qui est déjà configuré

### 1. Preload automatique au démarrage
- ✅ Données critiques (propriétés, page d'accueil, secteurs)
- ✅ Images importantes (logos, placeholders, icônes)
- ✅ Fonts essentielles (Roboto Regular, Medium, Bold)
- ✅ Routes prioritaires (accueil, recherche, achat/vente)

### 2. Optimisations des images
- ✅ Lazy loading intelligent avec fallback
- ✅ Preload des images "above the fold"
- ✅ Cache optimisé via service worker

### 3. Cache intelligent
- ✅ Service worker configuré pour les ressources statiques
- ✅ Cache API avec stratégie network-first
- ✅ Cache images avec stratégie cache-first

## 🔧 Utilisation dans vos composants

### Utiliser les données préchargées

```typescript
// Au lieu de :
this.inscriptionsService.getInscriptions(-1, { featured: 1 }).subscribe(data => {
  this.properties = data;
});

// Utilisez :
this.preloadService.getFeaturedProperties().subscribe(data => {
  this.properties = data;
});
```

### Précharger des images de propriétés

```typescript
// Dans votre composant
ngOnInit() {
  this.preloadService.getFeaturedProperties().subscribe(({ data }) => {
    this.properties = data;
    // Précharge automatiquement les images
    this.preloadService.preloadPropertyImages(data);
  });
}
```

### Marquer des images comme critiques

```html
<!-- Image critique (sera préchargée immédiatement) -->
<img src="hero-image.jpg" data-critical="true" alt="Hero">

<!-- Image qui ne doit pas être lazy-loadée -->
<img src="logo.svg" data-unlazy="true" alt="Logo">
```

## 📊 Monitoring en développement

Le composant de statut s'affiche automatiquement en mode développement :

```typescript
// Pour voir le statut de preload
// Regardez en haut à droite de votre écran en mode dev
```

## ⚙️ Configuration personnalisée

### Ajouter de nouvelles ressources critiques

Éditez `src/app/config/preload.config.ts` :

```typescript
export const PreloadConfig = {
  criticalResources: {
    images: [
      'assets/images/common/logo-equipe_bourdon.svg',
      'assets/images/your-new-critical-image.jpg' // ← Ajoutez ici
    ]
  }
};
```

### Ajouter de nouvelles routes prioritaires

```typescript
highPriorityRoutes: [
  'fr',
  'en',
  'fr/your-new-priority-route' // ← Ajoutez ici
]
```

## 🏗️ Build et déploiement

### Build de production

```bash
# Le script de build inclut maintenant l'optimisation automatique
./build.sh
```

### Ce qui se passe automatiquement :
1. ✅ Build Angular
2. ✅ Optimisation des preloads
3. ✅ Génération du service worker
4. ✅ Copie des fichiers statiques
5. ✅ Rapport d'optimisation

## 📈 Métriques à surveiller

### Core Web Vitals améliorés :
- **FCP (First Contentful Paint)** : Amélioration via preload fonts/CSS
- **LCP (Largest Contentful Paint)** : Amélioration via preload images hero
- **CLS (Cumulative Layout Shift)** : Stabilisation avec placeholders
- **TTI (Time to Interactive)** : Optimisation via preload données

### Outils de mesure :
- Chrome DevTools (Performance tab)
- Lighthouse
- PageSpeed Insights
- WebPageTest

## 🐛 Dépannage rapide

### Les images ne se préchargent pas ?
```typescript
// Vérifiez la console pour les erreurs
console.log('Image preload status:', this.imagePreloadService.getImageStatus('your-image.jpg'));
```

### Les données ne se préchargent pas ?
```typescript
// Vérifiez l'état du preload
this.preloadService.isPreloadComplete().subscribe(complete => {
  console.log('Preload complete:', complete);
});
```

### Service worker ne fonctionne pas ?
```javascript
// Dans la console du navigateur
navigator.serviceWorker.getRegistrations().then(registrations => {
  console.log('Service workers:', registrations);
});
```

## 🎯 Bonnes pratiques

### 1. Images
- ✅ Utilisez `data-critical="true"` pour les images hero
- ✅ Utilisez `data-unlazy="true"` pour les logos/icônes
- ✅ Optimisez les images (WebP, compression)

### 2. Données
- ✅ Utilisez les services de preload pour les données critiques
- ✅ Évitez les appels API redondants
- ✅ Gérez les erreurs avec des fallbacks

### 3. Performance
- ✅ Testez régulièrement avec Lighthouse
- ✅ Surveillez les métriques Core Web Vitals
- ✅ Ajustez la configuration selon l'usage réel

## 🔄 Maintenance

### Mise à jour des ressources critiques
1. Analysez les nouvelles ressources importantes
2. Mettez à jour `preload.config.ts`
3. Testez en développement
4. Déployez et mesurez l'impact

### Optimisation continue
- Analysez les rapports de preload générés
- Ajustez les priorités selon les analytics
- Surveillez les nouvelles opportunités d'optimisation

## 📞 Support

Pour toute question ou problème :
1. Consultez la documentation complète : `PRELOAD_SYSTEM.md`
2. Vérifiez les logs de la console en mode développement
3. Utilisez le composant de statut pour diagnostiquer les problèmes
