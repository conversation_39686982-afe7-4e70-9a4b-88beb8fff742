#!/usr/bin/env node

/**
 * Script d'optimisation du preload
 * 
 * Ce script analyse les ressources du build et génère automatiquement
 * les balises de preload optimales pour index.html
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

const DIST_DIR = path.join(__dirname, '../dist');
const INDEX_PATH = path.join(DIST_DIR, 'index.html');
const ASSETS_DIR = path.join(DIST_DIR, 'assets');

/**
 * Analyse les fichiers CSS pour extraire les fonts critiques
 */
function extractCriticalFonts() {
  const cssFiles = glob.sync(path.join(DIST_DIR, '*.css'));
  const criticalFonts = new Set();
  
  cssFiles.forEach(cssFile => {
    const content = fs.readFileSync(cssFile, 'utf8');
    
    // Recherche des références aux fonts
    const fontMatches = content.match(/url\(['"]?([^'"]+\.woff2?)['"]\)/g);
    if (fontMatches) {
      fontMatches.forEach(match => {
        const fontUrl = match.match(/url\(['"]?([^'"]+)['"]\)/)[1];
        if (fontUrl.includes('assets/fonts/')) {
          criticalFonts.add(fontUrl);
        }
      });
    }
  });
  
  return Array.from(criticalFonts);
}

/**
 * Analyse les images critiques basées sur leur taille et usage
 */
function extractCriticalImages() {
  const criticalImages = [];
  const imageDir = path.join(ASSETS_DIR, 'images');
  
  if (!fs.existsSync(imageDir)) {
    return criticalImages;
  }
  
  // Images communes critiques
  const commonDir = path.join(imageDir, 'common');
  if (fs.existsSync(commonDir)) {
    const commonImages = fs.readdirSync(commonDir);
    commonImages.forEach(img => {
      if (img.includes('logo') || img.includes('placeholder')) {
        criticalImages.push(`assets/images/common/${img}`);
      }
    });
  }
  
  // Icônes SVG critiques
  const svgDir = path.join(imageDir, 'SVG');
  if (fs.existsSync(svgDir)) {
    const iconsDir = path.join(svgDir, 'icons');
    const uiDir = path.join(svgDir, 'UI');
    
    [iconsDir, uiDir].forEach(dir => {
      if (fs.existsSync(dir)) {
        const icons = fs.readdirSync(dir);
        icons.forEach(icon => {
          if (['bed.svg', 'sink.svg', 'arrow-left.svg', 'arrow-right.svg'].includes(icon)) {
            const relativePath = path.relative(imageDir, path.join(dir, icon));
            criticalImages.push(`assets/images/${relativePath}`);
          }
        });
      }
    });
  }
  
  return criticalImages;
}

/**
 * Génère les balises de preload
 */
function generatePreloadTags() {
  const criticalFonts = extractCriticalFonts();
  const criticalImages = extractCriticalImages();
  
  let preloadTags = '\n    <!-- Auto-generated preload tags -->\n';
  
  // DNS Prefetch
  preloadTags += '    <link rel="preconnect" href="https://fonts.googleapis.com">\n';
  preloadTags += '    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>\n';
  preloadTags += '    <link rel="preconnect" href="https://consent.cookiebot.com">\n';
  preloadTags += '    <link rel="preconnect" href="https://addevent.com">\n\n';
  
  // Fonts
  if (criticalFonts.length > 0) {
    preloadTags += '    <!-- Critical fonts -->\n';
    criticalFonts.forEach(font => {
      preloadTags += `    <link rel="preload" href="${font}" as="font" type="font/woff2" crossorigin>\n`;
    });
    preloadTags += '\n';
  }
  
  // Images
  if (criticalImages.length > 0) {
    preloadTags += '    <!-- Critical images -->\n';
    criticalImages.forEach(image => {
      preloadTags += `    <link rel="preload" href="${image}" as="image">\n`;
    });
    preloadTags += '\n';
  }
  
  // CSS critique
  const cssFiles = glob.sync(path.join(DIST_DIR, 'styles.*.css'));
  if (cssFiles.length > 0) {
    const cssFile = path.basename(cssFiles[0]);
    preloadTags += '    <!-- Critical CSS -->\n';
    preloadTags += `    <link rel="preload" href="${cssFile}" as="style" onload="this.onload=null;this.rel='stylesheet'">\n`;
    preloadTags += `    <noscript><link rel="stylesheet" href="${cssFile}"></noscript>\n\n`;
  }
  
  return preloadTags;
}

/**
 * Met à jour index.html avec les balises de preload
 */
function updateIndexHtml() {
  if (!fs.existsSync(INDEX_PATH)) {
    console.error('index.html not found in dist directory');
    return;
  }
  
  let indexContent = fs.readFileSync(INDEX_PATH, 'utf8');
  const preloadTags = generatePreloadTags();
  
  // Recherche de la balise <head> pour insérer les preloads
  const headMatch = indexContent.match(/<head[^>]*>/);
  if (headMatch) {
    const headEndIndex = indexContent.indexOf('>', headMatch.index) + 1;
    indexContent = indexContent.slice(0, headEndIndex) + 
                   preloadTags + 
                   indexContent.slice(headEndIndex);
  }
  
  // Supprime les anciens preloads auto-générés
  indexContent = indexContent.replace(
    /\n\s*<!-- Auto-generated preload tags -->[\s\S]*?(?=\n\s*<(?!link rel="preload"))/,
    ''
  );
  
  fs.writeFileSync(INDEX_PATH, indexContent);
  console.log('✅ index.html updated with optimized preload tags');
}

/**
 * Génère un rapport d'optimisation
 */
function generateOptimizationReport() {
  const criticalFonts = extractCriticalFonts();
  const criticalImages = extractCriticalImages();
  
  const report = {
    timestamp: new Date().toISOString(),
    criticalFonts: criticalFonts.length,
    criticalImages: criticalImages.length,
    fonts: criticalFonts,
    images: criticalImages
  };
  
  const reportPath = path.join(DIST_DIR, 'preload-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  console.log('📊 Optimization Report:');
  console.log(`   - Critical fonts: ${criticalFonts.length}`);
  console.log(`   - Critical images: ${criticalImages.length}`);
  console.log(`   - Report saved to: ${reportPath}`);
}

/**
 * Fonction principale
 */
function main() {
  console.log('🚀 Starting preload optimization...');
  
  if (!fs.existsSync(DIST_DIR)) {
    console.error('❌ Dist directory not found. Please run build first.');
    process.exit(1);
  }
  
  try {
    updateIndexHtml();
    generateOptimizationReport();
    console.log('✅ Preload optimization completed successfully!');
  } catch (error) {
    console.error('❌ Error during optimization:', error.message);
    process.exit(1);
  }
}

// Exécution si appelé directement
if (require.main === module) {
  main();
}

module.exports = {
  extractCriticalFonts,
  extractCriticalImages,
  generatePreloadTags,
  updateIndexHtml,
  generateOptimizationReport
};
