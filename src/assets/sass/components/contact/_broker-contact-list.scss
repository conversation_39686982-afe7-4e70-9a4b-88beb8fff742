/*
	Contact Broker Components
*/

$contactBrokerBG: white;

.broker-contact-list-cpn {
  padding-top: 85px;
  padding-bottom: 80px;
  background-color: $contactBrokerBG;

  @media (max-width: 768px) {
    padding: 45px 0;
  }

  .emphase-title {
    margin-top: 15px;
    margin-bottom: 60px;
  }

  .broker-ctn {
    display: flex;
    flex-flow: row wrap;
  }

  .broker {
    width: calc(100% * 1/3 - 27px);
    padding-bottom: 60px;
    margin-right: 40px;
    display: flex;
    align-items: center;

    @media (min-width: 992px) {
      &:nth-child(3n) {
        margin-right: 0;
      }
    }

    @media (max-width: 992px) {
      width: calc(100% * 1/2 - 20px);

      &:nth-child(2n) {
        margin-right: 0;
      }
    }

    @media (max-width: 768px) {
      width: 100%;
      margin-right: 0;
    }

    .img-ctn {
      border-radius: 50%;
      overflow: hidden;
      width: 100px;
      height: 100px;
    }

    .broker-text {
      padding-left: 25px;
      width: calc(100% - 100px);
    }

    .title {
      margin-top: 0;
      margin-bottom: 5px;
      color: $secondaryMainColor;
      font-family: $primaryFont;
      font-size: $primaryTextSize;
      text-transform: uppercase;
      line-height: 28px;
    }

    .role {
      margin-top: 0;
      margin-bottom: 10px;
      color: $secondaryMainColor;
      font-family: $secondaryFont;
      font-size: 14px;
      font-weight: bold;
      line-height: 24px;
    }

    .phone {
      margin: 0;
      color: $secondaryMainColor;
      font-family: $primaryFont;
      font-size: $subTextSize;
      font-weight: 700;
      line-height: 22px;
    }
  }
}
