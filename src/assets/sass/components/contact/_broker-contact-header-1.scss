.broker-contact-header-1-cpn {
	padding-top: 60px;
	padding-bottom: 100px;
	background-color: #F2F2F2;

	@media (max-width: 768px) {
		padding-bottom: 45px;
	}

	.form-container {
		position: relative;
		&.send {
			display: flex;
			justify-content: center;
			align-items: center;

			.contact-form {
				opacity: 0;
			}

			.contact-form-ctn {
				opacity: 0;
			}

			.form-response {
				opacity: 1;
				transition: all 0.5s ease-in-out;
			}
		}

		.loading-inner{
			opacity: 0.5;
		}

		.form-loader{
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%,-50%);
		}

		.form-response {
			position: absolute;
			text-align: center;
			opacity: 0;
			padding: 0 15px;

			.button-ctn {
				justify-content: center;
			}

			&.show{
				opacity: 1;
				transition: all 0.5s ease-in-out;
			}
		}
	}

	.contact-form-ctn {
		padding-left: 0;

		&.no-padding {
			padding: 0;
		}

		@media (max-width: 768px) {
			padding-right: 0;
		}
	}

	.info-box-ctn {
		padding-right: 0;

		@media (max-width: 768px) {
			padding-left: 0;
			margin-top: 40px;
		}
	}

	.-page-title {
		margin-top: 0;
		margin-bottom: 20px;
		color: $primaryColor;
		text-transform: uppercase;
	}

	.-page-description {
		margin: 0;
		color: $secondaryMainColor;
		font-family: $secondaryFont;
		font-size: $subTextSize;
		line-height: $subTextLineHeight;
	}
	.-page-required{
		margin: 25px 0 0;
		color: $secondaryMainColor;
		font-family: $secondaryFont;
		font-size: 12px;

		&--right{
			text-align: right;
		}
	}

	.-form-result {
		.success {
			color: $primaryColor;
			font-size: 0.9rem;
		}
		.error{
			color: #bb0303;
			font-size: 0.9rem;
		}
	}

	.info-box {

		.info-text-ctn {
			padding: 35px 0;

			@media (max-width: 992px) {
				padding: 20px 0;
			}

			.logo-ctn{
				margin-bottom: 20px;
			}

			.title {
				margin-top: 0;
			}

			.block {
				display: flex;
				margin-bottom: 20px;

				&:last-child {
					margin-bottom: 0;
				}

				i {
					font-size: $subTextSize;
					color: $secondaryMainColor;
					position: relative;
					top: 4px;
				}

				p, a {
					margin: 0;
					font-family: $secondaryFont;
					color: $secondaryMainColor;
				}

				.text {
					padding-left: 15px;
				}
			}

			.location-ctn {
				i {
					font-size: $primaryTextSize;
				}

				.text {
					font-size: $detailTextSize;
					line-height: 19px;

					.links {
						margin-top: 6px;
					}

					a {
						margin-right: 20px;
						color: $secondaryMainColor;
						font-family: $primaryFont;
						font-size: 12px;
						font-weight: 600;
						text-transform: uppercase;
						line-height: $detailTextLineHeight;
					}
				}
			}

			.email-ctn {
				.text {
					font-size: $detailTextSize;
					line-height: 19px;
				}
			}

			.phone-ctn {
				.text a{
					font-size: $subTextSize;
					font-weight: 600;
					line-height: 22px;
					font-family: $primaryFont;
					display: block;

				}
			}
		}
	}

	.contact-form {
		padding-top: 60px;

		@media (max-width: 768px) {
			padding-top: 20px;
		}

		button {
			margin-top: 30px;

			@media (max-width: 768px) {
				display: block;
				margin: 0 auto;
			}
		}
	}

	&.-contact-sheet-2 {
		padding-bottom: 75px;

		.contact-form {
			padding-top: 0;
			@include clearfix;
		}
	}

	&.-contact-sheet-3 {
		padding-bottom: 75px;

		.contact-form {
			padding-top: 0;
			@include clearfix;

			button{
				margin-top: 5px;
				float: right;
			}

			textarea{
				height: 200px;
			}
		}
	}
}
