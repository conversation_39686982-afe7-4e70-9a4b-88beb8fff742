.broker-contact-header-2-cpn {
  padding: 60px 0;

	.-page-title {
		margin-top: 0;
		margin-bottom: 20px;
	}

	.sub-description {
		margin: 0 0 40px;
		color: $secondaryMainColor;
		font-family: $secondaryFont;
		font-size: $subTextSize;
		line-height: $subTextLineHeight;
	}

	.row {
		display: flex;
		align-items: flex-end;
	}

	.dual-ctn {
		display: flex;
		flex-flow: row wrap;
	}

	.broker-head {
		padding: 0 15px 105px;
	}

	.block {
		display: flex;
		margin-bottom: 15px;

		&:last-child {
			margin-bottom: 0;
		}

		i {
			font-size: $subTextSize;
			color: $secondaryMainColor;
			position: relative;
			top: 4px;
		}

		p, a {
			margin: 0;
			font-family: $secondaryFont;
			color: $secondaryMainColor;
		}

		.text {
			padding-left: 15px;
		}

		&.-dual {
			width: calc(100% * 1/2 - 25px);
			margin-right: 50px;

			&:nth-child(2n) {
				margin-right: 0;
			}
		}

		&.-clear {
			clear: both;
		}
	}

	.location-ctn {
		i {
			font-size: $primaryTextSize;
		}

		.text {
			font-size: $detailTextSize;
			line-height: 19px;

			.links {
				margin-top: 6px;
			}

			a {
				margin-right: 20px;
				color: $primaryColor;
				font-family: $secondaryFont;
				font-size: $detailTextSize;
				font-weight: 600;
				line-height: $detailTextLineHeight;
			}
		}
	}

	.email-ctn {
		.text {
			font-size: $detailTextSize;
			line-height: 19px;
		}
	}

	.phone-ctn {
		.text {
			font-size: $subTextSize;
			font-weight: 600;
			line-height: 22px;
		}
	}

  @media (max-width: 992px) {
    .row {
      display: block;
    }
  }
}
