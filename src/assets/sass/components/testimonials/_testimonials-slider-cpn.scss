/*
	Testimonials Slider Component
*/

$testimonialArrowHeight: 28px;
$testimonialArrowWidth: 17px;

$testimonialTitlePaddingTop: 75px;
$testimonialTitleSize: 24px;
$testimonialTitleColor: $white;
$testimonialTitleLineHeight: 29px;
$testimonialTitleWeight: bold;

$testimonialSwiperPaddingBottom: 100px;
$testimonialSwiperPaginationBottom: 40px;
$testimonialSwiperPaginationColor: rgba(255,255,255,0.5);
$testimonialSwiperPaginationActive: $white;

$testimonialDescriptionColor: $white;
$testimonialDescriptionSize: 24px;
$testimonialDescriptionLineHeight: 40px;
$testimonialDescriptionStyle: italic;
$testimonialDescriptionFont: $secondaryFont;

$testimonialNameColor: $white;
$testimonialNameSize: 14px;
$testimonialNameLineHeight: 19px;
$testimonialNameWeight: 600;
$testimonialNameFont: $primaryFont;

$testimonialLineWidth: 15px;
$testimonialLineHeight: 1px;
$testimonialLineColor: $white;

.testimonials-slider-cpn {
	background: $primaryColor;
	text-align: center;

	.title {
		margin-top: 0;
		padding-top: $testimonialTitlePaddingTop;
		font-family: $primaryFont;
		font-size: $testimonialTitleSize;
		color: $testimonialTitleColor;
		font-weight: $testimonialTitleWeight;
		line-height: $testimonialTitleLineHeight;
		text-transform: uppercase;
	}

	.swiper-testimonials {
		position: relative;
		padding-bottom: $testimonialSwiperPaddingBottom;

		.swiper-wrapper{
			display: flex;
			align-items: center;
		}

		@media (max-width:992px) {

			.swiper-wrapper{
				.swiper-slide{
					opacity: 0;
					transition: all 0.1s ease;
				}

				.swiper-slide-active{
					opacity: 1;
					transition: all 0.4s ease 0.8s;
				}
			}
		}
	}

	.swiper-pagination {
		bottom: $testimonialSwiperPaginationBottom;
		left: 50%;
		transform: translate(-50%, 0%);

		.swiper-pagination-bullet {
			margin-right: 10px;
			background: $testimonialSwiperPaginationColor;

			&-active {
				background: $testimonialSwiperPaginationActive;
			}

			&:last-child {
				margin-right: 0;
			}
		}
	}

	.container {

		.row{
			align-items: center;
			display: flex;
			flex-wrap: wrap;
			margin-top: 40px;
		}

		.description {
			margin: 0 auto;
			max-width: 700px;
			font-family: $testimonialDescriptionFont;
			font-style: $testimonialDescriptionStyle;
			font-size: $testimonialDescriptionSize;
			line-height: $testimonialDescriptionLineHeight;
			color: $testimonialDescriptionColor;

			p{
				margin-top: 0;
			}

			@media (max-width: 992px) {
				font-size: $subTextSize;
				line-height: 22px;
			}

			&.-left{
				text-align: left;
			}
		}

		.name {
			color: $testimonialNameColor;
			font-family: $testimonialNameFont;
			font-size: $testimonialNameSize;
			font-weight: $testimonialNameWeight;
			line-height: $testimonialNameLineHeight;

			.line {
				display: inline-block;
				width: $testimonialLineWidth;
				height: $testimonialLineHeight;
				background: $testimonialLineColor;
				position: relative;
				top: -3px;
				margin-right: 5px;
			}

			&.-left{
				text-align: left;
			}
		}

		.iframe-container {
			margin-bottom: 20px;
			position: relative;
			padding-bottom: 56.25%; /* 16:9 */
			padding-top: 25px;
			height: 0;

			iframe {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
			}
		}

		@media (max-width: 992px) {
			.col-md-6{
				width: 100%;
			}

			.description.-left{
				text-align: center;
			}

			.name.-left{
				text-align: center;
			}

		}
	}

	.swiper-btn {
		background: none;
		width: $testimonialArrowWidth;
		height: $testimonialArrowHeight;
		transform: translate(0%, -50%);
		margin-top: 0;

		&:before {
			position: absolute;
			font-family: 'icomoon' !important;
			speak: none;
			font-style: normal;
			font-weight: normal;
			font-variant: normal;
			text-transform: none;
			line-height: 1;
			font-size: 28px;
			color: white;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
		}

		@media (max-width: 992px) {
			display: none;
		}

		&.swiper-button-prev {
			left: 30px;

			&:before {
				content: "\e909";
			}
		}

		&.swiper-button-next {
			right: 30px;

			&:before {
				content: "\e90a";
			}
		}
	}
}
