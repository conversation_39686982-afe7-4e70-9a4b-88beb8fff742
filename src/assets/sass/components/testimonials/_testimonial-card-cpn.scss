/*
	Testimonial Card Component
	*/

.testimonial-card-cpn {
	display: block;
	position: relative;
	width: 100%;

	.title {
		margin-top: 0;
		margin-bottom: 20px;
		color: $secondaryMainColor;
		text-transform: uppercase;
	}

	.testimonial-content {
		padding: 40px;
		display: inline-block;
		box-shadow: 0 2px 4px 0 rgba(0,0,0,0.1);
		width: 100%;
		margin-bottom: 3px;
	}

	.testimonial-wrap {
		display: block;
		position: relative;
		width: 100%;
		margin-bottom: 20px;
		background-color: $white;
		border-radius: 3px;
	}

	.img-ctn {
		margin-bottom: 40px;

		img{
			width: 100%;
		}

		@media (max-width: 992px) {
			text-align: center;
		}
	}

	.description {
		margin-top: 0;
		margin-bottom: 20px;
		color: $secondaryMainColor !important;
		font-family: $secondaryFont !important;
		font-size: $detailTextSize !important;
		line-height: $detailTextLineHeight !important;
	}

	.user-ctn {
		display: flex;
		align-items: center;
		color: $secondaryMainColor;
		font-family: $secondaryFont;
		font-size: 14px;
		line-height: $detailTextLineHeight;
		font-weight: bold;

		p{
			display: flex;
			align-items: center;
		}



		span{
			width: 40px;
			height: 40px;
			background-color: $secondaryMainColor;
			border-radius: 50%;
			display: inline-flex;
			align-items: center;
			justify-content: center;
			margin-right: 20px;

			i{
				color: white;
				font-size: 22px;
			}

		}

		img {
			width: 42px;
			height: 42px;
			border-radius: 50%;
			margin-right: 20px;
		}
	}

	&.-alt{
		.testimonial-content{
			display: flex;
			align-items: center;
			flex-wrap: wrap;
			flex-direction: row-reverse;

			.img-ctn,.inner{
				width: calc(50% - 20px) ;
			}

			.inner{
				flex-grow: 1;
			}

			.img-ctn{
				margin-left: 40px;
				margin-bottom: 0;
			}
		}

		@media (max-width:992px) {
			.testimonial-content{
				display: block;

				.img-ctn,.inner{
					width: 100%;
				}

				.img-ctn{
					margin-left: 0;
					margin-bottom: 40px;
				}
			}
		}
	}
}
