/*
	Evaluation Form Components
*/

.evaluation-form-cpn {
  padding-top: 60px;
  padding-bottom: 80px;
  background-image: url("/assets/images/common/bg-estimation-2.jpg");
  background-size: cover;
  position: relative;

  form .ng-select.align-center .ng-option {
    text-align: left;
  }

  .ng-select.ng-select-single .ng-select-container {
    height: 42px;

    .ng-value-container {
      height: 42px;
      padding-left: 15px;
    }

    .ng-input {
      padding-left: 15px;
    }
  }

  .block {
    display: block;
  }

  .container {
    border-radius: 3px;
    background-color: #F2F2F2;
    box-shadow: 0 2px 4px 0 rgba(0,0,0,0.1);

    &.send {
      display: flex;
      justify-content: center;
      align-items: center;

      .alert-form{
        opacity: 0;
      }
    }

    @media (max-width:768px) {
      margin: 0 15px;
    }
  }

  .map-ctn {
    border: 1px solid #CCCCCC;
    margin-bottom: 20px;
    margin-top: 40px;

    .info-map-ctn {
      padding: 30px;

      p {
        margin: 0;
      }

      .found {
        margin-bottom: 20px;
        color: $secondaryMainColor;
        font-family: $primaryFont;
        font-size: 18px;
        line-height: 22px;
        text-transform: uppercase;
      }

      .address {
        color: $secondaryMainColor;
        font-family: $secondaryFont;
        font-size: $subTextSize;
        line-height: 19px;

        span {
          margin-left: 20px;
          color: $secondaryMainColor;
          text-transform: uppercase;
          font-family: $primaryFont;
          font-size: 13px;
          font-weight: 700;
          line-height: 18px;
          cursor: pointer;

          @media (max-width: 768px) {
            margin-left: 0;
          }
        }
      }
    }
  }

  #street-view {
    height: 360px;
    position: relative;

    .no-results{
      background-color: #fbfbfb;
      position: absolute;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      transition: $primaryAnimation;

      &-inner{
        text-align: center;
        padding: 20px;
        color: #666666;
      }
    }
  }

  .alert-form {
    padding: 80px 100px;

    @media (max-width: 768px) {
      padding: 60px 0px;
    }

  }

  .loading-inner{
    opacity: 0.5;
  }

  .form-loader{
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%);
  }

  .form-head {
    position: relative;
    padding-bottom: 40px;
  }

  .step {
    position: absolute;
    top: 0;
    right: 0;
    border: 2px solid $secondaryMainColor;
    border-radius: 20px;
    padding: 12px 26px;

    @media (max-width: 450px) {
      position: relative;
      max-width: 100px;
      margin: 20px auto 0;
    }

    p {
      margin: 0;
      color: $secondaryMainColor;
      font-family: $primaryFont;
      font-size: $detailTextSize;
      font-weight: bold;
      line-height: 17px;
      text-align: center;
    }
  }

  .step-ctn{
    display: none;

    &.show{
      display: block;
    }

    &#step3{
      @media(max-width:425px){
        .button-ctn{
          flex-wrap: wrap;
          justify-content: flex-start;

          .previous-button{
            margin-right: 0;
          }

          .main-button{
            margin-top:  10px;
          }
        }
      }
    }
  }

  .-page-title {
    margin: 0;
    font-size: $h2TitleSize;
    line-height: $h2TitleLineHeight;

    @media (max-width: 450px) {
      text-align: center;
    }
  }

  .-page-description {
    margin-top: 20px;
    margin-bottom: 0;
    padding-bottom: 40px;
    color: $secondaryMainColor;
    font-family: $secondaryFont;
    font-size: $primaryTextSize;
    line-height: $primaryTextLineHeight;
  }

  .emphase-title {
    margin-bottom: 0;
    margin-top: 40px;
  }

  .location-ctn, .type-ctn {
    padding-top: 20px;
    @include clearfix;

    .custom-checkbox-group {
      width: 50%;
      float: left;

      @media (max-width: 768px) {
        float: none;
        width: 100%;
      }

      label {
        color: $secondaryMainColor;
      }
    }
  }

  .input-ctn {
    padding-top: 40px;
  }

  .form-row {
    margin: 0;
  }

  .warning-message {
    //max-width: 580px;
    margin: 35px auto 0;
    color: $secondaryMainColor;
    font-family: $secondaryFont;
    font-size: $detailTextSize;
    line-height: $detailTextLineHeight;
    text-align: right;
  }

  .-page-required{
    color: $secondaryMainColor;
    font-family: $secondaryFont;
    font-size: 12px;
    line-height: $detailTextLineHeight;
    text-align: right;
  }

  .button-ctn {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-top: 40px;
  }

  .form-response {
    position: absolute;
    text-align: center;
    opacity: 0;
    padding: 0 15px;
    .button-ctn {
      justify-content: center;
    }

    &.show{
      transition: all 0.5s ease-in-out;
      opacity: 1;
    }
  }
}
