/*
	Evaluation Start Components
*/

//Disable Google logo in the dropdown
.hdpi.pac-logo:after {
	display: none;
}
.evaluation-start-cpn {
	padding-top: 160px;
	padding-bottom: 180px;
	background-image: url("/assets/images/common/Estimation-immo-en-ligne.webp");
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center center;
	position: relative;

	&:before {
		content: '';
		width: 100%;
		height: 100%;
		background: black;
		position: absolute;
		top: 0;
		left: 0;
		opacity: 0.5;
	}


	.container {
		max-width: 590px;
	}

	.-page-title {
		margin: 0;
		text-align: center;
		color: white;
		text-transform: uppercase;
	}

	.-page-description {
		margin-top: 20px;
		margin-bottom: 0;
		padding-bottom: 30px;
		color: white;
		font-family: $secondaryFont;
		font-size: $subTextSize;
		line-height: $subTextLineHeight;
		text-align: center;
	}

	.main-button {
		margin-top: 30px;
	}

	.pac-target-input {
		height: 60px;
		display: flex !important;
		justify-content: center;
		align-items: center;
		background-color: white;
		border-radius: 5px;
		width: 100%;
		border: none;
		padding-right: 35px;
		padding-left: 16px;
		font-family: $primaryFont;
	}

	#address-input{
		@media(max-width: 425px){
			text-overflow: ellipsis;
		}
	}
}
