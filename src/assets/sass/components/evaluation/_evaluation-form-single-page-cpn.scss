/*
	Evaluation Form Single Page Components
*/

.evaluation-form-single-page-cpn {
	padding-top: 60px;
	padding-bottom: 80px;
	background-color: $paleBackground;

	.block {
		display: block;
	}

	.container {
		border-radius: 3px;
		background-color: $white;
		box-shadow: 0 2px 4px 0 rgba(0,0,0,0.1);

		&.send {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 600px;

			.alert-form{
				display: none;
			}
		}

		@media(max-width: 768px){
			margin: 0 15px;

			&.send {
				.alert-form{
					display: none;
				}
			}
		}

	}

	.map-ctn {
		border: 1px solid #CCCCCC;
		margin-bottom: 55px;
		margin-top: 40px;

		.info-map-ctn {
			padding: 30px;

			p {
				margin: 0;
			}

			.found {
				margin-bottom: 20px;
				color: $black;
				font-family: $primaryFont;
				font-size: $emphaseTitleSize;
				line-height: $emphaseLineHeight;
			}

			.address {
				color: $secondaryMainColor;
				font-family: $primaryFont;
				font-size: $subTextSize;
				line-height: 19px;

				span {
					margin-left: 20px;
					color: $primaryColor;
					font-family: $secondaryFont;
					font-size: 13px;
					font-weight: 600;
					line-height: 18px;
					cursor: pointer;

					@media (max-width: 768px) {
						margin-left: 0;
					}
				}
			}
		}
	}

	#street-view {
		height: 360px;
		position: relative;

		.no-view{
			background-color: #fbfbfb;
			position: absolute;
			width: 100%;
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			transition: $primaryAnimation;
    }

		.no-results{
			background-color: #fbfbfb;
			position: absolute;
			width: 100%;
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			opacity: 0;
			transition: $primaryAnimation;

			&.show{
				opacity: 1;
				z-index: 12;
			}

			&-inner{
				text-align: center;
				padding: 20px;
				color: #666666;
			}
		}
	}

	.alert-form {
		padding: 80px 100px;

		@media (max-width: 768px) {
			padding: 60px 0px;
		}
	}

	.loading-inner{
		opacity: 0.5;
	}

	.required-input {
		color: #E71624;
	}

	.form-loader{
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%,-50%);
	}

	.form-head {
		position: relative;
		padding-bottom: 40px;
	}

	.-page-title {
		margin: 0;
		font-size: $h2TitleSize;
		line-height: $h2TitleLineHeight;

    @media (max-width: 450px) {
      text-align: center;
    }
	}

	.-page-description {
		margin-top: 20px;
		margin-bottom: 0;
		padding-bottom: 40px;
		color: $secondaryMainColor;
		font-family: $secondaryFont;
		font-size: $primaryTextSize;
		line-height: $primaryTextLineHeight;
	}

	.emphase-title {
		margin-bottom: 0;
        margin-top: 60px;
	}

	.input-ctn {
		padding-top: 40px;
		.img-weight-warning{
			color: #666666;
			font-size: 0.675rem;
			font-style: italic;
			margin-top: -10px;
		}
	}

	.warning-message {
		margin: 35px auto 0;
		color: $secondaryMainColor;
		font-family: $secondaryFont;
		font-size: $detailTextSize;
		line-height: $detailTextLineHeight;
		text-align: right;
	}

	.-page-required{
		color: $secondaryMainColor;
		font-family: $secondaryFont;
		font-size: 12px;
		line-height: $detailTextLineHeight;
		text-align: right;
	}

	.button-ctn {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		padding-top: 40px;

		@media(max-width: 500px){
			flex-direction: column;
			align-items: flex-start;

		}
	}

	.form-response {
		position: absolute;
		text-align: center;
		opacity: 0;
    	padding: 0 15px;
		.button-ctn {
			justify-content: center;
		}

		&.show{
			transition: all 0.5s ease-in-out;
			opacity: 1;
		}

		@media(max-width: 768px){
			padding: 30px;

			&.show{
				position: relative;
			}
		}
	}
}
