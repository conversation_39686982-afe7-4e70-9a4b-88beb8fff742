/*
	Blog Post Card Component
*/

$blogPostCardSpacingLeft: 80px;
$blogPostCardSpacingRight: 40px;

$blogPostCardTextColor: #666666;

.custom-content-cpn {

	.post-card {
		display: flex;
		align-items: strech;
		height: 500px;

		@media (max-width: 992px) {
			flex-direction: column !important;
			padding-bottom: 40px;
			height: unset;
		}

		&:hover {
			.filter {
				opacity: 1;
			}

			.post-card-content {
				&:before{
					transform: translateX(calc(-100% + 2px));
					transition-delay: 0.5s;
				}
			}

			&.left-post .post-card-content {
				&:before {
					transform: translateX(calc(100% - 2px));
				}
			}
		}

		&.left-post {
			flex-direction: row-reverse;
			.post-card-content {
				justify-content: flex-end;

				@media (max-width: 992px) {
					justify-content: center;
					order: 2;
				}

				&:before{
					left: inherit;
					right: 0;
				}

				.content {
					padding-left: $blogPostCardSpacingRight;
					padding-right: $blogPostCardSpacingLeft;


					@media (max-width: 992px) {
						padding: 0 15px;
					}
				}
			}
		}

		.img-ctn {
			position: relative;
			width: 50%;


			img {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}

			.filter {
				transition: $primaryAnimation;
			}


			@media (max-width: 992px) {
				width: 100%;
				
				img {
					max-height:350px;
				}
			}
		}

		.post-card-content {
			width: 50%;
			display: flex;
			align-items: center;
			position: relative;

			&:before{
				content: "";
				position: absolute;
				width: 10%;
				height: 100%;
				left: 0;
				background-color: white;
				transition: $primaryAnimation;
				z-index: 12;
			}

			@media (max-width: 992px) {
				justify-content: center;
				width: 100%;
        		margin-top: 30px;
			}

			.content {
				padding-left: $blogPostCardSpacingLeft;
				padding-right: $blogPostCardSpacingRight;
				padding-bottom: 30px;
				padding-top: 30px;
				max-width: 510px;
				z-index: 13;

				h2{
					margin-top: 0;
				}

				.main-button{
					margin-top:25px;
				}

				@media (max-width: 992px) {
					padding: 0 15px;
				}

				.title {
					@media (max-width: 992px) {
						font-size: 25px;
						line-height: 30px;
					}
				}
			}

			.description {
				font-size: $subTextSize;
				line-height: $subTextLineHeight;
				font-family: $secondaryFont;
				color: $blogPostCardTextColor;
			}
		}
	}
}
