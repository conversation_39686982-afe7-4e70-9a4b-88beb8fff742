/*
	Blog List Component
*/
$blogListPadding: 50px 0px 60px 0;
$blogListBackground: #F6F6F6;

$blogListDateColor: $secondaryMainColor;
$blogListDateSize: 13px;
$blogListDateLineHeight: 24px;

$blogListDescriptionColor: $secondaryMainColor;
$blogListDescriptionSize: 15px;
$blogListDescriptionLineHeight: 24px;

.blog-list-cpn {
	padding: $blogListPadding;
	background-color: $blogListBackground;

	&.blog-list-2, &.blog-list-3 {
		padding-top: 20px;

		.article-list-ctn {
			padding-top: 60px;

			@media (max-width: 768px) {
				padding-top: 40px;
			}
		}

		@media (max-width: 768px) {
			padding-top: 0px;
		}
	}

	.small-link {
		padding-right: 0px;
	}

	.page-title {
		text-transform: uppercase;
		color: $secondaryMainColor;
	}

	.cpn-head {
		display: flex;
		justify-content: space-between;
		align-items: center;

		&.-center{
			justify-content: center;
		}

		.title {
			text-transform: uppercase;
			color: $secondaryMainColor;
		}
	}

	.article-list-ctn {
		padding-top: 20px;
		display: flex;
		flex-flow: row wrap;
		align-items: strech;

		&.-full {
			.article {
				width: 100%;
				margin-right: 0;
			}
		}
	}

	.article {
		position: relative;
		margin-right: 20px;
		margin-bottom: 20px;
		width: calc(100% * 1/3 - 14px);
		background: white;
		box-shadow: 0 5px 15px 0 rgba(0,0,0,0.08);
		flex-grow: 1;
		min-height: 350px;

		&:only-of-type{
			flex-grow: 0;
		}

		&:nth-child(3n), &:last-child{
			margin-right: 0;
		}

		&:nth-child(n+4){
			flex-grow: 0;
		}

		&:hover {
			.filter {
				opacity: 1;
			}
		}

		.filter {
			transition: $primaryAnimation;
			position: absolute;
			background-color: rgba(0, 0, 0, 0.5);


			&:after{
				content: "";
				position: absolute;
				background-image: url("/assets/images/SVG/icons/more.svg");
				width: 50px;
				height: 50px;
				top: 50%;
				left: 50%;
				transform: translate(-50%,-50%);
				background-repeat: no-repeat;
			}
		}

		.img-ctn {
			position: relative;
			display: block;
			border-radius: 2px;
		}

		.flex-wrap {
			display: flex;
			align-items: center;
			flex-wrap: wrap;
			height: 100%;

			.img-ctn {
				padding-left: 0;
				height: 100%;

				img {
					width: 100%;
					height: 100%;
					object-fit: cover;
				}

				@media (max-width: 990px){
					padding-right: 0;
				}
			}
		}

		.article-info {
			position: relative;
			padding: 30px;
			padding-bottom: 45px;

			p {
				margin: 0;
				font-family: $secondaryFont;
			}

			.title {
				margin-top: 0;
				margin-bottom: 10px;
				font-weight: 500;

				> a {
					color: $secondaryMainColor;
					text-transform: uppercase;
				}
			}

			.date {
				margin-bottom: 15px;
				font-size: $blogListDateSize;
				line-height: $blogListDateLineHeight;
				color: $blogListDateColor;
				font-weight: 700;
			}

			.description {
				font-size: $blogListDescriptionSize;
				line-height: $blogListDescriptionLineHeight;
				color: $blogListDescriptionColor;
			}
		}

		@media (max-width: 768px) {
			width: 100%;
			margin-right: 0;
		}

		@media (max-width: 992px) {
			.article-info {
				.title {
					font-size: 18px;
					line-height: 23px;
				}
			}
		}
	}

	.blog-button {
		position: absolute;
		right: 0;
		bottom: 0;
		height: 50px;
		width: 50px;
		background: $primaryColor;
		color: $white;
		display: flex;
		align-items: center;
		justify-content: center;
		@include transition(all 0.3s ease-in);

		&:hover {
			background: $secondaryMainColor;
		}
	}
}
