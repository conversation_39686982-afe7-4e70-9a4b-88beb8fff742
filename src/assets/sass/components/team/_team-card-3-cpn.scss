/*
	Team Card 3 Component
*/

.team-card-3-cpn {
	padding-top: 100px;
	padding-bottom: 120px;
	display: flex;
	flex-flow: row wrap;
	@include clearfix;

	@media (max-width: 768px) {
		padding-top: 50px;
		padding-bottom: 60px
	}

	.team-img {
		img {
			max-width: 100%;
		}

		@media (max-width: 768px) {
			order: 1;
			margin-bottom: 20px;
		}
	}

	.team-card-details {
		@media (max-width: 768px) {
			order: 2;
		}
	}

	.name {
		margin-top: 0;
		margin-bottom: 15px;
	}

	.role {
		margin-top: 0px;
		margin-bottom: 35px;
		color: $secondaryMainColor;
		font-family: $secondaryFont;
		font-size: $detailTextSize;
		line-height: $detailTextLineHeight;
	}

	.team-card-text {
		color: $secondaryMainColor;
		font-family: $secondaryFont;
		font-size: $subTextSize;
		line-height: $subTextLineHeight;
	}

	.main-button {
		@media (max-width: 768px) {
			display: block;
		}
	}
}
