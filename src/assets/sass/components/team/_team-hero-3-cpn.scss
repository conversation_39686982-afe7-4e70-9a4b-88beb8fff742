/*
	Team Hero 3 Component
*/

.team-hero-3-cpn {
	padding-top: 50px;
	background: url("/assets/images/placeholder/ban-equipe.png");
	color: $secondaryMainColor;

	p {
		font-family: $secondaryFont;
		font-size: $subTextSize;
		line-height: $subTextLineHeight;
	}

	.page-title {
		margin-top: 0;
		color: #000000;
	}

	.team-hero {
		display: flex;
		align-items: center;

		@media (max-width: 992px) {
			flex-direction: column;

			.team-hero-content {
				width: 100%;
			}
		}
	}
	ul {
		padding-left: 0px;
		padding-bottom: 30px;

		li {
			position: relative;
			padding: 16px 0px 16px 40px;
			color: $listColor;
			font-family: $secondaryFont;
			font-size: $listSize;
			line-height: $listLineHeight;
			border-bottom: $listBorderBottom;

			&:before {
				color: $primaryColor;
				font-family: 'icomoon';
				speak: none;
				font-style: normal;
				font-weight: normal;
				font-variant: normal;
				text-transform: none;
				line-height: 1;
				-webkit-font-smoothing: antialiased;
				-moz-osx-font-smoothing: grayscale;
				content: "\e90c";
				position: absolute;
				left: 0;
				top: 22px;
				//transform: translate(0%, -50%);
			}
		}
	}
}

