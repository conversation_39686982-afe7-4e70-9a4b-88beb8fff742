/*
	Team Hero 2 Component
*/

.team-hero-2-cpn {
	padding-top: 60px;

	.page-title {
		margin-top: 0;
		text-transform: uppercase;
		color: $primaryColor;
	}

	@media (max-width: 768px) {
		.img-ctn {
			padding-top: 20px;
		}
	}

	.description {
		position: relative;
		max-width: 530px;

		&.-opened {
			.text-ctn {
				max-height: 5000px;
			}

			.small-link {
				display: none;
			}

			.gradient {
				opacity: 0;
			}
		}

		.text-ctn {
			//max-height: 210px;
			//overflow: hidden;
			transition: $primaryAnimation;

			p {
				margin-top: 0;
				color: $secondaryMainColor;
				font-family: $secondaryFont;
				font-size: $subTextSize;
				line-height: 30px;
			}

			a {
				color: $secondaryMainColor;
				font-weight: 500;
			}
		}

		.small-link {
			display: block;
			margin-top: 10px;
			font-size: $detailTextSize;
			text-transform: none;
		}

		.gradient {
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			height: 113px;
			width: 100%;
			background: linear-gradient(180deg, rgba(255,255,255,0) 0%, #FFFFFF 100%);
			transition: $primaryAnimation;
		}
	}

	ul {
		padding-left: 0px;
		padding-bottom: 30px;

		li {
			position: relative;
			padding: 16px 0px 16px 40px;
			color: $listColor;
			font-family: $secondaryFont;
			font-size: $listSize;
			line-height: $listLineHeight;
			border-bottom: $listBorderBottom;

			&:before {
				color: $primaryColor;
				font-family: 'icomoon';
				speak: none;
				font-style: normal;
				font-weight: normal;
				font-variant: normal;
				text-transform: none;
				line-height: 1;
				-webkit-font-smoothing: antialiased;
				-moz-osx-font-smoothing: grayscale;
				content: "\e90d";
				position: absolute;
				left: 0;
				top: 22px;
				//transform: translate(0%, -50%);
			}
		}
	}
}



