/*
Team Hero 5 Component
*/

.team-hero-5-cpn {
	padding: 60px 0;

	.page-title {
		margin-top: 0;
	}

	.team-card-4-cpn {
		margin-top: 30px;
	}

	.team-card-text{
		color: #666;
		font-family: $secondaryFont;
		font-size: 16px;
		line-height: 30px;
	}

	@media (max-width: 768px) {
		.team-card-details {
			order: 2;
		}

		.team-img {
			order: 1;
		}
	}

	ul {
		padding-left: 0px;
		padding-bottom: 30px;

		li {
			position: relative;
			padding: 16px 0px 16px 40px;
			color: $listColor;
			font-family: $secondaryFont;
			font-size: $listSize;
			line-height: $listLineHeight;
			border-bottom: $listBorderBottom;

			&:before {
				color: $primaryColor;
				font-family: 'icomoon';
				speak: none;
				font-style: normal;
				font-weight: normal;
				font-variant: normal;
				text-transform: none;
				line-height: 1;
				-webkit-font-smoothing: antialiased;
				-moz-osx-font-smoothing: grayscale;
				content: "\e90c";
				position: absolute;
				left: 0;
				top: 22px;
			}
		}
	}
}
