/*
	Awards Slider Component
*/

$awardsSliderBG: #F6F6F6;

.awards-slider-cpn {
	padding: 80px 0px;
	background-color: $awardsSliderBG;

	.title {
		margin:         0 0 30px;
		text-transform: uppercase;
		color: $primaryColor;
	}

	.swiper-slide {
		margin-bottom: 4px;
		height: auto;
	}

	.nav-ctn {
		position: absolute;
		top: 0;
		right: 0;

		@media (max-width: 768px) {
			right: 5px;
		}
	}

	.swiper-btn {
		position: relative;
		display: inline-block;
		height: 40px;
		width: 40px;
		top: inherit;
		left: 0;
		right: 0;
		margin-top: 0;
		border-radius: 50%;
		background-color: $primaryColor;
		box-shadow: 0 2px 4px 0 rgba(0,0,0,0.1);
		background-image: none;

		&:before {
			position: absolute;
			font-family: 'icomoon' !important;
			speak: none;
			font-style: normal;
			font-weight: normal;
			font-variant: normal;
			text-transform: none;
			line-height: 1;
			font-size: 20px;
			color: white;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);;
		}

		&:focus{
			outline: none;
		}
	}

	.swiper-button-prev {
		margin-right: 10px;

		@media (max-width: 768px) {
			margin-right: 5px;
		}

		&:before {
			content: "\e909";
		}
	}

	.swiper-button-next {
		&:before {
			content: "\e90a";
		}
	}
}

