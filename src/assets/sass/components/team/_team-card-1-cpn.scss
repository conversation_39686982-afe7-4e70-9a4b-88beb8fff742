/*
	Team Card 1 Component
*/

.team-card-cpn {
	@media (max-width: 992px) {
		display: flex;
    	flex-flow: row wrap;
	}
}

.team-card-1 {
	padding: 25px 20px 20px 20px;
	background-color: #F2F2F2;
	margin-bottom: 30px;
	box-sizing: border-box;

	@media (max-width: 992px) {
		width: calc(100% * 1/2 - 10px);
		margin-right: 20px;

		&:nth-child(2n){
			margin-right: 0;
		}
	}

	@media (max-width: 768px) {
		width: 100%;
		margin-right: 0;
	}

	.card-content {
		padding-bottom: 20px;
		display: flex;
		align-items: center;
		flex-wrap: wrap;
	}

	.img-ctn {
		width: 100px;
		height: 100px;
		border-radius: 50%;
		overflow: hidden;

		@media (max-width: 1200px) {
			width: 75px;
			height: 75px;
		}
	}

	.info-ctn {
		margin-left: 20px;
		width: calc(100% - 120px);

		@media (max-width: 1200px) {
			width: calc(100% - 95px);
		}

		p {
			margin: 0;
		}

		.name {
			padding-bottom: 5px;
			color: $secondaryMainColor;
			font-family: $primaryFont;
			font-size: 18px;
			font-style: normal;
			font-weight: 500;
			line-height: 24px;
			text-transform: uppercase;
		}

		.role {
			padding-bottom: 10px;
			font-family: $secondaryFont;
			color: $secondaryMainColor;
			font-size: 14px;
			font-style: normal;
			font-weight: 700;
			line-height: 24px;
		}

		.number {
			display: block;
			margin-bottom: 5px;
			color: $secondaryMainColor;
			font-family: $primaryFont;
			font-size: 16px;
			font-style: normal;
			font-weight: 600;
			line-height: normal;

			i {
				margin-right: 12px;
			}
		}
	}

	.main-button {
		width: 100%;
		min-width: 0;
		color: $white;
		text-align: center;
		font-family: $primaryFont;
		font-size: 14px;
		font-style: normal;
		font-weight: 700;
		line-height: normal;
		text-transform: uppercase;
		background-color: $secondaryMainColor;

		&:hover {
			background-color: $white;
			color: $secondaryMainColor;
		}
	}
}
