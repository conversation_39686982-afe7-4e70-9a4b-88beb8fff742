/*
	Award Card Component
	*/

.award-card-cpn {
	text-align: center;
	background: white;
	box-shadow: 0 2px 4px 0 rgba(0,0,0,0.1);
	width: calc(100% - 2px);
	height: 100%;

	&.-invisible {
		.swiper-container {
			opacity: 0;
		}
	}

	.swiper-container {
		transition: opacity 0.5s ease;
	}

	/* 	@media (max-width: 992px) {
            padding: 40px 25px 25px 25px;
        } */

	a {
		padding: 50px 35px 35px 35px;
		display: block;
		height: 100%;
	}

	p {
		margin-top: 40px;
		margin-bottom: 0;
		color: $secondaryMainColor;
		font-family: $primaryFont;
		font-size: $primaryTextSize;
		font-weight: 500;
		line-height: $detailTextLineHeight;
		text-transform: uppercase;
		text-align: center;
	}
}
