/*
	Team Card 4 Component
*/

.team-card-4-cpn {
	margin-top: 80px;
	padding-bottom: 80px;
	padding-left: 0px;
	padding-right: 0px;
	display: flex;
	flex-flow: row wrap;
	@include clearfix;

	.team-img {
		@media (max-width: 768px) {
			margin-bottom: 20px;
		}
		img{
			width: 100%;
		}
	}

	.name {
		margin-top: 0;
		margin-bottom: 15px;
	}

	.role {
		margin-top: 0px;
		margin-bottom: 35px;
		color: $secondaryMainColor;
		font-family: $secondaryFont;
		font-size: $detailTextSize;
		line-height: $detailTextLineHeight;
	}

	.team-card-text {
		color: $secondaryMainColor;
		font-family: $secondaryFont;
		font-size: $detailTextSize;
		line-height: $detailTextLineHeight;
	}

	.main-button {
		@media (max-width: 768px) {
			display: block;
		}
	}
}
