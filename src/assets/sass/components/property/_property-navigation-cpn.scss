/*
	Property Navigation Component
*/

.property-navigation-cpn {
	border-top: 1px solid #DDDDDD;
	display: flex;
	align-items: center;
	overflow: hidden;

	.previous-ctn, .next-ctn {
		margin: 0;
		width: 50%;
		display: flex;
		align-items: center;
	}

	.next-ctn {
		.property-nav-ctn {
			border-left: 1px solid #CCCCCC;
			justify-content: flex-end;

			.text-ctn {
				padding-left: 0;
				padding-right: 40px;
				text-align: right;

				@media (max-width: 768px) {
					padding-right: 20px;
				}
			}
		}

		.-right-nav {
			float: right;
		}
	}

	.property-nav-ctn {
		position: relative;
		width: 100%;

		&:hover {
			cursor: pointer;
			.arrow-ctn {
				opacity: 1;

				&.-left-arrow {
					left: 0px;
				}

				&.-right-arrow {
					right: 0px;
				}
			}

			.nav-info {
				&.-left-nav {
					left: 40px;

					@media (max-width: 768px) {
						left: 0px;
					}
				}
				&.-right-nav {
					right: 40px;

					@media (max-width: 768px) {
						right: 0px;
					}
				}
			}
		}

		.arrow-ctn {
			z-index: 4;
			position: absolute;
			width: 40px;
			height: 100%;
			background: $primaryColor;
			color: $white;
			display: flex;
			align-items: center;
			justify-content: center;
			opacity: 0;
			transition: all 275ms cubic-bezier(0.420, 0.000, 1.000, 1.000);

			@media (max-width: 768px) {
				display: none;
			}

			i {
				font-size: 15px;
			}

			&.-left-arrow {
				left: -40px;
			}

			&.-right-arrow {
				right: -40px;
			}
		}

		.nav-info {
			position: relative;
			display: flex;
			align-items: center;
			transition: all 275ms cubic-bezier(0.420, 0.000, 1.000, 1.000);

			&.-left-nav {
				left: 0px;
			}
			&.-right-nav {
				right: 0px;
			}
		}

		.img-ctn {
			position: relative;
			height: 160px;
			width: 160px;
			overflow: hidden;

			@media (max-width: 768px) {
				display: none;
			}

			img {
				position: absolute;
				height: 100%;
				max-width: none;
				left: 50%;
				transform: translate(-50%, 0%);
        object-fit: cover;
			}
		}

		.text-ctn {
			padding-left: 40px;

			@media (max-width: 768px) {
				padding-top: 30px;
				padding-bottom: 30px;
				padding-left: 20px;
			}

			p {
				margin: 0;
			}

			.title {
				margin-bottom: 10px;
				color: $primaryColor;
				font-family: $primaryFont;
				font-size: 24px;
				font-weight: 600 ;
				line-height: 22px;
				text-transform: uppercase;

				@media (max-width: 768px) {
					font-size: $subTextSize;
					font-weight: 600;
					line-height: 22px;
					margin-bottom: 6px;
				}
			}

			.location {
				color: $secondaryMainColor;
				font-family: $secondaryFont;
				font-size: 16px;
				line-height: 24px;
			}
		}
	}
}
