.properties-type-choice-cpn {
  padding-bottom: 100px;

  .grid-ctn {
    padding: 110px 0 55px;
    margin:  auto 45px;

    @media (max-width: 768px) {
      padding: 60px 20px;
      margin: auto;
    }

    .column {
      // 3 columns
      width:           100%;
      display:         grid;
        grid-template-columns: repeat(3, 1fr);
        grid-gap: 25px;

      @media (max-width: 768px) {
        display: block;
      }
    }

    .cta-bloc {
      position: relative;

      @media (max-width: 768px) {
        margin-bottom: 20px;
      }

      &:hover {
        cursor: pointer;

        .filter {
          opacity: 1;
        }
      }

      .image-ctn {
        position:   relative;
        transform:  scale(1) rotateY(0deg);
        transition: all 1.25s cubic-bezier(.475, .425, 0, .995);
        overflow:   hidden;
        height:     100%;
        min-height: 360px;
        max-height: 360px;

        .property-img {
          width:      100%;
          height:     100%;
          min-height: 360px;
          object-fit: cover;
          transform:  scale(1);
          display:    block;
          transition: transform 1.25s cubic-bezier(.475, .425, 0, .995);
        }

        &:before {
          content:    "";
          position:   absolute;
          width:      100%;
          height:     50%;
          //background-color: rgba($color: #000000, $alpha: 0.3);
          background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.8) 100%);
          z-index:    1;
          bottom:     0;
          transition: all 1.25s cubic-bezier(.475, .425, 0, .995);
        }

        .abs-img {
          position: absolute;
          width:    160px;
          top:      50px;
          right:    40px;
          z-index:  3;

          img {
            transition: transform 1.25s cubic-bezier(.475, .425, 0, .995);
          }
        }
      }

      .info {
        width:      100%;
        padding: 30px 0;
        text-align: center;

        .main-cta {
          color:          $secondaryMainColor;
          font-size:      24px;
          text-transform: uppercase;
          font-family:    $primaryFont;
          display:        inline-block;
          font-style: normal;
          font-weight: 700;
          line-height: normal;
          margin-bottom: 15px;

          span.icon {
            vertical-align: middle;
            font-size:      17px;
            line-height:    18px;
            padding-left:   10px;
          }
        }

        .property-info {
          margin:      5px 0 0;
          color:       $secondaryMainColor;
          text-align: center;
          font-family: $secondaryFont;
          font-size: 15px;
          font-style: normal;
          font-weight: 500;
          line-height: 21px;

          .municipality {
            display:      inline-block;
            padding-left: 20px;
            position:     relative;

            &:before {
              content:          "";
              position:         absolute;
              left:             5px;
              top:              50%;
              transform:        translateY(-50%);
              width:            10px;
              height:           1px;
              background-color: white;
            }
          }
        }
      }

      &.openhouse-bloc .image-ctn .property-img {
        object-position: 0 -80px;
      }

      .filter {
        transition: $primaryAnimation;
        position: absolute;
        background-color: rgba(0, 0, 0, 0.5);


        &:after{
          content: "";
          position: absolute;
          background-image: url("/assets/images/SVG/icons/more.svg");
          width: 50px;
          height: 50px;
          top: 50%;
          left: 50%;
          transform: translate(-50%,-50%);
          background-repeat: no-repeat;
        }
      }
    }

    &.no-open-house {
      .column:last-child {

        .cta-bloc {
          height: 100%;

          .image-ctn {
            max-height: 500px;
            min-height: 0;

            .property-img {
              object-position: inherit !important;
            }
          }
        }
      }
    }

    @media (max-width: 1300px) {
      .cta-bloc.openhouse-bloc .image-ctn .property-img {
        object-position: inherit;
      }
    }

    @media (max-width: 1080px) {
      .cta-bloc {
        .info {
          display: block;
          padding: 20px 30px;

          .main-cta {
            font-size:     20px;
            line-height:   26px;
            margin-bottom: 8px;
          }

          .property-info {
            font-size:   14px;
            line-height: 17px;
          }
        }

        &:hover {
          .info {
            width:  90%;
            bottom: 10px;
          }
        }
      }
    }

    @media (max-width: 425px) {
      .cta-bloc {
        width:      100%;
        max-height: 350px;

        .image-ctn {
          .abs-img {
            width: 120px;
            top:   25px;
            right: 25px;
          }

        }

        .info {
          padding: 10px 20px 20px;

          .main-cta {
            font-size:     18px;
            line-height:   21px;
            margin-bottom: 0;
          }

          .property-info {
            margin-top: 10px;

          }
        }

        &.openhouse-bloc {
          .info {
            .main-cta {
              margin-bottom: 20px;
            }
          }
        }
      }
    }
  }
}
