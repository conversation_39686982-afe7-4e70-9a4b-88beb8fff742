/*
	Properties List
*/

$propertiesListBG: $white;

.properties-list-cpn {
  padding-top:      50px;
  padding-bottom:   60px;
  background-color: $propertiesListBG;

  &.-grey {
    background-color: #F2F2F2;
  }

  .cpn-head {
    display:         flex;
    justify-content: space-between;
    align-items:     center;

    .title {
      text-transform: uppercase;
      color:          $primaryColor;
    }
  }
}

.properties-menu {
  display:         flex;
  align-items:     center;
  justify-content: space-between;
}

.properties-list-ctn {
  display:     flex;
  flex-flow:   row wrap;

  @media (min-width: 768px) {
    padding-bottom: 20px;
  }

  .small-link {
    padding-right: 0px;
  }
}

.properties {
  position:      relative;
  margin-right:  20px;
  margin-bottom: 20px;
  width:         calc(100% * 1 / 3 - 14px);
  overflow:      hidden;
  height: 240px;

  //If Desktop apply this
  @media (min-width: 992px) {
    &:nth-child(3n) {
      margin-right: 0;
    }
  }

  @media (max-width: 992px) {
    width: calc(100% * 1 / 2 - 10px);

    &:nth-child(2n) {
      margin-right: 0;
    }
  }

  @media (max-width: 768px) {
    width:        100%;
    margin-right: 0 !important;
  }

  .img-ctn {
    border-radius: 2px;

    img {
      width:      100%;
      height: 100%;
      object-fit: cover;
    }
  }

  &:hover {
    .filter {
      background-color: rgba(0, 0, 0, 0.3);
      opacity:          1;
    }

    .properties-info {
      max-height: 115px;
      //transform: translateY(0%);

      .more-info {
        max-height: 250px;
        opacity:    1;
      }
    }
  }

  .gradiant {
    position:   absolute;
    height:     50%;
    width:      100%;
    left:       0;
    right:      0;
    bottom:     0;
    top:        50%;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #000000 100%);
  }

  .filter {
    position:            absolute;
    height:              100%;
    width:               100%;
    left:                0;
    right:               0;
    top:                 0;
    bottom:              0;
    transition:          $propertyAnimation;
    transition-duration: 0.3s;
    background-color:    rgba(0, 0, 0, 0);

    @media (max-width: 992px) {
      opacity:          0;
      background-color: rgba(0, 0, 0, 0.3);
    }
  }

  .icon-tab {
    position:        absolute;
    top:             10px;
    right:           10px;
    display:         flex;
    align-items:     center;
    justify-content: center;
  }

  .play-btn {
    position:         relative;
    margin-left:      10px;
    width:            35px;
    height:           35px;
    border-radius:    100%;
    background-color: rgba(0, 0, 0, 0.5);
    cursor:           pointer;
    color:            white;
    font-size:        20px;
    display:          flex;
    justify-content:  center;
    align-items:      center;

    .icon-play {
      width: 16px;
    }
  }

  .player360 {
    position:         relative;
    margin-left:      10px;
    width:            35px;
    height:           35px;
    border-radius:    100%;
    background-color: rgba(0, 0, 0, 0.5);
    cursor:           pointer;
    color:            white;
    font-size:        20px;
    display:          flex;
    justify-content:  center;
    align-items:      center;

    a {
      color: white;
    }
  }

  .properties-info {
    position:   absolute;
    z-index:    4;
    bottom:     0;
    left:       0;
    right:      0;
    transition: $propertyAnimation;
    max-height: 200px;

    @media (max-width: 992px) {
      max-height: 250px !important;
    }

    p {
      margin:      0;
      color:       white;
      font-family: $secondaryFont;
    }

    .bloc-head {
      padding:         0 20px;
      padding-bottom:  15px;
      display:         flex;
      justify-content: space-between;
      align-items:     center;

      .price {
        font-size:   20px;
        font-weight: bold;
        line-height: 27px;
        white-space: nowrap;
        font-family: $primaryFont;
      }

      .type {
        font-size:   13px;
        font-weight: 600;
        line-height: 18px;
        text-align:  right;
        font-family: $primaryFont;
      }
    }

    .more-info {
      position:   relative;
      top:        -15px;
      padding:    0 20px;
      overflow:   hidden;
      max-height: 0;
      text-align: left;
      transition: $propertyAnimation;
      opacity:    0;

      @media (max-width: 992px) {
        max-height: 250px;
        opacity:    1;
      }

      .address, .location {
        font-size: 14px;
        color:     white;
      }

      .align {
        display:         flex;
        justify-content: space-between;
        align-items:     center;

        .numbers {
          display: flex;

          .icon-ctn {
            padding-left: 32px;
            display:      flex;
            align-items:  center;

            i {
              font-size: $primaryTextSize;
              color:     white;
            }

            p {
              padding-left: 12px;
              font-size:    $detailTextSize;
              font-weight:  600;
              line-height:  19px;
            }
          }
        }
      }
    }
  }
}

.containerBandeau {
  position: absolute;
  z-index: 5;
  top: 15px;
  left: 15px;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.properties-label {
  border-radius:  3px;
  color:          $white;
  font-family:    $primaryFont;
  margin-right:   5px;
  padding:        5px 24px;
  @include fontSize($detailTextSize, 19px, 600);
  text-transform: uppercase;
  width: fit-content;

  &.-label-view {
    padding:     4px 12px;
    background:  $primaryColor;
    font-size:   13px;
    font-weight: 600;
    line-height: 18px;
  }

  &.-label-sold {
    padding:        5px 24px;
    font-size:      $detailTextSize;
    font-weight:    bold;
    line-height:    19px;
    background:     #DB1C2E;
    text-transform: uppercase;
  }

  &.-label-collection {
    padding:        5px 24px;
    font-size:      $detailTextSize;
    font-weight:    bold;
    line-height:    19px;
    background:     black;
    text-transform: uppercase;
  }

  &.-label-new {
    background: $secondaryMainColor;
  }
}
