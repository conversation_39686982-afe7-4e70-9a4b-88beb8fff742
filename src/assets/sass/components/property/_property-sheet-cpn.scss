/*
	Property Sheet Component
*/
$propertySheetTableBGOdd: #F2F2F2;
$propertySheetTableBGEven: white;
$propertySheetTableTextSize: 14px;
$propertySheetTableTextLineHeight: 19px;
$propertySheetTableTextColor: $secondaryMainColor;
$propertySheetTableTextEmphaseColor: $secondaryMainColor;

$propertySheetSpacing: 60px;

.property-sheet {
	padding-top: $propertySheetSpacing;
	@include clearfix;

	@media (max-width: 768px) {
		padding-top: 44px;
	}

	.emphase-title {
		margin-top: $propertySheetSpacing;
		margin-bottom: 40px;
	}

	.property-sheet-content {
		padding-left: 0;

		@media (max-width: 992px) {
			width: 100%;
			padding: 0;
		}
	}

	.property-mobile-sidebar {
		margin-top: 40px;
		display: none;

		@media (max-width: 992px) {
			display: block;
		}
	}

	.property-sheet-sidebar {
		padding-right: 0;
		position: sticky;
		top: 30px;
		margin-bottom: 30px;

		@media (max-width: 992px) {
			display: none;
		}
	}

	.table-ctn-dual {
		display: flex;
		align-items: flex-start;
		justify-content: space-between;
		flex-wrap: wrap;

		@media (max-width: 1200px) {
			align-items: flex-start;
		}
	}

	.table-ctn {
		.table-row {
			padding: 10px 20px;
			display: flex;
			align-items: flex-start;

			&:nth-child(odd) {
				background-color: $propertySheetTableBGOdd;
			}

			&:nth-child(even) {
				background-color: $propertySheetTableBGEven;
			}

			> p {
				padding: 0;
				margin-top: 0;
				margin-bottom: 0;
				color: $propertySheetTableTextColor;
				font-family: $secondaryFont;
				font-size: $propertySheetTableTextSize;
				line-height: $propertySheetTableTextLineHeight;

				&:last-child {
					text-align: right;
				}

				&.date {
					text-align: left;
					display: flex;
    				align-items: center;

    				i {
    					font-size: 20px;
    				}

					span {
						margin-left: 15px;
						display: inline-block;
					}
				}
			}

			span {
				display: block;
				color: $propertySheetTableTextEmphaseColor;
				font-family: $secondaryFont;
				font-size: $propertySheetTableTextSize;
				font-weight: 700;
				line-height: $propertySheetTableTextLineHeight;
			}
		}

		&.-dual {
			margin-right: 20px;
			width: calc(100% * 1/2 - 10px);

			@media (max-width: 768px) {
				width: 100%;
				margin-right: 0;
			}

			&:last-child {
				margin-right: 0;

				@media (max-width: 768px) {
					.table-row {
						&:nth-child(odd) {
							background-color: $propertySheetTableBGEven;
						}

						&:nth-child(even) {
							background-color: $propertySheetTableBGOdd;
						}
					}
				}
			}
		}

		&.-between {
			.table-row {
				justify-content: space-between;
			}
		}

		&.-has-button {
			position: relative;

			.table-wrap {
				max-height: 300px;
				overflow: hidden;
				transition: $primaryAnimation;
			}

			.gradient {
				position: absolute;
				bottom: 0;
				left: 0;
				right: 0;
				height: 113px;
				width: 100%;
				background: linear-gradient(180deg, rgba(255,255,255,0) 0%, #FFFFFF 100%);
				transition: $primaryAnimation;
			}

			.small-link {
				margin-top: 22px;
				display: block;
				transition: $primaryAnimation;

				i {
					padding: 5px;
					position: relative;
					top: -2px;

          &::before {
            position: relative;
            top: 3px;
            content: '+';
            font-size: 32px;
            font-weight: 400;
          }
				}
			}
		}

		&.-opened {
			.table-wrap {
				max-height: 5000px;
			}

			.gradient {
				opacity: 0;
			}

			.small-link {
				opacity: 0;
			}
		}

		&.-center {
			.table-row {
				align-items: center;
			}
		}
	}

	.text-wrap {
		margin-bottom: 20px;
		color: $secondaryMainColor;
		font-family: $secondaryFont;
		font-size: $detailTextSize;
		line-height: $detailTextLineHeight;
	}

	.dual-wrap {
		display: flex;
		align-items: flex-start;
		justify-content: space-between;
		flex-wrap: wrap;

		@media (max-width: 768px) {
			display: block;
		}

		.dual-div {
			margin-right: 20px;
			width: calc(100% * 1/2 - 10px);

			@media (max-width: 768px) {
				width: 100%;
			}

			&:last-child {
				margin-right: 0;
			}
		}
	}
}

.propertygroup-sheet {
	padding-bottom: 0px;

	.propertygroup-description {
		.title {
			margin-top: 0;
			margin-bottom: 30px;
		}

		.subtext {
			color: $secondaryMainColor;
			font-family: $secondaryFont;
			font-size: $subTextSize;
			line-height: $subTextLineHeight;
		}
	}

	.property-sheet-sidebar {
		position: relative;
		top: 0;
	}
}
