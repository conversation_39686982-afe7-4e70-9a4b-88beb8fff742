
$propertyStatisticsTitleBgColor: $secondaryMainColor;
$propertyStatisticsTitleColor: $white;

$propertyStatisticsTableRowBg: #F2F2F2;
$propertyStatisticsTableRowColor: $secondaryMainColor;
$propertyStatisticsTableRowBorder: solid 1px #dddddd;
$propertyStatisticsTableRowLabelColor: $secondaryMainColor;
$propertyStatisticsTableRowLabelSize: 14px;
$propertyStatisticsTableRowLabelHeight: 19px;

$propertyStatisticsEvaluationBG: $paleBackground;

.property-statistics-cpn{
    padding-top: 70px;
    border-top: solid 1px #E7E7E7;
    margin-top: 60px;

    .title-bg{
        margin-top: 0;
        display: inline-block;
        text-transform: uppercase;
        font-weight: 700;
        padding: 15px 30px;
        background-color: $propertyStatisticsTitleBgColor;
        color: $propertyStatisticsTitleColor;
    }

    .table-ctn{
        margin-bottom: 30px;
        .table-row {
            margin: 0;
			padding: 10px 20px;
			display: flex;
            align-items: flex-start;
            justify-content: space-between;
            border-top: $propertyStatisticsTableRowBorder;

            &:nth-child(2){
                border: 0;
            }

            &.-head{
                background-color: $propertyStatisticsTableRowBg;
                border: 0;

                & > p{
                    color: $propertyStatisticsTableRowColor;
                    font-size: 18px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: normal;
                }

            }

            &.-mutation{
                flex-wrap: wrap;
                border: 0;

                div{
                    width: 100%;
                    margin-top: 15px;

                    p{
                        color: $propertyStatisticsTableRowLabelColor;
                        font-size: 14px;
                        line-height: 20px;
                        margin: 0;
                    }

                    a {
                        color: $primaryColor;
                        transition: all ease 0.2s;

                        &:hover {
                            opacity: 0.7;
                        }
                    }
                }
            }

            > p {
				padding: 0;
				margin-top: 0;
                margin-bottom: 0;
                font-family: $secondaryFont;
                color: $propertyStatisticsTableRowLabelColor;
                font-size: 15px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px;

				&:last-child {
					text-align: right;
                }

                span{
                    color: $secondaryMainColor;
                    font-family: $primaryFont;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 700;
                    line-height: normal;

                    &.icon{
                        font-weight: 400;
                        color: #A2A5AA;
                        margin-left: 7px;
                        vertical-align: middle;
                    }
                }
			}
        }
    }

    .form-container{
        background-color: #F2F2F2;
        padding: 50px 30px;

        .form-row {
            margin-bottom: 40px;
        }

        form {
            .input-ctn{
                label{
                    color: $secondaryMainColor;
                    font-size: 16px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 24px;
                }
            }

            .ng-select.ng-select-single .ng-select-container{
                height: $inputHeight;
            }
        }

        .value-ctn{
            padding-top: 20px;
            border-top: $propertyStatisticsTableRowBorder;
            display: flex;
            justify-content: space-between;
            align-items: center;

            p{
                color: $secondaryMainColor;
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px;
                margin:0;

                &.value{
                    font-family: $primaryFont;
                    font-size: 22px;
                    font-style: normal;
                    font-weight: 700;
                    line-height: normal;
                }
            }
        }
    }

    @media (max-width:992px) {
        .title-bg{
            font-size: 18px;
            line-height: 24px;
        }

        .table-ctn{
            .table-row {
                &.-head{
                    p{
                        font-size: 16px;
                        line-height: 26px;
                    }
                }
            }
        }

        .form-container{
            form {
                .input-ctn{
                    label{
                        font-size: 15px;
                        line-height: 24px;
                    }
                }
            }
        }
    }

    @media (max-width:425px) {
        padding-top: 50px;
        margin-top: 40px;
    }
}
