/*
	Location Pane Component
*/

.location-pane-cpn {
	position: absolute;
	margin: 0;
	z-index: 10;
	width: 400px;
	height: 100%;
	background-color: $white;
	box-shadow: 2px 0 4px 0 rgba(0,0,0,0.10);
	transition: $primaryAnimation;
	margin-left: -400px;
	display: flex;
	align-items:center;

	@media (max-width: 768px) {
		display: none;
	}

	.toggle-pane {
		position: absolute;
		top: 15px;
		right: -30px;
		display: block;
		width: 30px;
		line-height: 60px;
		text-align: center;
		background-color: $primaryColor;
		color: white;
		transition: $primaryAnimation;
		cursor:pointer;

		div {
			transform: rotate(180deg);
		}

		&:hover {
			background-color: rgba($primaryColor, .60);
		}
	}
	&.open {
		margin: 0;

		.toggle-pane {
			div {
				transform: rotate(0);
			}
		}
	}

	.pane-content-ctn {
		padding: 0px 60px;

		.pane-title {
			margin: 0;
			color: $secondaryMainColor;
			text-transform: uppercase;
		}

		.info-ctn {
			margin-top: 30px;
			padding-bottom: 30px;
			border-bottom: 1px solid #DCDCDC;

			&:last-child {
				padding-bottom: 0;
				border-bottom: none;
			}

			.sub-section-title {
				margin-top: 0;
				margin-bottom: 10px;
				color: $secondaryMainColor;
				font-family: $secondaryFont;
				font-size: $detailTextSize;
				line-height: 19px;
			}

			.address {
				margin-top: 0;
				margin-bottom: 15px;
				color: $secondaryMainColor;
				font-family: $secondaryFont;
				font-size: 18px;
				line-height: 28px;
			}

			.location {
				margin-top: 0;
				margin-bottom: 15px;
				color: $secondaryMainColor;
				font-family: $secondaryFont;
				font-size: 16px;
				line-height: 24px;
				font-weight: 600;
			}

			.direction {
				font-size: 14px;
				font-weight: 600;
				line-height: normal;
				text-transform: uppercase;
			}

			.description {
				margin-top: 0;
				margin-bottom: 15px;
				color: $secondaryMainColor;
				font-family: $secondaryFont;
				font-size: $detailTextSize;
				line-height: $detailTextLineHeight;
			}

			.small-link {
				font-weight: 600;
			}
		}
	}
}
