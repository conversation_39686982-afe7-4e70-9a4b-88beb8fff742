.properties-slider-cpn {
  background-color: $primaryColor;
  padding:          80px 50px 0;
  position:         relative;

  &.-pdb {
    padding-bottom: 60px;
  }

  @media (max-width: 992px) {
    padding: 40px 20px;
  }

  .header {
    display:         flex;
    align-items:     center;
    justify-content: space-between;
    margin-bottom:   50px;

    @media (max-width: 992px) {
      flex-direction: column;
      gap:            15px;
      margin-bottom:  20px;
    }

    h2 {
      margin:         0;
      color:          $white;
      text-transform: uppercase;
    }

    .right {
      display:     flex;
      align-items: center;
      gap:         15px;

      @media (max-width: 576px) {
        flex-direction: column;
      }

      .small-link {
        border:         2px solid rgba(255, 255, 255, 0.3);
        padding:        16px 20px;
        color:          $white;
        font-size:      14px;
        font-weight:    700;
        text-transform: uppercase;

        i {
          display: none;
        }

        &:hover {
          background-color: $white;
          color:            $primaryColor;
        }
      }

      .nav-ctn {
        display:     flex;
        align-items: center;
        gap:         15px;

        .main-button {
          width:           50px;
          height:          50px;
          border:          2px solid $white;
          display:         flex;
          align-items:     center;
          justify-content: center;

          i {
            font-size: 20px;
            color:     $white;
          }

          &:hover {
            background-color: $white;

            i {
              color: $primaryColor;
            }
          }
        }
      }
    }
  }

  .properties {
    height: 310px;

    .price {
      color:       $white;
      font-family: $primaryFont;
      font-size:   22px;
      font-weight: 600;
    }

    .type {
      color:       $white;
      font-size:   16px;
      font-weight: 500;
    }

    .img-ctn {
      height: 310px;
    }
  }
}
