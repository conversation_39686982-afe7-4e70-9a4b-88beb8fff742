/*
	Property Description Component
*/
$propertyDescriptionInfoSize: $primaryTextSize;
$propertyDescriptionInfoLineHeight: $primaryTextLineHeight;
$propertyDescriptionInfoColor: #666;

$propertyDescriptionInfoIconBorderColor: #dcdcdc;
$propertyDescriptionInfoIconSize: 18px;
$propertyDescriptionInfoIconLineHeight: 24px;

.property-description-cpn {
  .title {
    margin: 0px;
    color: $secondaryMainColor;
    text-transform: uppercase;
  }

  .location {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin: 25px 0 0;
    font-size: 17px;
    line-height: 28px;
    font-weight: 400;
    color: $propertyDescriptionInfoColor;

    .municipality{
      border-right: 1px solid $propertyDescriptionInfoColor;
      padding-right: 20px;
    }

    .mls {
      font-size: 15px;
      border-left: 1px solid $propertyDescriptionInfoColor;
      padding-left: 20px;
    }
  }

  .icon-info-ctn {
    margin-top: 40px;
    padding: 15px 0;
    border-top: 1px solid #dddddd;
    border-bottom: 1px solid #dddddd;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .icon-wrap {
      display: flex;
      align-items: center;
      color: $secondaryMainColor;

      i {
        font-size: 22px;
      }

      p {
        margin: 0;
        margin-left: 15px;
        font-family: $primaryFont;
        font-size: $propertyDescriptionInfoIconSize;
        line-height: $propertyDescriptionInfoIconLineHeight;
      }
    }
  }

  .description {
    margin-top: 40px;
    margin-bottom: 0;
    color: $secondaryMainColor;
    font-family: $primaryFont;
    font-size: $propertyDescriptionInfoSize;
    line-height: $primaryTextLineHeight;
  }

  @media (max-width: 768px) {
    .title {
      font-size: 32px;
      line-height: 39px;
    }

    .icon-info-ctn {
      padding-right: 0px;
      .icon-wrap { text-align: center; }
    }
  }
}
