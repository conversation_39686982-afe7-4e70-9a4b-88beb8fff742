/*
	Properties Featured
*/

$propertiesFeaturedBG: $primaryColor;

$propertyHeroButtonHeight: 80px;
$propertyHeroButtonWidth: 80px;
$propertyHeroButtonHeightMobile: 40px;
$propertyHeroButtonWidthMobile: 40px;
$propertyHeroRadius: 40px;
$propertyHeroLeftButtonPosition: 20px;
$propertyHeroLeftButtonPositionMobile: 15px;
$propertyHeroRightButtonPosition: 20px;
$propertyHeroRightButtonPositionMobile: 15px;
$propertyHeroSettingsBackground: rgba(0,0,0,0.5);
$propertyHeroSettingsColor: white;


.properties-featured-cpn{
    padding: 100px 0;
    background-color: $propertiesFeaturedBG;

    .row{
        align-items: center;
        display: flex;
        flex-wrap: wrap;
    }

    .title{
        color: white;
        text-transform: uppercase;

        .icon{
            display: block;
            color: $primaryColor;
            font-size: 60px;
            margin-bottom: 30px;
        }


    }

    .slider-container{
        @media (max-width:769px) {

            width:100%;
        }
    }

    .description{
        font-size: 15px;
        line-height: 24px;
        margin-bottom: 40px;
        color: white;
    }

    .small-link{
        color: white;
    }

    @media (max-width:992px) {

        .title{
            text-align: center;

            .icon{
                margin: 0 auto 30px;
            }
        }

        .description{
            text-align: center;
        }

        .small-link{
            display: inline-block;
            margin: 0 auto 40px;
            text-align: center;
        }

    }

    .swiper-btn{
        width: $propertyHeroButtonWidth;
        height: $propertyHeroButtonHeight;
        background: $propertyHeroSettingsBackground;
        border-radius: $propertyHeroRadius;
        transform: translate(0%, -50%);
        margin-top: 0;
        transition: all 0.4s ease;

        @media (max-width:992px) {
            width: $propertyHeroButtonWidthMobile;
            height: $propertyHeroButtonHeightMobile;
        }

        &:before {
            position: absolute;
            font-family: 'icomoon' !important;
            speak: none;
            font-style: normal;
            font-weight: normal;
            font-variant: normal;
            text-transform: none;
            line-height: 1;
            font-size: 28px;
            color: $propertyHeroSettingsColor;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);

            @media (max-width:992px) {
                font-size: 20px;
            }
        }

        &.hidden{
            display: none;
        }

        &.swiper-button-disabled{
            opacity: 0 !important;
        }

        &.swiper-button-prev {
            left: $propertyHeroLeftButtonPosition;

            @media (max-width:992px) {
                left: $propertyHeroLeftButtonPositionMobile;
            }

            &:before {
                content: "\e909";
            }
        }

        &.swiper-button-next {
            right: $propertyHeroRightButtonPosition;

            @media (max-width:992px) {
                right: $propertyHeroRightButtonPositionMobile;
            }

            &:before {
                content: "\e90a";
            }
        }
    }


    .property-featured-cpn{
        position: relative;

        .img-ctn{
            img{
                width: 100%;
            }
        }



        .properties-info{
            position: absolute;
            background-color: white;
            right: 0;
            bottom: 0;
            max-width: 70%;
            width: 100%;

            .bloc-head {
                padding: 0 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;

                .price {
                    font-size: 20px;
                    font-weight: bold;
                    line-height: 27px;
                    white-space: nowrap;
                    font-family: $primaryFont;
                    color: $secondaryMainColor;
                    margin: 0;
                }

                .numbers {
                    font-size: 13px;
                    font-weight: 600;
                    line-height: 18px;
                    text-align: right;
                    display: flex;
                    width: 100%;
                    justify-content: flex-end;

                    .icon-ctn {
                        padding-left: 32px;
                        display: flex;
                        align-items: center;
                        color: #999999;

                        i {
                            font-size: $primaryTextSize;
                        }

                        p {
                            padding-left: 12px;
                            font-size: $detailTextSize;
                            font-weight: 600;
                            line-height: 19px;
                            font-family: $primaryFont;
                        }
                    }
                }
            }

            .location{
                padding: 0 20px;
                margin: 0;
                color: $secondaryMainColor;
                font-size: 16px;
                line-height: 22px;
                margin-bottom: 10px;
            }

            .more-info {
                position: relative;
                border-top: solid 1px #D5D5D5;
                display: flex;
                justify-content: space-between;

                .button{
                    width: 50px;
                    height: 100%;
                    background-color: $primaryColor;
                    transition: all 0.5s ease-in-out;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    position: absolute;
                    right: 0;
                    z-index: 5;

                    i{
                        color: white;
                    }

                    &:hover{
                        cursor: pointer;
                        background-color: darken($primaryColor, 0.9);
                    }
                }

                .inner {
                    padding: 0 20px;
                    display: flex;
                    margin-right: 10px;

                    .address, .type{
                        color: $secondaryMainColor;
                        font-size: 13px;
                        line-height: 24px;
                        font-weight: 700;
                    }

                    .type{
                        margin-left: 25px;
                    }
                }
            }

            @media (max-width:600px) {
                position: relative;
                max-width: 100%;
            }

        }
    }
}
