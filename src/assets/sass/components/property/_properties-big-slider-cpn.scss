.properties-big-slider-cpn {
  height: 845px;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: red;
    opacity: 0.66;
    z-index: 99;
    pointer-events: none;
  }

  .logo-remax-collection {
    position: absolute;
    right: 80px;
    bottom: 30px;
    width: 220px;
    z-index: 999;
    pointer-events: none;

    @media (max-width: 768px) {
      display: none;
    }
  }

  .properties-list-ctn {
    height: 100%;
    padding: 0;
  }

  .properties {
    width: 100%;
    height: 100%;
    margin-right: 0;

    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    &:before {
      content: '';
      position: absolute;
      width: 100%;
      height: 422.5px;
      top: 0;
      left: 0;
      background: linear-gradient(0deg, rgba(0, 0, 0, 0.00) 0%, #000 100%);
      opacity: 0.66;
      z-index: 1;
      pointer-events: none;
    }

    &:after {
      content: '';
      position: absolute;
      width: 100%;
      height: 422.5px;
      bottom: 0;
      left: 0;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0.00) 0%, #000 100%);
      opacity: 0.66;
      z-index: 1;
      pointer-events: none;
    }

    &:hover {
      .properties-info {
        max-height: unset;
      }
    }

    .properties-info {
      position: absolute;
      left: 50px;
      bottom: 30px;
      z-index: 2;

      @media (max-width: 550px) {
        left: 20px;
        bottom: 20px;
      }

      .bloc-head {
        background-color: $primaryColor;
        color: $white;
        padding: 7px 24px;
        justify-content: center;
        max-width: fit-content;

        p {
          font-size: 20px;
          font-weight: 600;
          text-transform: uppercase;
          font-family: $primaryFont;
        }
      }

      .more-info {
        position: unset;
        max-height: unset;
        opacity: 1;
        background-color: $white;
        padding: 15px 20px;
        max-width: 400px;

        @media (max-width: 550px) {
          max-width: calc(100% - 20px);
        }

        .address {
          color: $primaryColor;
          font-family: $primaryFont;
          font-size: 20px;
          font-weight: 600;
          text-transform: uppercase;
          margin-bottom: 0;
        }

        .align {
          justify-content: flex-start;
          gap: 5px;
          margin-top: 5px;

          p {
            color: $primaryColor;
            font-size: 14px;
            font-weight: 400;
            line-height: 24px;
          }
        }
      }
    }
  }

    .nav {
      width: 65px;
      height: 65px;
      background-color: rgba(0, 0, 0, 0.3);
      border-radius: 100px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      top: calc(50% - 32.5px);
      z-index: 15;

      i {
        font-size: 26px;
        color: $white;
      }

      &.next {
        right: 40px;
      }

        &.prev {
            left: 40px;
        }

      &:hover {
        background-color: rgba(0, 0, 0, 0.7);
      }
    }
  }
