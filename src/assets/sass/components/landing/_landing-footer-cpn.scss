.landing-footer-cpn {
  margin: 100px 0;

  .main-button.-primary {
    min-width: auto;
    font-size: $detailTextSize;
    line-height: $detailTextLineHeight;
    text-transform: none;
  }

  .footer-info {
    display: flex;
    align-items: center;
    gap: 60px;
    max-width: 770px;
    margin: auto;

    > div {
      width: 50%;
      max-width: 250px;
      &:first-child { margin-left: auto; }
      &:last-child { margin-right: auto; }
    }

    .logo-ctn {
      p {
        font-size: 12px;
        line-height: 17px;
      }
    }

    .contact-ctn {
      .location-ctn,
      .mail-ctn,
      .phone-ctn {
        width: auto;
        display: flex;
        align-items: flex-start;
        margin-bottom: 20px;

        a, p {
          color: #2D2F31;
          font-size: 14px;
          line-height: 18px;
          font-weight: 400;
        }

        .icon {
          position: relative;
          top: 2px;
          padding-right: 15px;
          color: #2D2F31;

          &-pin {
            left: -3px;
            padding-right: 9px;
            font-size: 23px;
          }
        }
      }

      .phone-ctn a {
        font-size: 16px;
        line-height: 21px;
        font-weight: 700;
      }
    }
  }

  .footer-share {
    text-align: center;

    .main-button { margin: 60px auto; }

    p {
      margin-bottom: 30px;
      color: $secondaryMainColor;
      font-size: 16px;
      line-height: 22px;
    }

    .share-ctn {
      display: flex;
      gap: 20px;
      width: max-content;
      margin: auto;

      a {
        color: $secondaryMainColor;
        font-size: 18px;
      }
    }
  }

  @media (max-width: 768px) {
    .footer-info { gap: 20px; }
  }
}

