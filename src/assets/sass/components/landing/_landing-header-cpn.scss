.landing-header-cpn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 120px;
  padding: 0 40px;

  .logo-ctn a { display: block; }

  .main-button {
    min-width: auto;
    font-size: $detailTextSize;
    line-height: $detailTextLineHeight;
    text-transform: none;
  }

  a {
    color: $secondaryMainColor;
    transition: $primaryAnimation;
    cursor: pointer;
    &:hover { color: $primaryColor; }
  }

  nav {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 30px;
    height: 100%;

  //   .share-ctn {
  //     display: flex;
  //     align-items: center;
  //     margin: 0 25px;

  //     a {
  //       padding: 10px;
  //       &:hover { color: $primaryColor; }
  //     }
  //   }

	// 	.number-ctn {
  //     display: flex;
  //     align-items: center;
  //     height: 100%;
	// 		padding: 0 25px;
	// 		background: $primaryColor;
	// 		transition: $primaryAnimation;

	// 		&:hover { background-color: darken($primaryColor, 10); }

	// 		a {
	// 			color: white;
	// 			font-size: 20px;
	// 			font-weight: $primaryBold;

	// 			i {
	// 				margin-right: 10px;
	// 				font-size: 18px;
	// 			}
	// 		}
	// 	}
  }

  @media (max-width: 768px) {
    .main-button {
      padding: 10px 22px;
    }
  }

  @media (max-width: 570px) {
    padding: 0 15px;

    nav {
      gap: 10px;
    }

    .main-button {
      height: auto;
      margin-left: 10px;
      font-size: 11px;
    }

    .language-switcher {
      padding-left: 20px;
    }
  }
}
