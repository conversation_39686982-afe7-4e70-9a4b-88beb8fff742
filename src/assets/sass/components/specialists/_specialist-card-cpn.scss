/*
	Specialist Card Component
*/

.specialist-card-cpn {
	display: block;
	position: relative;
	width: 100%;

	.title {
		margin-top: 0;
		margin-bottom: 20px;
	}

	.specialist-content {
		padding: 40px;
		width: 100%;
		display: inline-block;
		box-shadow: 0 2px 4px 0 rgba(0,0,0,0.1);
		background-color: $white;
		margin-bottom: 4px;

		.title {
			text-transform: uppercase;
			color: $primaryColor;
		}
	}

	.specialist-wrap {
		display: block;
		position: relative;
		width: 100%;
		margin-bottom: 20px;
		border-radius: 3px;
	}

	.img-ctn {
		img {
			margin-bottom: 40px;
		}

		@media (max-width: 992px) {
			text-align: center;
		}
	}

	.description {
		margin-top: 0;
		margin-bottom: 20px;
		color: $secondaryMainColor;
		font-family: $secondaryFont;
		font-size: $detailTextSize;
		line-height: $detailTextLineHeight;
	}

	.user-ctn {
		.name {
			margin-top: 0;
			margin-bottom: 10px;
			color: $secondaryMainColor;
			font-family: $secondaryFont;
			font-size: $detailTextSize;
			font-weight: 700;
			line-height: 19px;
		}

		.address {
			margin: 0;
			max-width: 250px;
			color: $secondaryMainColor;
			font-family: $secondaryFont;
			font-size: $detailTextSize;
			line-height: $detailTextLineHeight;
		}

		.contact-ctn {
			display: flex;
			align-items: center;
			margin-top: 40px;

			@media (max-width:570px) {
				flex-wrap: wrap;
			}

			a {
				margin-right: 35px;
				color: $secondaryMainColor;
				font-size: $detailTextSize;
				font-weight: 900;
				line-height: 17px;
				text-transform: uppercase;

				@media (max-width:570px) {
					width: 100%;
					margin-bottom: 15px;
				}

				&:last-child {
					margin-right: 0;
				}

				&.mobile {
					display: flex;
					align-items: center;
					font-family: $primaryFont;

					i {
						font-size: 18px;
						margin-right: 5px;
					}
				}

				&.website {
					font-family: $primaryFont;
					display: flex;
					align-items: center;

					i {
						font-size: 18px;
						margin-right: 5px;
					}
				}

				&.icon-mail {
					font-size: 16px;
				}
			}
		}
	}
}
