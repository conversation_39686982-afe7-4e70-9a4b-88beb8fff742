.search-map-pane-cpn {
	#map {
		position: fixed;
    width: 100%;
    min-height: 500px;
		height: 100%;
		right: 0;
    top: 80px;
		transition: $primaryAnimation;

		.mapbox-logo { display: none; }
    .mapboxgl-ctrl-top-right { top: 160px; }

    .inner-popup {
      display: flex;
      cursor: pointer;

      img {
        width: 70px;
      }

      .content {
        padding-left: 10px;

        .price {
          margin-top: 0;
          margin-bottom: 5px;
          font-family: $thirdFont;
          color: $primaryColor;
          font-size: 16px;
          font-style: normal;
          font-weight: 600;
          line-height: normal;
        }

        .location {
          margin: 0;
          color: #666666;
          font-family: $thirdFont;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
        }
      }
    }
	}

	@media (max-width: 992px) {
    #map {
      display: none;
      position: relative;
      top: 0;

      .mapboxgl-ctrl-top-right { top: 20px; }
    }

		&.show #map { display: block; }
	}
}
