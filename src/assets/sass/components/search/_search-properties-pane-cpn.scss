.search-properties-pane-cpn {
  position:         relative;
  margin:           0;
  z-index:          10;
  width:            50%;
  background-color: $white;
  min-height:       500px;
  box-shadow:       2px 0 4px 0 rgba(0, 0, 0, 0.10);
  transition:       $primaryAnimation;
  margin-left:      -50%;
  padding-top:      45px;

  .properties-menu {
    h3 {
      color:          $primaryColor;
      font-family:    $primaryFont;
      font-size:      24px;
      font-style:     normal;
      font-weight:    600;
      line-height:    normal;
      text-transform: uppercase;
      margin:         0;
      margin-bottom: 10px;
    }

    .ng-select {
      width: auto;

      .ng-select-container .ng-value-container {
        color:       #666;
        font-family: $primaryFont;
        font-size:   15px;
        font-style:  normal;
        font-weight: 400;
        line-height: 24px;
      }

      .ng-select-container, .ng-select.ng-select-opened > .ng-select-container {
        border: none;
      }
    }
  }

  .properties-list-cpn {
    padding-top: 0;
  }

  .loading {
    margin-top: 150px;
    opacity:    1 !important;
  }

  .properties {
    width: calc(100% * 1 / 2 - 10px);

    &:nth-child(3n) {
      margin-right: 20px;
    }

    &:nth-child(2n) {
      margin-right: 0;
    }
  }

  .toggle-pane {
    position:         sticky;
    top:              185px;
    right:            0px;
    margin-right:     -45px;
    margin-top:       20px;
    float:            right;
    display:          block;
    width:            30px;
    line-height:      60px;
    text-align:       center;
    background-color: $primaryColor;
    color:            white;
    transition:       $primaryAnimation;
    cursor:           pointer;
    opacity:          1;
    transition:       $primaryOpacityAnimation;

    div {
      transform: rotate(180deg);
    }

    &:hover {
      background-color: rgba($primaryColor, .60);
    }

    &.selected {
      pointer-events:   none;
      background-color: rgba(153, 153, 153, 0.7);
    }

    &.toggle-pane-1 {
      margin-bottom: 65px;
    }

    &.toggle-pane-2 {
      top:        250px;
      margin-top: 85px;
    }
  }

  &.open {
    margin: 0;

    .toggle-pane-1 {
      pointer-events:   auto;
      background-color: $primaryColor;

      &:hover {
        background-color: rgba($primaryColor, .60);
      }
    }
  }

  &.grid {
    width:  calc(100% - 80px) !important;
    margin: 0;

    .properties-list-cpn {
      margin: 0 auto;
    }

    .properties {
      width: calc(100% * 1 / 3 - 14px);

      &:nth-child(2n) {
        margin-right: 20px;
      }

      &:nth-child(3n) {
        margin-right: 0px;
      }

      .properties-info .more-info .align {
        @media (max-width: 1200px) {
          display: flex;
        }
      }
    }

    .toggle-pane {
      right:        0px;
      margin-right: -46px;
    }
  }

  .no-results {
    color:      $secondaryMainColor;
    margin-top: 120px;

    .txt1 {
      font-size:  $primaryTextSize;
      margin-top: 60px;
    }

    .txt2 {
      font-size:  $detailTextSize;
      margin-top: 20px;
    }

    a {
      margin: 40px auto;
    }
  }

  @media (min-width: 993px) and (max-width: 1200px) {
    .properties .properties-info .more-info .align {
      display: none;
    }
  }

  @media (max-width: 992px) {
    display: none;

    &.show {
      display: block;
      width:   100% !important;
      margin:  0;
    }

    .loading {
      margin-top: 100px;
    }

    .toggle-pane {
      display: none;
    }
  }

  @media (max-width: 768px) {
    &.show {
      .properties {
        width:        100%;
        margin-right: 0;
      }
    }
  }
}
