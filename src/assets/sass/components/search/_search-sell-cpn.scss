$searchSellSearchPadding: 40px 0;
$searchSellSearchBG: $primaryColor;

$searchSellSearchOpacity: 0.6;
$searchSellSearchColor: white;
$searchSellSearchFont: $secondaryFont;
$searchSellSearchSize: 13px;
$searchSellSearchWeight: 600;
$searchSellSearchLineHeight: 18px;

$searchSellInputBG: white;
$searchSellInputColor: white;
$searchSellInputFont: $secondaryFont;
$searchSellInputSize: 16px;
$searchSellInputLineHeight: 22px;

.search-sell-cpn {
  padding: $searchSellSearchPadding;
  background-image: url("/assets/images/placeholder/banner/ban-2.jpg");
  background-repeat: no-repeat;
  background-size: cover;
  min-height: 680px;
  display: flex;
  align-items: center;

  @media (max-width: 992px) {
    padding: 40px 0;
  }


  .container {
    @media (max-width: 992px) {
      padding: 0;
      width: 90%;
    }
  }

  .tabs-ctn {
    float: none;
    margin: 0 auto;
    background-color: $searchSellSearchBG;
    padding: 50px 80px;
    text-align:center;

    @media (max-width: 992px) {
      position: relative;
      padding: 50px 40px;
    }

    .icon{
      display: block;
      color: white;
      font-size: 60px;
      margin-bottom: 30px;
    }

    .title {
      margin-top: 0px;
      text-align: center;
      color: white;
      font-size: 32px;
      max-width: 350px;
      line-height: 42px;
      margin: 0 auto 30px;
      text-transform: uppercase;
    }

    p{
      color:white;
      font-size: 18px;
    }

    label {
      position: relative;
      padding: 0 12px 5px;
      color: $searchBuyTabColor;
      font-family: $searchBuyTabFont;
      font-size: $searchBuyTabSize;
      font-weight: $searchBuyTabWeight;
      text-align: center;
      text-transform: none;
      overflow: inherit;

      &:after {
        position: absolute;
        bottom: 0;
        left: 0;
        content: "";
        background: #fff;
        height: 2px;
        display: block;
        width: 0px;
        transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
      }
    }

    .tab-head {
      display: flex;
      align-items: center;
      justify-content: flex-start;

      @media (max-width: 992px) {
        display: block;
      }
    }

    .tabs {
      padding-left: 0;
      background: transparent;
      margin: 0;
      width: auto;

      .indicator {
        background: $searchBuyIndicatorColor;
      }
    }

    .special-search {
      display: block;
      margin-top: 40px;
      text-align: center;
      opacity: $searchBuySearchOpacity;
      color: $searchBuySearchColor;
      font-family: $searchBuySearchFont;
      font-size: $searchBuySearchSize;
      font-weight: $searchBuySearchWeight;
      line-height: $searchBuySearchLineHeight;

      @media (max-width: 992px) {
        padding-left: 0;
      }

      .icon-arrow-externe{
        margin-left: 5px;
        display: inline-block;
        font-size: 12px;
      }
    }

    .form-ctn {
      .input-ctn {
        position: relative;
        margin-top: 30px;
        border-radius: 3px;
      }

      .ap-input-icon {
        display: none;
      }

      input {
        height: 60px;
        width: 100%;
        padding-left: 20px;
        border-radius: 3px;
        background: $searchBuyInputBG;
        color: $searchBuyInputColor;
        font-family: $searchBuyInputFont;
        font-size: $searchBuyInputSize;
        line-height: $searchBuyInputLineHeight;
        box-sizing: border-box;

        @media (max-width: 425px ) {
          max-width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .main-button {
        margin-top: 30px;
      }
    }
  }

}
