$searchBuySearchPadding: 100px 0;
$searchBuySearchBG: white;

$searchBuyIndicatorColor: white;
$searchBuyTabColor: white;
$searchBuyTabFont: $secondaryFont;
$searchBuyTabSize: 18px;
$searchBuyTabWeight: 600;

$searchBuySearchOpacity: 1;
$searchBuySearchColor: $primaryColor;
$searchBuySearchFont: $secondaryFont;
$searchBuySearchSize: 13px;
$searchBuySearchWeight: 600;
$searchBuySearchLineHeight: 18px;

$searchBuyInputBG: white;
$searchBuyInputColor: #666666;
$searchBuyInputFont: $secondaryFont;
$searchBuyInputSize: 16px;
$searchBuyInputLineHeight: 22px;

.search-buy-cpn {
	padding: $searchBuySearchPadding;
	background-image: url("/assets/images/placeholder/banner/ban-2.jpg");
	background-repeat: no-repeat;
	background-size: cover;
	min-height: 680px;
	display: flex;
    align-items: center;

	@media (max-width: 992px) {
		padding: 40px 0;
	}

	.container {
		@media (max-width: 992px) {
			padding: 0;
			width: 90%;
		}
	}

	#acheter {
		.ng-control {
			padding-left: 10px;
			min-height: 60px;
		}
    
		.ng-option {
			text-align: left !important;
		}
	}

	.tabs-ctn {
		float: none;
		margin: 0 auto;
		background-color: $searchBuySearchBG;
		padding: 50px 80px; 

		@media (max-width: 992px) {
			position: relative;
			padding: 50px 40px;
		}

		.title {
			margin-top: 0px;
			text-align: center;
			color: black;
			font-size: 32px;

			.icon{
				display: block;
				color: $primaryColor;
				font-size: 60px;
				margin-bottom: 30px;
			}
		}

		label {
			position: relative;
			padding: 0 12px 5px;
			color: $searchBuyTabColor;
			font-family: $searchBuyTabFont;
			font-size: $searchBuyTabSize;
			font-weight: $searchBuyTabWeight;
			text-align: center;
			text-transform: none;
			overflow: inherit;

			&:after {
				position: absolute;
				bottom: 0;
				left: 0;
				content: "";
				background: #fff;
				height: 2px;
				display: block;
				width: 0px;
				transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
			}
		}

		.tab-head {
			display: flex;
			align-items: center;
			justify-content: flex-start;

			@media (max-width: 992px) {
				display: block;
			}
		}

		.tabs {
			padding-left: 0;
			background: transparent;
			margin: 0;
			width: auto;

			.indicator {
				background: $searchBuyIndicatorColor;
			}
		}

		.special-search {
			display: block;
			margin-top: 40px;
			text-align: center;
			opacity: $searchBuySearchOpacity;
			color: $searchBuySearchColor;
			font-family: $searchBuySearchFont;
			font-size: $searchBuySearchSize;
			font-weight: $searchBuySearchWeight;
			line-height: $searchBuySearchLineHeight;

			@media (max-width: 992px) {
				padding-left: 0;
			}

			.icon-arrow-externe{
				margin-left: 5px;
				display: inline-block;
				font-size: 12px;
			}
		}

		.form-ctn {
			.input-ctn {
				position: relative;
				margin-top: 12px;
			}

			.main-button {
				position: absolute;
				right: 5px;
				top: 5px;
				height: 50px;
				border-radius: 3px;
				text-transform: uppercase;

				@media (max-width: 992px) {
					width: 50px;
					min-width: 50px;
					padding: 7px 18px;
					font-size: $primaryTextSize;

					span {
						display: none;
					}

					&:before {
						content: "\e90a";
						font-family: 'icomoon' !important;
						speak: none;
						font-style: normal;
						font-weight: normal;
						font-variant: normal;
						text-transform: none;
						line-height: 1;
						font-size: $primaryTextSize;
					}
				}
			}
		}
	}
}