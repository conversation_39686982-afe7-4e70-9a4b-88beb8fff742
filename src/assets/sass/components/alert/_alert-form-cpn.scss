/*
	Alert Form Components
*/

.alert-form-cpn {
	padding-top: 60px;
	padding-bottom: 80px;
	background-image: url("/assets/images/common/bg-alerte.jpg");
	background-size: cover;

	.container {
		border-radius: 3px;
		background-color: #F2F2F2;
		box-shadow: 0 2px 4px 0 rgba(0,0,0,0.1);

		&.send{
			display: flex;
			justify-content: center;
			align-items: center;

			.alert-form{
				opacity: 0;
			}
		}

		@media (max-width:768px) {
			margin: 0 15px;
		}
	}

	.alert-form {
		padding: 80px 100px;

		@media (max-width: 768px) {
			padding: 60px 0px;
		}
	}

	.loading-inner{
		opacity: 0.5;
	}

	.form-loader{
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%,-50%);
	}

	.form-head {
		position: relative;
		padding-bottom: 40px;

		.-page-title {
			color: $secondaryMainColor;
			text-transform: uppercase;
		}
	}

	.step {
		position: absolute;
		top: 0;
		right: 0;
		border: 2px solid $secondaryMainColor;
		border-radius: 20px;
		padding: 12px 26px;

		@media (max-width: 450px) {
			position: relative;
			max-width: 100px;
			margin: 20px auto 0;
		}

		p {
			margin: 0;
			color: $secondaryMainColor;
			font-family: $primaryFont;
			font-size: $detailTextSize;
			font-weight: bold;
			line-height: 17px;
			text-align: center;
		}
	}

	.-page-title {
		margin: 0;
		font-size: $h2TitleSize;
		line-height: $h2TitleLineHeight;

		@media (max-width: 450px) {
			text-align: center;
		}
	}

	.-page-description {
		margin-top: 20px;
		margin-bottom: 0;
		padding-bottom: 40px;
		color: $secondaryMainColor;
		font-family: $secondaryFont;
		font-size: $primaryTextSize;
		line-height: $primaryTextLineHeight;
	}

	.location-ctn, .type-ctn {
		padding-top: 20px;
		@include clearfix;

		.custom-checkbox-group {
			width: 50%;
			float: left;

			@media (max-width: 768px) {
				float: none;
				width: 100%;
			}

			label {
				color: $secondaryMainColor;
			}
		}
	}

	.location-ctn {

		.separator {
			border-bottom: 1px solid #e1e1e1;
			margin-bottom: 15px;
			padding-top: 15px;
			clear: both;

			p {
				text-transform: uppercase;
				color: $secondaryMainColor;
				font-weight: 700;
				font-size: 15px;
				line-height: 16px;
			}
		}
	}

	.input-ctn {
		padding-top: 40px;
	}

	.form-row {
		margin: 0;
	}

	.warning-message {
		//max-width: 580px;
		margin-top: 35px;
		margin-bottom: 0;
		color: $secondaryMainColor;
		font-family: $secondaryFont;
		font-size: $detailTextSize;
		line-height: $detailTextLineHeight;
		text-align: right;
		width: 100%;
	}

	.-page-required{
		color: $secondaryMainColor;
		font-family: $secondaryFont;
		font-size: 12px;
		line-height: $detailTextLineHeight;
		text-align: right;
	}

	.button-ctn {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		padding-top: 40px;
	}

	.form-response {
		position: absolute;
		text-align: center;
		opacity: 0;
		.button-ctn {
			justify-content: center;
		}

		&.show{
			opacity: 1;
			transition: all 0.5s ease-in-out;
		}
	}
}
