.laraberg{

  margin-bottom:60px;

  a{
    color:$primaryColor;
  }

  .wp-block-quote{
    border-color: $primaryColor;
    margin:40px 0;

    &.is-style-large{
      padding-left: 0;
    }

    cite{
      font-style: italic;
    }
  }

  .wp-block-columns,.wp-block-table,.wp-block-image,.wp-block-audio,.wp-block-quote{
    margin:40px 0 !important;
  }

  .wp-block-columns {

    .wp-block-image,.wp-block-table,.wp-block-image,.wp-block-audio,ul,ol,.wp-block-button,.wp-block-quote{
      margin:0 !important;
    }

  }


  .wp-block-button{
    .wp-block-button__link{
      @extend .main-button;
      @extend .-primary;
    }

    &.is-style-outline{
      .wp-block-button__link{
        @extend .-secondary;
      }
    }
  }


  ul{
    margin:40px 0;

    p,a {
			color: $secondaryMainColor;
			font-family: $secondaryFont;
			font-size: $subTextSize;
			line-height: $subTextLineHeight;
		}

    li{
        ul,ol{
            margin:10px 0;
            padding-bottom:0  !important;

            li{
              border: none !important;
              margin-bottom:0;
            }
        }
      }
  }


  ol {
    padding-left: 0px;
    padding-bottom: 30px;
    list-style: none;
    counter-reset: item;

    li{
        position:relative;
        counter-increment: item;
        border-bottom: $listBorderBottom;
        color: $listColor;
				font-family: $secondaryFont;
        margin-bottom:25px;
        padding: 16px 0px 16px 40px;

        &:before {
          left: 0;
          font-weight: 600;
          font-family: $secondaryFont;
          position:absolute;
          content: counter(item);
          color: $primaryColor;
      }

        ul,ol{
          margin:10px 0;
          padding-bottom:0 !important;
          li{
            border: none !important;
            margin-bottom:0;

              &:before {
              font-size:15px;
            }
          }
        }



    }
  }

  .wp-block-file{

    background-color:#f2f2f2;
    margin-bottom: 40px;
    padding:15px 20px;

    br:first-child{
      display: none;
    }
    a{
      color:black;
      font-weight: 600;
      font-family: $primaryFont;
    }

    .wp-block-file__button{
      @extend .main-button;
      @extend .-primary;

      text-transform: none;
      height: 25px;
      font-weight: 600;
      width:160px;
    }

    @media (max-width: 600px) {
      flex-wrap: wrap;
      justify-content: center;

      .wp-block-file__button{
        margin-top: 10px;
      }


    }
  }


  /**** Block Youtube ****/
  .wp-block-embed-youtube,
  .wp-block-embed-vimeo{
    margin:40px 0;

    .wp-block-embed__wrapper {
        position: relative;
        padding-bottom: 56.25%; /* 16:9 */
        padding-top: 25px;
        height: 0;

        iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
    }
  }

  .wp-block-table{

    tr{

      td{
        border:none;
        text-transform: unset;
      }


      &:nth-child(odd) {
        background-color: #f2f2f2;
      }

      &:nth-child(even) {
        background-color: white;
      }
    }




    figcaption{
      margin: 20px 0;
    }

    a{
      color:$primaryColor;
      opacity:1;
      transition: $primaryAnimation;

      &:hover{
        opacity:0.7
      }
    }

    .is-style-stripes{
      padding-bottom: 30px;
    }
  }

  .wp-block-cover{
    .wp-block-cover__inner-container{
      p{
        color: white;
        &.has-large-font-size{
          font-size: 24px;
        }
      }
    }
  }

  .wp-block-separator{
    margin: 60px auto;
  }

}
