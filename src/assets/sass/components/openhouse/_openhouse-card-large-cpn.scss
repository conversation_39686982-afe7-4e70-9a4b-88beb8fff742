$openHouseCardBackground: #fbf7ee;

.openhouse-card-large-cpn{
    background-color: $openHouseCardBackground;
    display: flex;
    margin-bottom: 30px;

    .img-ctn{
        padding: 0;

        img{
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }

    .content{

        padding: 0;

        .bloc-head {
            padding: 30px 40px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
    
            .price {
                font-size: 24px;
                font-weight: bold;
                line-height: 33px;
                white-space: nowrap;
                margin: 0;
            }
    
            .numbers {
                font-size: 16px;
                font-weight: 600;
                line-height: 19px;
                text-align: right;
                display: flex;
                width: 100%;
                justify-content: flex-end;
    
                .icon-ctn {
                    padding-left: 32px;
                    display: flex;
                    align-items: center;
                    color: #999999;
    
                    i {
                        font-size: 20px;
                        margin-right: 10px;
                    }
    
                    p {
                        font-size: 16px;
                        font-size: $detailTextSize;
                        font-weight: 600;
                        line-height: 19px;
                        font-family: $secondaryFont;
                    }
                }
            }
        }

        .dates-list{
            margin: 20px 0 80px;
            padding: 0 40px;
            display: flex;
            flex-wrap: wrap;

            .date{
                border: 1px solid #D5D5D5;
                border-radius: 20px;
                padding: 10px 20px;
                margin-right: 7px;
                margin-bottom: 10px;

                &:last-child{
                    margin-left: 0;
                }

                span{
                    color: #666666;
                    font-size: 14px;
                    line-height: 19px;
                    font-weight: 700;
                    text-align: center;
                }
            }
        }

        .location{
            padding:0 40px;
            margin: 0;
            color: #666666;
            font-size: 16px;
            line-height: 22px;
            margin-bottom: 10px;
        }


    }

    

    

    .more-info {
        position: absolute;
        bottom: 0;
        width: 100%;
        border-top: solid 1px #D5D5D5;
        display: flex;
        justify-content: space-between;

        .button{
            width: 50px;
            height: 100%;
            background-color: $primaryColor;
            transition: all 0.5s ease-in-out;
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            right: 0;
            z-index: 5;

            i{
                color: white;
            }

            &:hover{
                cursor: pointer;
                background-color: darken($primaryColor, 0.9);
            }
        }

        .inner {
            padding: 0 40px;
            display: flex;
            margin-right: 10px;

            .address, .type{
                color: $grey;
                font-size: 13px;
                line-height: 24px;
            }

            .type{
                margin-left: 25px;
            }
        }
    }

    @media (max-width:992px) {
        .content{
            .bloc-head {
                padding: 30px 20px 0;
            }

            .dates-list{
                padding: 0 20px;
            }

            .location{
                padding: 0 20px;
            }
        }

        .more-info {
            .inner {
                padding: 0 20px;
            }
        }
    }

    @media (max-width:768px) {
        flex-wrap: wrap;

        .img-ctn{
            width: 100%;
        }

        .content{
            width: 100%;

            .dates-list{
                .date{
                    padding: 5px 15px;

                    span{
                        font-size: 13px;
                        line-height: 16px;
                    }
                }
            }
        }
    }
}