/*
	Property Group Card Component
*/

$propertyGroupsCardRadius: 0 0 2px 2px;
$propertyGroupsCardBG: #FFFFFF;
$propertyGroupsCardShadow: 0 2px 6px 0 rgba(0,0,0,0.1);

.propertygroup-card-cpn {
	padding: 30px;
	margin-bottom: 40px;
	border-radius: $propertyGroupsCardRadius;
	background-color: $propertyGroupsCardBG;
	box-shadow: $propertyGroupsCardShadow;
	@include clearfix;

	.img-ctn {
		padding-left: 0;

		@media (max-width: 768px) {
			padding: 0;
			margin-bottom: 20px;
		}
	}

	.card-content {
		padding-right: 0;

		@media (max-width: 768px) {
			padding: 0;
		}
	}

	.propertygroup-card {
		display: flex;
		align-items: center;
		justify-content: space-between;

		@media (max-width: 768px) {
			display: block;
		}
	}

	.title {
		margin-top: 0;
		margin-bottom: 25px;
	}

	.description {
		margin-top: 0;
		margin-bottom: 15px;
		color: $secondaryMainColor;
		font-family: $secondaryFont;
		font-size: $detailTextSize;
		line-height: $detailTextLineHeight;
	}
}
