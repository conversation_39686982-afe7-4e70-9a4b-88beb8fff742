/*
	CTA Small Component
*/

.cta-small-cpn {
	background: $primaryColor;

	.container {
		padding: 100px 0 170px;

		@media (max-width: 768px) {
			padding: 60px 40px 100px;
		}
	}

	iframe {
		margin-bottom: 140px;

		@media (max-width: 768px) {
			margin-bottom: 60px;
			height: 350px;
		}

		@media (max-width: 425px) {
			height: 300px;
		}
	}

	.title {
		margin: 0;
		color: white;
		font-size: 34px;
		line-height: 42px;
		width: 80%;
		text-transform: uppercase;
		padding-right: 10px;

		@media (max-width: 768px) {
			width: 100%;
			margin-bottom: 20px;
		}
	}

	.text-content {
		color: white;
		font-size: 17px;
		line-height: 28px;
		font-family: $secondaryFont;

		> div {
			padding: 0;
		}

		p {
			margin: 0;
		}
	}
}
