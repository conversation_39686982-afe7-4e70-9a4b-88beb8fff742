$pdf-guide-cta-cpn-background: $paleBackground;

.pdf-guide-cta-cpn {
    margin: 80px 0;

    &.full-width {
        background-color: $pdf-guide-cta-cpn-background;
        margin: 0;
    }

    .container {
        @include flex();
        padding: 80px 60px;
        background-color: $pdf-guide-cta-cpn-background;

        .img-ctn {
            width: 600px;
            margin-right: 100px;

            img {
                width: 100%;
            }
        }

        .text-ctn {
            width: 100%;

            h2.title {
                margin-top: 0;
            }

            p.description {
                margin-bottom: 30px;
                color: $secondaryMainColor;
                font-family: $secondaryFont;
                font-size: $subTextSize;
                line-height: $subTextLineHeight;
            }
        }
    }

	@media (max-width: 768px) {

        margin: 60px 0;

		.container {
			padding: 30px 60px;
			flex-direction: column;

			.img-ctn {
                width: auto;
                margin: 30px 0 40px;
			}

			.text-ctn {
                text-align: center;

				a.main-button {
					margin: auto;
				}
			}
		}
	}

	@media (max-width: 500px) {
		.container {
			padding: 30px 20px;
		}
	}
}
