/*
	CTA Broker Component
*/

$ctaBrokerPadding: 120px 0 140px;

$ctaBrokerObliqueBG: white;
$ctaBrokerObliqueWidth: 240px;
$ctaBrokerObliqueRight: 290px;
$ctaBrokerObliqueSkew: skew(-45deg);

$ctaBrokerTagSize: 13px;
$ctaBrokerTagLineHeight: 18px;
$ctaBrokerTagNameWeight: 700;

.cta-broker-cpn {
	overflow: hidden;
	padding:$ctaBrokerPadding;
	padding-bottom: 95px;
	position: relative;
	background-size: cover;

	@media (max-width: 768px) {
		padding: 0;
	}

	.container {
		z-index: 3;
	}

	.list {
		padding-left: 0;
	}

	.row {
		display: flex;
		align-items: stretch;

		@media (max-width: 1200px) {
			margin: 0;
		}

		@media (max-width: 992px) {
			flex-direction: column;
		}
	}

	.cta-broker-content {
		display: flex;
		flex-direction: column;
		justify-content: center;
		text-align: center;
		margin-bottom: 40px;

		@media (max-width: 992px) {
			order: 2;
		}

		@media (max-width: 768px) {
			padding: 40px 0;
		}

		.img-ctn {
			height: 490px;

			@media (max-width: 768px) {
				height: 350px;
			}

			@media (max-width: 425px) {
				height: 300px;
			}

			img {
				object-fit: cover;
				height: 100%;
			}
		}

		.title {
			position: relative;
			color: $secondaryMainColor;
			font-family: $primaryFont;
			font-size: 38px;
			max-width: 600px;
			line-height: normal;
			font-weight: 500;
			text-transform: uppercase;
			margin: 40px auto 0;
		}

		.subtext {
			font-size: $subTextSize;
			line-height: $subTextLineHeight;
			margin-top: 0;
			margin-bottom: 30px;
			color: $secondaryMainColor;
			font-family: $secondaryFont;

		}

		a {
			width: fit-content;
			margin: 0 auto;
		}

		ul {
			padding-left: 0px;
			padding-bottom: 30px;

			li {
				position: relative;
				padding: 16px 0px 16px 40px;
				color: $listColor;
				font-family: $secondaryFont;
				font-size: $listSize;
				line-height: $listLineHeight;
				border-bottom: $listBorderBottom;

				&:before {
					color: $primaryColor;
					font-family: 'icomoon';
					speak: none;
					font-style: normal;
					font-weight: normal;
					font-variant: normal;
					text-transform: none;
					line-height: 1;
					-webkit-font-smoothing: antialiased;
					-moz-osx-font-smoothing: grayscale;
					content: "\e90c";
					position: absolute;
					left: 0;
					top: 22px;
					//transform: translate(0%, -50%);
				}
			}
		}
	}

	.oblique {
		display: block;
		background: $ctaBrokerObliqueBG;
		height: 100%;
		width: $ctaBrokerObliqueWidth;
		position: absolute;
		z-index: 1;
		bottom: 0%;
		right: $ctaBrokerObliqueRight;
		transform: $ctaBrokerObliqueSkew;
	}

	.cta-broker-img {
		text-align: center;
		position: relative;

		.img-ctn{
			position: relative;

			// &:before{
			// 	position: absolute;
			// 	content: "";
			// 	background-color: #00377C;
			// 	width: 60px;
			// 	height: 60px;
			// 	top: -10px;
			// 	right: -10px;
			// 	z-index: -1;
			// }
		}

		img{
			max-height: 560px;
			border: 10px solid #00377C;
		}

		@media (max-width: 992px) {
			order: 1;
			padding-top: 20px;
			width: 100%;
		}
	}

	.broker-tag {
		min-width: 280px;
		padding: 12px 20px;
		position: absolute;
		right: 20px;
		bottom: 60px;
		background-color: rgba(0,0,0,0.5);
		text-align: left;

		@media (max-width: 992px) {
			right: 0;
		}

		p {
			margin: 0;
			color: $white;
			font-family: $secondaryFont;
			font-size: $ctaBrokerTagSize;
			line-height: $ctaBrokerTagLineHeight;
		}

		.name {
			font-weight: $ctaBrokerTagNameWeight;
		}
		.role {
			font-weight: normal;
		}
	}
}

.cta-broker-cpn.-home-hero-type {
	@media (max-width: 992px) {
		padding-top: 0;
		background: white;
	}

	.cta-broker-content {
		@media (max-width: 992px) {
			padding-top: 34px;
			padding-bottom: 60px;
		}
	}

	.background-ctn {
		position: relative;
		display: none;

		@media (max-width: 992px) {
			display: block;
		}
	}

	.cta-broker-img {
		@media (max-width: 992px) {
			display: none;
		}
	}

	.broker-tag {
		@media (max-width: 992px) {
			bottom: 15px;
			min-width: 220px;
		}
	}
}
