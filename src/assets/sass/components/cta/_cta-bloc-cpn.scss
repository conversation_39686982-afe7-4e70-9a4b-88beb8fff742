/*
	CTA Bloc Component
*/

.cta-bloc-cpn {
	background-size: cover;

	padding: 140px 0;

	@media (max-width: 768px) {
		padding: 70px 0;
	}

	.row {
		display: flex;
		align-items: center;
		justify-content: center;

		> div {

			@media (max-width: 768px) {
				margin: 0 15px;
			}
		}
	}

	.text-wrap {
		background-color: $white;

		.text-content {

			.title {
				margin-top: 0;
				text-transform: uppercase;
				color: $secondaryMainColor;

				.icon-eval-1,
				.icon-alerte-1{
					margin-right: 32px;
					display: inline-block;
					font-size: 60px;
				}
			}

			p {
				margin-bottom: 30px;
				font-size: $subTextSize;
				line-height: $subTextLineHeight;
				color: $secondaryMainColor;
			}
		}
	}

	&.cta-right{
		.text-content{
			padding: 0;
		}

		.img-ctn{
			position: relative;

			.caption{
				position: absolute;
				top: 0;
				right: 0;
				transform: translate(50%,-50%);
				display: flex;
    			align-items: center;
    			justify-content: center;
				border-radius: 50%;
				width: 140px;
				height: 140px;
				background-color: $complementaryEmphase;

				&:before{
					position: absolute;
					content: "";
					width: 160px;
					height: 160px;
					top: 50%;
					left: 50%;
					transform: translate(-50%,-50%);
					background-color: rgba($complementaryEmphase, 0.7);
					z-index: -1;
					border-radius: 50%;
				}

				span{
					text-align: center;
					color: white;
					text-transform: uppercase;
					font-size: 18px;
					line-height: 22px;
					font-weight: 500;
					font-family: $primaryFont;
				}
			}

			&:after{
				position: absolute;
				content: "";
				background-color: $secondaryMainColor;
				width: 60px;
				height: 60px;
				bottom: -10px;
				left: -10px;
				z-index: -1;
			}
		}

		.row{
			align-items: center;
			display: flex;
			flex-wrap: wrap;
		}

		@media(max-width: 992px){

			.img-ctn{
				max-width: 85%;
				margin: 0 auto 50px;

				.caption{
					width: 120px;
					height: 120px;

					span{
						font-size: 15px;
						line-height: 20px;
					}

					&:before{
						width: 130px;
						height: 130px;
					}
				}


				img{
					width: 100%;
				}
			}
		}

		@media(max-width: 768px){
			.img-ctn{
				.caption{
					transform: translate(10px, -40px);
				}
			}
		}

		@media(max-width: 425px){
			.img-ctn{
				.caption{
					width: 100px;
					height: 100px;

					&:before{
						width: 110px;
						height: 110px;
					}

					span{
						font-size: 13px;
						line-height: 16px;
					}
				}
			}
		}
	}

	&.cta-center{
		.row{
			justify-content: center;
			display: flex;
		}

		.text-wrap{
			background-color: $primaryColor;

			.text-content{
				text-align: center;
				.icon-eval-1,
				.icon-alerte-1{
					display: inline-block;
					font-size: 60px;
					color: white;
					margin-bottom: 15px;
				}
				.title{
					color: white;
				}

				p{
					color: white;
				}
			}
		}
	}

  &.cta-left {
    .container {
		padding-left: 0;
		padding-right: 0;

		@media (max-width: 768px) {
			padding-left: inherit;
			padding-right: inherit;
		}
    }

    .text-content {
		padding-top: 0;
		width: 80%;

		@media (max-width: 768px) {
			width: 100%;
		}
	}

		.img-ctn{
			position: relative;
			box-shadow: 0px 20px 50px 0px rgba(0, 0, 0, 0.15);
		}

    @media (max-width: 991px) {
      .row {
        flex-direction: column-reverse;
        display: flex;
        gap: 40px;
      }
    }
  }
}

.alert-cta-1-cpn {
	.img-ctn {
		padding-left: 0;
	}

	.title {
		margin-top: 0;
	}
}

.homestaging-cta-cpn {
	.text-wrap {
		padding-left: 0;
	}

	@media (max-width: 768px){
		.container {
			display: flex;
			flex-direction: column;
		}

		.img-ctn {
			order: 1;
			padding: 0 !important;
		}

		.text-wrap {
			order: 2;
		}
	}
}

