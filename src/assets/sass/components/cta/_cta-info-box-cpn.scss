$infoBoxBorder: 0;

$infoBoxTitleSize: 24px;
$infoBoxTitleColor: $secondaryMainColor;
$infoBoxTitleLineHeight: 29px;

$infoBoxDescriptionSize: 15px;
$infoBoxDescriptionColor: $secondaryMainColor;
$infoBoxDescriptionLineHeight: 24px;

.team-info-box{
    padding: 40px 30px;
    border: $infoBoxBorder;
    text-align: center;
    @include clearfix;
    position: relative;
    background-color: #F2F2F2;

    .team-info-box-content {
        margin: 0 auto;
        float: none;
    }

    .title {
        margin: 0;
        margin-bottom: 30px;
        color: $infoBoxTitleColor;
        font-family: $primaryFont;
        font-size: $infoBoxTitleSize;
        line-height: $infoBoxTitleLineHeight;
        text-transform: uppercase;
        font-weight: 600;
    }

    .description {
        margin-top: 0;
        margin-bottom: 30px;
        color: $infoBoxDescriptionColor;
        font-family: $secondaryFont;
        font-size: $infoBoxDescriptionSize;
        line-height: $infoBoxDescriptionLineHeight;
        text-align: center;
    }

    .main-button {
        min-width: 100%;

        @media (max-width: 992px) {
            min-width: inherit;
        }
    }

    &.-large{

        text-align: left;
        padding: 75px 45px;

        .team-info-box-content{
            display: flex;
            align-items: center;
            flex-wrap: wrap;
        }

        .title{
            margin-bottom: 20px;
        }

        .description{
            text-align: left;
            margin-bottom: 0;
        }

        @media (max-width:992px) {
            padding: 50px 20px;
        }

        @media (max-width:768px) {

            text-align: center;

            .team-info-box-content{
                display: block;
            }

            .description{
                text-align: center;
                margin-bottom: 20px;
            }
        }
    }
}
