.cta-evaluation-cpn {
	background-image: url("/assets/images/common/Estimation-immo-accueil-v2.webp");
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center center;
	min-height: 750px;
	position: relative;

	.text-ctn {
		width: 432px;
		height: fit-content;
		position: absolute;
		background-color: $primaryColor;
		padding: 45px;
		text-align: center;
		color: $white;
		right: 80px;
		bottom: 80px;

		.title {
			color: $white;
			line-height: normal;
		}

		p {
			margin: 30px 0;
			font-size: 17px;
			line-height: 28px;
		}

		.main-button {
			border-color: $white;
			color: $white;

			&:hover {
				background: $white;
				color: $primaryColor;
			}
		}
	}

	@media screen and (max-width: 768px) {
		min-height: auto;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 40px;

		.text-ctn {
			width: 100%;
			position: unset;
		}
	}

	@media screen and (max-width: 375px) {
		padding: 20px;
	}

	&.--second {
		background-image: url("/assets/images/common/cta-evaluation-bg-second.jpg");

		.text-ctn {
			width: 470px;
			right: 0;
			bottom: 0;
			top: 80px;
			left: 80px;
		}
	}
}
