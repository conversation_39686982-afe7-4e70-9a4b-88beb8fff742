// Plain full-width CTA with icon

$cta-alert-small-cpn-color: white;
$cta-alert-small-cpn-background: $primaryColor;

.cta-alert-small-cpn {
	background: $cta-alert-small-cpn-background;

	.container {
		@include flex(center, space-between);
		padding: 85px 0;

		.text-ctn {
			color: $cta-alert-small-cpn-color;
			max-width: 50%;

			.button-ctn {
				margin-top: 30px;
			}
		}

		.icon-ctn i {
			margin-left: auto;
			color: $cta-alert-small-cpn-color;
			font-size: 130px;
		}
	}
	
	@media (max-width: 768px) {
		.container {
			flex-direction: column-reverse;
			padding: 60px;
			text-align: center;

			.text-ctn {
				max-width: none;

				.button-ctn {
					width: max-content;
					margin: 40px auto 0;

					a.-white {
						padding: 0 30px;
					}
				}
			}
		}
	}
}
