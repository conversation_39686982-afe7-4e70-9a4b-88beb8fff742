.reviews-slider-cpn {
	background-color: #F2F2F2;
	padding:          100px 0 170px;

	.reviews-list-ctn {
		overflow: 	   hidden;
	}

	@media (max-width: 992px) {
		padding: 50px 0 100px;
	}

	.container { position: relative; }

	.rating-stars {
		display: flex;

		.fa-star {
			color: #ccc;
			margin-right: 2px;

			&.checked {
				color: #ffcc00;
			}
		}

		&.--head {
			font-size: 26px;
			margin-bottom: 50px;
			margin-top: 15px;
			justify-content: center;
		}
	}

	.title {
		color: $secondaryMainColor;
		text-align: center;
		font-family: $primaryFont;
		font-style: normal;
		text-transform: uppercase;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 30px;
		margin: 0;

		@media (max-width: 992px) {
			font-size: 24px;
			gap: 15px;
		}
	}

	.review {
		background-color: $white;
		padding: 32px;
		width: 33.33%;
		height: 355px;

		&-header {
			display: flex;
			align-items: center;
			margin-bottom: 20px;
			gap: 20px;

			&-left {
				display: flex;
				align-items: center;

				.reviewer-image {
					width: 80px;
					height: 80px;
					border-radius: 100px;
				}
			}

			&-right {
				.reviewer-name {
					font-size: 18px;
					font-style: normal;
					font-weight: 500;
					line-height: normal;
					margin-bottom: 8px;
					color: $secondaryMainColor;
					text-transform: capitalize;
				}

				.reviewer-rating {
					display: flex;
					align-items: center;
					margin-bottom: 8px;

					.rating-value {
						font-size: 18px;
					}
				}

				.reviewer-date {
					color: #777;
					font-size: 13px;
					font-style: normal;
					font-weight: 400;
					line-height: 19.6px;
				}
			}
		}

		&-body {

			.review-text {
				color: $secondaryMainColor;
				font-size: 15px;
				font-style: normal;
				font-weight: 400;
				line-height: 24px;
			}
		}
	}

	.swiper-btn {
		background: none;
		width: 18px;
		height: 30px;
		transform: translate(0%, calc(-50% + 60px));
		margin-top: 0;

		&:before {
			position: absolute;
			font-family: 'icomoon' !important;
			speak: none;
			font-style: normal;
			font-weight: normal;
			font-variant: normal;
			text-transform: none;
			line-height: 1;
			font-size: 28px;
			color: $secondaryMainColor;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
		}

		&:after {
			display: none;
		}

		@media (max-width: 992px) {
			display: none;
		}

		&.swiper-button-prev {
			left: -30px;

			&:before {
				content: "\e909";
			}
		}

		&.swiper-button-next {
			right: -30px;

			&:before {
				content: "\e90a";
			}
		}
	}

	.swiper-pagination {
		bottom: -70px;
		left: 50%;
		transform: translate(-50%, -50%);

		.swiper-pagination-bullet {
			margin-right: 10px;
			background: rgba(0, 0, 0, 0.2);
			width: 11px;
			height: 11px;

			&-active {
				background: $primaryColor;
			}

			&:last-child {
				margin-right: 0;
			}
		}
	}
}

