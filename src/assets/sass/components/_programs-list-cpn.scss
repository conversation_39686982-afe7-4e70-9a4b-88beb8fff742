.prog-list-ctn{
  background-color: white;
  padding-bottom: 200px;

  .prog-illu{
    width: 100%;
    background-color: #0F4584;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    &:before{
      position: absolute;
      content: "";
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
    }

    img{
      z-index: 2;
    }

    &.-red{
      background-color: #B3222E;

      &:before{
        opacity: 0.2;
      }
    }

    &.-blue-dark{
      background-color: #0A2C59;

      &:before{
        background-size: 55px;
      }
    }
  }

  .article{
    box-shadow: none;
    .article-info{
      padding: 30px 0 35px;
      .title{
        text-transform: uppercase;
        margin-bottom: 20px;
      }
    }
  }
}
