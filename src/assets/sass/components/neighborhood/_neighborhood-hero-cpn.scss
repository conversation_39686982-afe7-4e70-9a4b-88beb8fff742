.neighborhood-hero-cpn {
	@media (min-width: 1024px){
		min-height: 450px;
	}


	.filter {
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 2;
		background: rgba(0,0,0,0.2);
		opacity: 1;
	}

	.container {
		z-index: 3;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		height: auto;
		bottom: inherit;



		.inner{
			max-width: 600px;
			background-color: transparent;
			text-align: center;
			margin: 0 auto;

			.name{
				color: white;
				font-family: $primaryFont;
				font-size: 42px;
				line-height: 49px;
				margin-top: 0;
				margin-bottom: 0;
				text-transform: uppercase;
			}

			.open-video{
				font-size: 60px;
				color: white;
				text-align: left;
				margin: 0;
				margin-top: 30px;

				span{
					transition: all 0.5s ease-in-out;
					display: inline-block;

					&:hover{
						cursor: pointer;
						transform: scale(1.3);
					}
				}
			}

			@media (max-width:992px) {
				padding: 30px 50px;

				.name{
					font-size: 30px;
				}
			}

			@media (max-width:768px) {
				display: none;
			}

		}


	}

	.swiper-slide{

		@media (max-width:425px) {
			max-height: 250px;
		}
	}
}

.full-screen-cpn{
	.video-ctn{
		z-index: 2;

		@media (max-width: 768px){
			width: 100%;
		}

		.video-inner{
			position: relative;
			padding-bottom: 56.2%;
			height: 0;
			z-index: 2;


			iframe{
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
			}
		}


	}
}
