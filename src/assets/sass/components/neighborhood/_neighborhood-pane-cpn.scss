.neighborhood-pane-cpn {
	padding-top: 60px;
	padding-bottom: 60px;
	background-color: $paleBackground;
	display: block;
	width: 35%;
	position: relative;
    margin: 0;
    z-index: 10;
    min-height: 600px;
	margin-left: -35%;

	@media (max-width: 1100px){
		width: 45%;
		margin-left: -45%;
	}

	@media (max-width: 992px){
		display: none;
		width: 100%;
		margin-left: 0;

		&.show {
			display: block;
		}
	}

	.dropdown { 
		padding-top: 40px;
	}

	.toggle-pane {
		position: sticky;
		top: 185px;
		right: 0px;
		margin-right: -45px;
		margin-top: 20px;
		float: right;
		display: block;
		width: 30px;
		line-height: 60px;
		text-align: center;
		background-color: $primaryColor;
		color: white;
		transition: $primaryAnimation;
		cursor:pointer;
		opacity: 1;
		transition: $primaryOpacityAnimation;

		div {
			transform: rotate(180deg);
		}

		&:hover { background-color: rgba($primaryColor, .60); }

		&.selected {
			pointer-events: none; 
			background-color: rgba(153,153,153,0.7);
		}

		@media (max-width: 992px) {
			display: none;
		}
	}

	&.open {
		margin: 0;

		div {
			transform: rotate(0);
		}
	}

	.pane-content-ctn {
    	max-width: 100%;
		padding: 0 25px;

		@media (max-width: 420px){
			padding: 0;
		}

		.pane-title {
			font-family: $primaryFont;
			font-size: 28px;
			line-height: 34px;
		}

		.info-ctn {
			border-bottom: none;

			.sub-section-title {
				font-size: 14px;
				line-height: 24px;
				color: #999999;
			}
		}

		.selection-list {
			max-height: 100%;
	  
			.neighborhood-card {
			  display: flex;
			  margin-bottom: 15px;
			  overflow: hidden;
			  align-items: center;
			  z-index: 1;
			  position: relative;
			  height: 60px;
			  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.1);
			  @include transition(all 0.3s ease-in);
	  
			  img {
				min-width: 100px;
				max-width: 100px;
				width: 100px;
				height: 100%;
				object-fit: cover;
				z-index: 0;
				@include transition(all 0.3s ease-in);
			  }
	  
			  .link-ctn {
				background-color: $white;
				height: 100%;
				width: 100%;
				z-index: 99;
			  }

			  p {
				display: flex;
				align-items: center;
				font-size: 1rem;
				color: $primaryColor;
				font-weight: 600;
				padding: 0 20px 0 20px;
				width: 100%;
				height: 100%;
				margin: 0;
				z-index: 0;
				@include transition(all 0.3s ease-in);
			  }
	  
			  &:hover {
				cursor: pointer;
				p {
				  background-color: $primaryColor;
				  color: $white;
				  @include transition(all 0.3s ease-in);
				}
				img {
				  transform: scale(1.1);
				  opacity: 0.7;
				  @include transition(all 0.3s ease-in);
				}
			  }
	  
			  &.active {
				p {
				  color: $white;
				  background-color: $primaryColor;
				}
				img {
				  transform: scale(1.1);
				}
			  }
			}
		  }
	}
}