.neighborhood-highlights-cpn {
	padding: 80px 0;

	.neighborhood-description {
		.title {
			margin-top: 0;
			color: $secondaryMainColor;
			font-family: $primaryFont;
		}

		.main-inscription {
			p {
				color: $secondaryMainColor;
				font-family: $secondaryFont;
				font-size: $subTextSize;
				line-height: $subTextLineHeight;
			}
		}

		.emphase-title {
			margin-top: 54px;
		}
	}

	.small-list {
		color: $secondaryMainColor;
		font-family: $secondaryFont;
		font-size: $subTextSize;
		line-height: $subTextLineHeight;
		display: flex;
		flex-wrap: wrap;
		padding: 0;

		li {
			position: relative;
			width: 100%;
			font-weight: 600;
			font-size: 14px;
			line-height: 20px;
			color: $secondaryMainColor;
			padding: 10px 30px;
			margin-bottom: 20px;
			background-color: #f4f4f4;
		}
	}

	.blocks-ctn {
		margin-top: 60px;

		@media (max-width: 992px) {
			margin-top: 30px;
		}

		.block {
			margin-bottom: 30px;
			display: flex;
			align-items: flex-start;
			justify-content: center;
			padding-bottom: 40px;
			border-bottom: solid 1px #DCDCDC;

			&:last-child{
				padding-bottom: 0;
				border: 0;
			}


			@media (max-width: 992px) {
				justify-content: flex-start;
			}

			.icon-ctn {
				padding-right: 30px;

				i {
					font-size: 35px;
					color: $secondaryMainColor;
				}
			}

			.title {
				margin: 0;
				color: $secondaryMainColor;
				font-family: $primaryFont;
				font-size: $primaryTextSize;
				font-weight: 400;
				line-height: 28px;
			}

			.description {
				margin-top: 7px;
				margin-bottom: 0;
				color: $secondaryMainColor;
				font-family: $secondaryFont;
				font-size: 15px;
				line-height: $detailTextLineHeight;
			}
		}
	}

	@media (max-width:425px) {
		padding: 40px 0 80px;
	}
}
