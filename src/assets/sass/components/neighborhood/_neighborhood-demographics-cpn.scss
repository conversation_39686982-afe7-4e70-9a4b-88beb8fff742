.neighborhood-demographics-cpn {
	padding-top: 30px;
	background: $footerMainBackground;

	.single-title {
		text-transform: uppercase;
	}

	.chart-wrap {
		display: flex;
		flex-flow: row wrap;

		.block {
			width: calc(100% * 1/2 - 20px);
			margin-right: 40px;
			margin-bottom: 40px;

			@media (max-width: 768px) {
				margin-right: 0px;
				width: 100%;

				&:nth-child(4n) {
					margin-bottom: 0px;
				}
			}

			&:nth-child(2n) {
				margin-right: 0px;
			}
		}
	}

	.chart-wrap {
		max-width: 1070px;
		margin: 0 auto;
		@include clearfix();
	}

	.chart-ctn {
		text-align: center;
		background: white;
		padding: 80px 40px 60px 40px;

		@media (max-width: 992px) {
			padding: 60px 20px 40px 20px;
		}

		canvas {
			transform: scale(1.2);

			@media (max-width: 768px) {
				transform: none;
			}
		}

		.label {
			padding-top: 20px;
			color: $secondaryMainColor;
			font-family: $secondaryFont;
			font-size: 14px;
			font-weight: 600;
			line-height: 19px;
		}
	}

	.stats-ctn {
		display: flex;
		align-items: center;
		justify-content: center;
		background: white;

		@media (max-width: 768px) {
			display: block;
			padding: 20px 30px;
		}
	}

	.stats {
		display: flex;
		align-items: center;
		justify-content: space-between;
		min-width: 320px;
		border-bottom: 1px solid #D8D8D8;

		&:last-child {
			border: none;
		}

		@media (max-width: 992px) {
			max-width: none;
			min-width: 0px;
		}

		p {
			margin: 30px 0;
		}

		.name {
			color: $primaryColor;
			font-family: $secondaryFont;
			font-size: 14px;
			line-height: 19px;
		}

		.value {
			color: $secondaryMainColor;
			font-family: $secondaryFont;
			font-size: 18px;
			font-weight: 600;
			line-height: 24px;
			text-align: right;
		}
	}
}
