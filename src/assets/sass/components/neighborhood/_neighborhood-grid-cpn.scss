.neighborhood-grid-cpn {
  padding: 110px 0 240px;

  @media (max-width: 768px) {
    padding: 60px 40px 100px;
  }

  @media (max-width: 425px) {
    padding: 30px 20px 40px;
  }

  .page-head {
    text-align:    center;
    margin-bottom: 110px;

    @media (max-width: 768px) {
      margin-bottom: 60px;
    }

    @media (max-width: 425px) {
      margin-bottom: 30px;
    }

    .title {
      text-transform: uppercase;
      color:          $primaryColor;
      margin:         0 0 30px;
    }

    p {
      margin:      0 auto;
      color:       $primaryColor;
      font-size:   22px;
      font-weight: 400;
      line-height: 32px;
      width: 70%;

        @media (max-width: 768px) {
            width: 100%;
        }
    }
  }

  .nbh-grid {
    display:               grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap:                   120px 40px;
    padding:               0 40px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr 1fr;
      gap:                   60px 20px;
    }

    @media (max-width: 425px) {
      grid-template-columns: 1fr;
      gap:                   20px 10px;
    }
  }

  .nbh-item {
    @include flex(start, space-between, column);

    .item-img {
      position: relative;
      height:   300px;
      overflow: hidden;

      img {
        @include img();
        transition: 0.4s all ease;
      }

      // Darker overlay
      &::after {
        @include pseudoElement();
        top:              0;
        width:            100%;
        height:           100%;
        background-color: black;
        opacity:          0;
        transition:       0.4s all ease;
        pointer-events:   none;
      }
    }

    .item-title {
      @include fontSize(24px, 30px, 600);
      text-transform: uppercase;
      color:          $primaryColor;
      margin:         25px auto;
    }

    p {
      color: $primaryColor;
      @include fontSize(15px, 25px, 400);
    }

    .main-button {
      margin-top: 20px;
      width:      100%;
    }

    &:hover {
      .item-img {
        img {
          transform: scale(1.06);
        }

        &::after {
          opacity: 0.2;
        }
      }

      cursor: pointer;
    }
  }

  .loading {
    padding: 60px 0 100px;
  }

  @media (max-width: 991px) {
    .page-head {
      max-width: none;

      .title {
        @include fontSize(60px, 66px);
      }
    }

    .nbh-grid {
      grid-template-columns: 1fr 1fr;
    }
  }

  @media (max-width: 600px) {
    .page-head {
      .title {
        margin: 70px 0 40px;
        @include fontSize(44px, 50px);
      }
    }

    .nbh-item {
      .main-button {
        width: 100%;
      }
    }

    .nbh-grid {
      grid-template-columns: 1fr;
    }
  }
}
