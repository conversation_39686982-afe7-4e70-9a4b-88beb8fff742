.text-card-1-cpn{
	padding: 80px 0;

	&.-grey {
		background-color: #F6F6F6;
	}

	&.alert-cta-1-cpn {
		.container {
			.text-ctn {
				width: 100%;
				max-width: 60%;
				padding: 50px 0 50px 110px;

				@media (max-width: 992px) {
					max-width: 50%;
					padding-left: 40px;
				}

				@media (max-width: 768px) {
					max-width: none;
					padding: 10px 30px;
				}
			}
		}
	}

	.container {
		@include flex(center, center);

		.text-ctn {
			p {
				margin-bottom: 30px;
				color: $secondaryMainColor;
				font-family: $secondaryFont;
				font-size: $subTextSize;
				line-height: $subTextLineHeight;
			}
		}

		.img-ctn {
			text-align: center;
		}
	}

	@media (max-width: 768px) {
		display: block;
		padding: 60px 0;

		.container {
			flex-direction: column;

			.text-ctn {
				margin-top: 40px;
				padding: 0;
				text-align: center;

				.main-button {
					margin: auto;
				}
			}

			&.homestaging-cta-cpn {
				.text-ctn {
					margin-bottom: 20px;
				}
			}
		}
	}
}
