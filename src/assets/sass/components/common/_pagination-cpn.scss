.paginiation-controls {
	.ngx-pagination {
    @include flex();
    gap: 5px;
		margin: 50px auto;
		padding: 0;
		text-align: center;

		li {
      width: 40px;
      height: 40px;
      padding: 0;
			border: 1px solid $primaryColor;
			border-radius: 3px;
          font-family: $primaryFont;

      > span, > a {
        display: block;
        padding: 0;
        font-size: 16px;
        line-height: 36px;
        font-weight: 600;
        color: $primaryColor;
      }

      >a { height: 100%; }

			&.disabled { display: none; }

			&.current {
				background: $primaryColor;
        border-color: $primaryColor;
				span { color: white; }
			}
		}

		a:hover { background-color: rgba($primaryColor, 0.2); }

		.pagination-previous, .pagination-next {
			border: 2px solid $primaryColor;

      a {
        height: 100%;

        &:after, &::before {
          position: relative;
          top: -1px;
          margin: 0;
          font-family: 'icomoon';
          font-size: 14px;
          color: $primaryColor;
        }
      }
    }

		.pagination-previous a::before { content: "\e909"; }
		.pagination-next a::after { content: "\e90a"; }
	}
}
