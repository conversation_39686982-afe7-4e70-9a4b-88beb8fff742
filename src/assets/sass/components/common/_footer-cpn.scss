/*
	Footer
*/

$footerMainBackground: #F2F2F2;
$footerMainPadding: 67px 0px 87px 0px;

$footerMainSubSectionLinkColor: $secondaryMainColor;
$footerMainSubSectionLinkFont: $primaryFont;
$footerMainSubSectionLinkSize: 16px;
$footerMainSubSectionLinkWeight: 600;
$footerMainSubSectionLinkLineHeight: 22px;
$footerMainSubSectionLinkTextTransform: uppercase;

$footerMainItemLinkColor: $secondaryMainColor;
$footerMainItemLinkFont: $secondaryFont;
$footerMainItemLinkSize: 14px;
$footerMainItemLinkLineHeight: 19px;

$footerMainDescriptionSize: 14px;
$footerMainDescriptionFont: $secondaryFont;
$footerMainDescriptionColor: $secondaryMainColor;
$footerMainDescriptionWeight: 400;
$footerMainDescriptionLineHeight: 17px;

$footerMainInfoColor: $secondaryMainColor;
$footerMainInfoFont: $primaryFont;
$footerMainInfoSize: 14px;
$footerMainInfoLineHeight: 19px;

$footerMainInfoPhoneColor: $secondaryMainColor;
$footerMainInfoPhoneFont: $primaryFont;
$footerMainInfoPhoneSize: 20px;
$footerMainInfoPhoneWeight: bold;
$footerMainInfoPhoneLineHeight: 24px;

$footerMainLogoHeight: 81px;

$subFooterMainBackground: #F2F2F2;
$subFooterHeight: 48px;
$subFooterBorderTop: 0px;
$subFooterBorderTopColor: rgba(255, 255, 255, 0);

$subFooterCopyrightColor: $secondaryMainColor;
$subFooterCopyrightFont: $secondaryFont;
$subFooterCopyrightSize: 12px;
$subFooterCopyrightLineHeight: 17px;

$subFooterShareColor: $secondaryMainColor;
$subFooterShareColorHover: $secondaryComplementaryColor;

#footer {
	position: relative;

	.main-footer {
		background-color: $footerMainBackground;
	}

	.sub-footer {
		background-color: $subFooterMainBackground;
		border-top: solid $subFooterBorderTop $subFooterBorderTopColor;
		padding-top: 15px;
	}

	.footer-ctn {
		padding: $footerMainPadding;
		margin-top: 0;

		@media (max-width: 992px) {
			padding: 40px 20px;
		}

		> div {
			@media (max-width: 992px) {
				display: none;

				&.info-section {
					display: block;
					width: 100%;
				}
			}
		}

		.sub-section-link {
			margin-bottom: 17px;

			> a,span {
				color: $footerMainSubSectionLinkColor;
				font-family: $footerMainSubSectionLinkFont;
				font-size: $footerMainSubSectionLinkSize;
				font-weight: $footerMainSubSectionLinkWeight;
				line-height: $footerMainSubSectionLinkLineHeight;
				text-transform: $footerMainSubSectionLinkTextTransform;
			}

			&.-second {
				margin-top: 40px;
			}
		}

		.item-link {
			> a {
				color: $footerMainItemLinkColor;
				font-family: $footerMainItemLinkFont;
				font-size: $footerMainItemLinkSize;
				line-height: $footerMainItemLinkLineHeight;
			}
		}



		.logo-ctn {
			img {
				height: $footerMainLogoHeight;
			}
		}
	}

	.info-section {
		text-align: center;

		@media (max-width: 450px) {
			padding: 0 15px;
		}

		.description {
			margin-top: 23px;
			color: $footerMainDescriptionColor;
			font-family: $footerMainDescriptionFont;
			font-size: $footerMainDescriptionSize;
			font-weight: $footerMainDescriptionWeight;
			line-height: $footerMainDescriptionLineHeight;

			a{
				color: $footerMainDescriptionColor;
			}
		}

		.info-ctn {
			padding-top: 15px;
			display: flex;
			flex-wrap: wrap;
			justify-content: center;
			position: relative;
			padding-bottom: 40px;

			@media (max-width: 450px) {
				justify-content: space-between;
			}

			&:after{
				position: absolute;
				content: "";
				width: 60px;
				height: 4px;
				background-color: $secondaryMainColor;
				left: 50%;
				transform: translateX(-50%);
				bottom: 0;
			}

			> div {
				display: flex;
				align-items: center;
				margin-right: 30px;

				@media (max-width: 450px) {
					margin-right: 0;
				}

				&:last-child{
					margin-right: 0;
				}
			}

			.icon {
				color: $footerMainInfoColor;
				padding-right: 15px;
			}

			.icon-pin {
				font-size: 23px;
				padding-right: 9px;
				position: relative;
				left: -3px;
			}

			p, a{
				color: $footerMainInfoColor;
				font-family: $footerMainInfoFont;
				font-size: $footerMainInfoSize;
				line-height: $footerMainInfoLineHeight;
				font-weight: 700;

				&.icon-logo-facebook,&.icon-logo-instagram{
					font-family: 'icomoon';
				}
			}

			.mail-ctn {
				margin-top: 15px;
			}

			.phone-ctn {
				margin-top: 15px;

				> a, > p {
					color: $footerMainInfoPhoneColor;
					font-size: $footerMainInfoPhoneSize;
					font-weight: $footerMainInfoPhoneWeight;
					line-height: $footerMainInfoPhoneLineHeight;
				}
			}
		}
	}

	.sub-footer-ctn {
		height: $subFooterHeight;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 30px 0;

		@media (max-width: 992px) {
			height: auto;
			flex-direction: column;
			padding: 0 20px;
		}

		p {
			margin: 0;
		}

		.copyright{
			display: flex;
			align-items: center;

			@media (max-width: 992px) {
				display: block;
				padding-top: 10px;
				padding-bottom: 20px;
				text-align: center;
			}

			p, a {
				margin-right:0;
				color: $subFooterCopyrightColor;
				font-family: $subFooterCopyrightFont;
				font-size: $subFooterCopyrightSize;
				line-height: $subFooterCopyrightLineHeight;

				@media (max-width: 992px) {
					margin-bottom: 10px;
					margin-right: 0;
				}
			}

			.privacy{
				margin: 10px 40px;
				a {
					color: $subFooterCopyrightColor;
					font-family: $subFooterCopyrightFont;
					font-size: $subFooterCopyrightSize;
					line-height: $subFooterCopyrightLineHeight;
					transition: all .2s ease-in-out;

					&:hover{
						opacity: 0.7;
					}
				}
			}
		}

		.share-ctn {
			@media (max-width: 992px) {
				padding-bottom: 15px;
			}

			ul {
				margin: 0;
				padding-left: 0;
				display: flex;

				li {
					margin-right: 25px;

					&:last-child {
						margin-right: 0;
					}
				}

				a {
					display: inline-block;
					color: $subFooterShareColor;
					transition: $primaryTextAnimation;
					font-size: 18px;

					&:hover {
						color: $subFooterShareColorHover;
					}
				}
			}
		}
	}
}
