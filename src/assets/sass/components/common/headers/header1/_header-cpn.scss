/*
	Sub Header Component
*/
$subHeaderHeight1: 45px;
$subHeaderBG1: white;
$subHeaderLinkSize1: 14px;
$subHeaderLinkColor1: $secondaryMainColor;

$subHeaderFavoriteBorderLeft1: 0;
$subHeaderFavoritePadding1: 0 20px;
$subHeaderFavoriteSize1: 14px;

$subHeaderNumberBG1: transparent;
$subHeaderNumberBGHover1: transparent;
$subHeaderNumberBorderLeft1: 0;
$subHeaderNumberPadding1: 0 15px;
$subHeaderNumberColor1: $secondaryMainColor;
$subHeaderNumberSize1: 22px;
$subHeaderNumberWeight1: 700;
$subHeaderNumberFont1: $primaryFont;
$subHeaderNumberOpacity1: 1;
$subHeaderNumberPhoneBorder1: none;
$subHeaderNumberPhoneRadius1: 0px;

/*
	Header Component
*/
$headerHeight1: 100px;
$headerBG1: $white;
$headerBorderBottom1: none;
$headerLinkPaddingRight1: 35px;
$headerLinkSize1: 15px;
$headerLinkFont1: $primaryFont;
$headerLinkWeight1: 600;
$headerLinkLineHeight1: 17px;
$headerLinkColor1: $secondaryMainColor;
$headerLinkTransform1: uppercase;
$headerSubMenuBG1: #eee;
$headerSubMenuLinkBGHover1: transparent;
$headerSubMenuLinkColor1: $secondaryMainColor;
$headerSubMenuLinkColorHover1: $secondaryMainColor;
$headerSubMenuLinkLineHeight1: 20px;
$headerSubMenuLinkSize1: 14px;
$headerSubLinkTransform1: none;

/*
	Transparent Header
*/
$transparentSubHeaderLinkColor1: $white;
$transparentSubHeaderNumberColor1: $white;
$transparentHeaderLinkColor1: $white;

/*
	Special Color Header
*/
$specialSubHeaderLinkColor1: blue;
$specialSubHeaderNumberColor1: blue;
$specialHeaderLinkColor1: blue;

.overwriteAbsolute {
	#header {
		position: absolute;
		width: 100%;
		left: 0;
		top: 0;

		.sub-header,.main-header {
			background: transparent;

			> .sub-head-menu {
				a {
					color: $white!important;
				}
			}

			.item-menu > a {
				color: $white!important;
			}
		}

		.header-menu-toggle span {
			background-color: $white!important;
		}

		.logo-ctn {
			display: none;

			&.--white {
				display: block;
			}
		}
	}
}

#header {
	position: relative;
	z-index: 200;
	border-bottom: $headerBorderBottom1;

	.sub-header {
		background: $subHeaderBG1;
		padding: 10px 15px 0;
		@include clearfix;

		ul {
			float: right;
			margin: 0;
			color: $subHeaderLinkColor1;
			line-height: $subHeaderHeight1;
			display: flex;
			padding-left: 0;

			li {
				display: inline-block;

				&:first-child {
					padding-right: 15px;
				}

				@media (max-width: 992px) {
					&:first-child {
						padding-right: 10px;
					}
				}

				a {
					color: $subHeaderLinkColor1;
					font-size: $subHeaderLinkSize1;
				}
			}
		}

		.english-fake {
			display: none;
		}

		.switch {
			padding-right: 10px;

			&:hover {
				cursor: pointer;
			}
		}

		.favorite {
			padding: $subHeaderFavoritePadding1;
			border-left: $subHeaderFavoriteBorderLeft1;

			a {
				font-family: $primaryFont;
				font-size: $subHeaderFavoriteSize1;
				font-weight: $primaryBold;
				display: flex;
				align-items: center;

				i {
					margin-right: 10px;
					font-size: 20px;
				}
			}
		}

		.number-ctn {
			background: $subHeaderNumberBG1;
			padding: $subHeaderNumberPadding1;
			border-left: $subHeaderNumberBorderLeft1;
			transition: $primaryAnimation;

			&:hover {
				background: $subHeaderNumberBGHover1;
				color: $primaryButtonTextColorHover;
			}

			a {
				color: $subHeaderNumberColor1;
				font-size: $subHeaderNumberSize1;
				line-height: 27px;
				font-weight: $subHeaderNumberWeight1;
				font-family: $subHeaderNumberFont1;
				opacity: $subHeaderNumberOpacity1;

				i {
					margin-right: 10px;
					font-size: $subTextSize;
					border: $subHeaderNumberPhoneBorder1;
					border-radius: $subHeaderNumberPhoneRadius1;
				}
			}
		}

		.share-ctn {
			padding-left: 30px;
			padding-right: 30px;

			@media (max-width: 992px) {
				display: none;
			}

			ul {
				margin: 0;
				padding-left: 0;
				display: flex;
				align-items: center;

				li {
					margin-right: 15px;
					padding-right: 0;

					&:last-child {
						margin-right: 0;
					}
				}

				a {
					display: inline-block;
					font-size: 20px;
					color: $secondaryMainColor;
					transition: $primaryTextAnimation;

					&:hover {
						color: darken($color: $secondaryMainColor, $amount: 10);
					}
				}
			}
		}
	}
	.main-header {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		height: $headerHeight1;
		padding: 17px 40px;
		background-color: $headerBG1;

		@media (max-width: 1095px) {
			padding: 17px 20px;
		}

		.special-logo {
			fill: $black;
			width: 67px;
			max-height: 55px;
		}

		.main-menu-ctn {
			@media (max-width: 992px) {
				display: none;
			}

			.main-menu {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding-left: 0;
				display: block;
				list-style-type: none;
				position: relative;
				z-index: 1;
				margin: 0;
				flex-flow: row wrap;
				background: transparent;

				.item-menu {
					position: relative;
					display: inline-block;
					padding-right: $headerLinkPaddingRight1;

					@media (max-width: 1300px) {
						padding-right: 25px;
					}

					@media (max-width: 1200px) {
						padding-right: 15px;
					}

					&:last-child {
						padding-right: 0;
					}

					&.active-child {
						> a {
							background: $headerSubMenuLinkBGHover1;
							color: $headerSubMenuLinkColorHover1;
							opacity: 0.5;
						}
					}

					.secondary-ul {
						background: $headerSubMenuBG1;
						margin: 0px;
						width: 250px;
						padding-left: 0;
						padding-top: 15px;
						padding-bottom: 15px;
						left: 0;
						position: absolute;
						z-index: -1000;
						opacity: 0;
						display: none;

						&:before {
							content: "";
							width: 0;
							height: 0;
							border-left: 10px solid transparent;
							border-right: 10px solid transparent;
							border-bottom: 10px solid $headerSubMenuBG1;
							position: absolute;
							top: -10px;
							left: 15px;
						}
					}

					> a {
						font-family: $headerLinkFont1;
						font-size: $headerLinkSize1;
						color: $headerLinkColor1;
						font-weight: $headerLinkWeight1;
						line-height: 66px;
						text-transform: $headerLinkTransform1;
						opacity: 1;
						@include transition(opacity 0.3s ease-in);

						i {
							margin-left: 10px;
							font-size: 11px;
						}

						@media (max-width: 1200px) {
							font-size: 13px;
						}

						@media (max-width: 1095px) {
							font-size: 11px;
						}

						&.active {
							opacity: 0.5;
						}
					}

					&:hover {
						cursor: pointer;

						> a {
							opacity: 0.5;
						}

						.secondary-ul {
							z-index: 200;
							opacity: 1;
							transition: z-index 0.5s step-start, opacity 0.5s ease;
							display: block;

							li {
								display: block;

								a {
									display: block;
									font-family: $headerLinkFont1;
									font-size: $headerSubMenuLinkSize1;
									color: $headerSubMenuLinkColor1;
									font-weight: $headerLinkWeight1;
									line-height: $headerSubMenuLinkLineHeight1;
									text-transform: $headerSubLinkTransform1;
									padding: 10px 30px 10px 30px;

									&.active,
									&:hover {
										background: $headerSubMenuLinkBGHover1;
										color: $headerSubMenuLinkColorHover1;
										opacity: 0.5;
									}
								}
							}
						}
					}
				}
			}
		}
	}

	.header-menu-toggle {
		display: none;
		position: relative;
		right: 0;
		width: 30px;
		height: 25px;
		transition: all 0.5s ease;

		@media (max-width: 992px) {
			display: inline-block;
		}

		span {
			width: 100%;
			height: 3px;
			background-color: $headerSubMenuLinkColorHover1;
			position: absolute;
			top: 0;
			right: 0;
			left: 0;
			-ms-filter: none;
			-webkit-filter: none;
			filter: none;
			opacity: 1;
			-webkit-transform-origin: center;
			-ms-transform-origin: center;
			transform-origin: center;
			transition: all 0.25s ease-in-out;

			&:nth-child(2) {
				top: 10px;
			}

			&:nth-child(3) {
				top: 20px;
			}
		}
	}

	&.transparent {
		.sub-header {
			background: transparent;

			ul {
				li {
					a {
						color: $transparentSubHeaderLinkColor1;
					}
				}
			}

			.number-ctn {
				a {
					color: $transparentSubHeaderNumberColor1;
				}
			}
		}
		.main-header {
			background: transparent;

			.main-menu-ctn {
				.main-menu {
					.item-menu {
						> a {
							color: $transparentHeaderLinkColor1;
						}
					}
				}
			}
		}
	}

	&.special {
		.sub-header {
			background: transparent;

			ul {
				li {
					a {
						color: $specialSubHeaderLinkColor1;
					}
				}
			}

			.number-ctn {
				a {
					color: $specialSubHeaderNumberColor1;
				}
			}
		}
		.main-header {
			background: transparent;

			.main-menu-ctn {
				.main-menu {
					.item-menu {
						> a {
							color: $specialHeaderLinkColor1;
						}
					}
				}
			}
		}
	}

	.logo-ctn {
		height: 74px;
		display: flex;
		align-items: center;
		position: absolute;
		top: calc(50% - 37px);
		left: 40px;

		@media (max-width: 768px) {
			height: auto;
			top: calc(100% - 55px);
			left: 20px;
			transform: translate(0%, -50%);
		}

		&.--white {
			display: none;
		}

		> a {
			height: 74px;
			display: flex;
			align-items: center;

			@media (max-width:425px) {
				height: 45px;
			}
		}

		img {
			max-height: 100%;
		}

		.special-logo {
			margin-left: 25px;
		}
	}
}

//Opened Menu
body {
	&.-open-menu {
		#header {
			.header-menu-toggle {
				height: 25px;

				span {
					&:first-child {
						transform: rotate(45deg);
						top: 12px;
					}

					&:nth-child(2) {
						opacity: 0;
					}

					&:nth-child(3) {
						transform: rotate(-45deg);
						top: 12px;
					}
				}
			}
		}
	}
}
