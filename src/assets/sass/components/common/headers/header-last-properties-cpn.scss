$headerSubMultiColumnWidth: 440px;

$lastPropertiesBorder: 1px solid rgba(#000, 0.2);
$lastPropertiesTitle: $white;
$lastPropertiesName: $secondaryMainColor;
$lastPropertiesDescription: #353f46;

#header,
#header2 {
    .main-header {
        .main-menu-ctn {
            .main-menu {
                .item-menu:hover {
                    .secondary-ul {
                        pointer-events: all !important;
                    }
                }

                .secondary-ul.multi-column {
                    display: flex;
                    width: $headerSubMultiColumnWidth;
                    flex-direction: column;
                    padding: 0;
                    transition: none !important;
                    pointer-events: none !important;
    
                    @media (max-width: 1200px) {
                        width: 360px;
                    }
        
                    @media (max-width: 1100px) {
                        width: 290px;
                    }
            
                    &:hover {
                        pointer-events: auto !important;
                    }
                
                    ul {
                        padding: 0;

                        &:first-child {
                            margin-bottom: 10px;
                        }
                    }
    
                    .header-last-properties {
                        border-top: $lastPropertiesBorder;
    
                        &__title {
                            margin: 20px 30px 0;
                            opacity: 0.6;
                            font-size: 14px;
                            text-transform: uppercase;
                            // color: $lastPropertiesTitle;
                        }
    
                        &__list {
                            margin: 0;
                            padding: 0 30px 12px;
                
                            li {
                                display: flex;
                                padding: 10px 0;
                                color: $subHeaderBG2;
                                border-bottom: $lastPropertiesBorder;
                    
                                &:last-child {
                                    border: none;
                                }
                
                                .image-ctn {
                                    display: flex;
                                    background-color: $black;
                                    margin: auto 0;
                                    width: 85px;
                                    height: 55px;
    
                                    img {
                                        width: 100%;
                                        height: 100%;
                                        object-fit: cover;
                                        font-size: 12px;
                                        transition: all 0.2s ease;
                                    }
                                }
                    
                                a {
                                    padding: 10px 0px 10px 20px;
                                    color: $lastPropertiesName;
                                    transition: all 0.2s ease;
                
                                    small {
                                        display: block;
                                        margin-top: 4px;
                                        font-family: $secondaryFont;
                                        color: $lastPropertiesDescription;
                                        line-height: 16px;
                                        font-size: 13px;
                                        text-transform: none;
                                        font-weight: 400;
                                    }
                
                                    &:hover { opacity: 1; }
                                }   
                
                                &:hover { 
                                    a {
                                        color: rgba($secondaryMainColor, 0.6);
                                    }
                
                                    img {
                                        opacity: 0.6;
                                    }
                                }
                    
                            }
                        }
                    }
                
                    .properties-loader {
                        display: flex;
                        width: $headerSubMultiColumnWidth;
                        min-height: 50px;
                
                        @media (max-width: 1200px) {
                            width: 360px;
                        }
                    }
                }
            }
            
        }
    }
}

#header2 {
    .main-header {
        .main-menu-ctn {
            .main-menu {
                .secondary-ul.multi-column {
                    left: -150px;

                    &::before {
                        left: 160px;
                    }
                }
            }
        }
    }
}
