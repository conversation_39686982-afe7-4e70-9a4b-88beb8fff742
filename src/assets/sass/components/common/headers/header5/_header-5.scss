/*
	Header Component
*/
$headerHeight5: 100px;
$headerBG5: $white;
$headerBorderBottom5: 1px solid #DDDDDD;
$headerLinkPaddingRight5: 35px;
$headerLinkSize5: 14px;
$headerLinkFont5: $primaryFont;
$headerLinkWeight5: normal;
$headerLinkLineHeight5: 17px;
$headerLinkColor5: #4A4A4A;
$headerLinkTransform5: none;
$headerSubMenuBG5: #eee;
$headerSubMenuLinkBGHover5: transparent;
$headerSubMenuLinkColor5: $secondaryMainColor;
$headerSubMenuLinkColorHover5: $secondaryMainColor;
$headerSubMenuLinkLineHeight5: 20px;
$headerSubMenuLinkSize5: 14px;
$headerSubLinkTransform5: none;

$subHeaderFavoriteBorderLeft5: none;
$subHeaderFavoritePadding5:0 20px 0 0;
$subHeaderFavoriteSize5: 14px;
$subHeaderFavoriteColor5: #2D2F31;

$subHeaderNumberBG5: #FFFFFF;
$subHeaderNumberBGHover5: #FFFFFF;
$subHeaderNumberBorderLeft5: none;
$subHeaderNumberPadding5: 0 25px 0 0px;
$subHeaderNumberColor5: $primaryColor;
$subHeaderNumberSize5: 18px;
$subHeaderNumberWeight5: 900;
$subHeaderNumberFont5: $primaryFont;
$subHeaderNumberOpacity5: 0.96;
$subHeaderNumberPhoneBorder5: none;
$subHeaderNumberPhoneRadius5: 0px;

/*
	Transparent Header
*/
$transparentSubHeaderLinkColor5: $white;
$transparentSubHeaderNumberColor5: $white;
$transparentHeaderLinkColor5: $white;

/*
	Special Color Header
*/
$specialSubHeaderLinkColor5: blue;
$specialSubHeaderNumberColor5: blue;
$specialHeaderLinkColor5: blue;

#header5 {
	position: relative;
	z-index: 200;
	border-bottom: $headerBorderBottom5;

	.main-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: $headerHeight5;
		padding: 10px 40px 10px;
		background-color: $headerBG5;

		@media (max-width: 1095px) {
			padding: 10px 20px;
		}

		.special-logo {
			fill: $black;
			width: 40px;
      		max-height: 50px;
		}

		.logo-ctn {
			height: 58px;
			display: flex;
			align-items: center;

			> a {
				height: 58px;
				display: flex;
				align-items: center;
			}

			img {
				max-height: 100%;
			}

			.special-logo {
				margin-left: 25px;
			}
		}

		.number-ctn {
			background: $subHeaderNumberBG5;
			padding: $subHeaderNumberPadding5;
			border-left: $subHeaderNumberBorderLeft5;
			transition: $primaryAnimation;
			list-style: none;

			@media (max-width: 1400px) {
				padding: 0 0 0 20px;
			}

			&:hover {
				background: $subHeaderNumberBGHover5;
				color: $primaryButtonTextColorHover;
			}

			a {
				color: $subHeaderNumberColor5;
				font-size: $subHeaderNumberSize5;
				font-weight: $subHeaderNumberWeight5;
				font-family: $subHeaderNumberFont5;
				opacity: $subHeaderNumberOpacity5;

				@media (max-width: 1400px) {
					font-size: 15px;
					line-height: 18px;
				}

				i {
					margin-right: 10px;
					font-size: $subTextSize;
					border: $subHeaderNumberPhoneBorder5;
					border-radius: $subHeaderNumberPhoneRadius5;
				}
			}
		}

		.favorite {
			padding: $subHeaderFavoritePadding5;
			border-left: $subHeaderFavoriteBorderLeft5;

			a {
				font-size: $subHeaderFavoriteSize5;
				font-weight: $primaryBold;
				display: flex;
				align-items: center;
				color: $subHeaderFavoriteColor5;

				i {
					margin-right: 10px;
					font-size: 20px;
				}
			}
		}
	}

	.right {
		display: flex;
		align-items: center;

		li {
			list-style: none;
		}
	}

	.header-menu-toggle {
		display: inline-block;
		position: relative;
		right: 0;
		width: 29px;
		height: 20px;
		transition: all 0.5s ease;

		span {
			width: 100%;
			height: 2px;
			background-color: #2D2F31;
			position: absolute;
			top: 0;
			right: 0;
			left: 0;
			-ms-filter: none;
			-webkit-filter: none;
			filter: none;
			opacity: 1;
			-webkit-transform-origin: center;
			-ms-transform-origin: center;
			transform-origin: center;
			transition: all .25s ease-in-out;

			&:nth-child(2){
				top: 9px;
			}

			&:nth-child(3){
				top: 18px;
				width: 16px;
				left: 13px;
			}
		}
	}

	&.transparent {
		.sub-header {
			background: transparent;

			ul {
				li {
					a {
						color: $transparentSubHeaderLinkColor5;
					}
				}
			}

			.number-ctn {
				a {
					color: $transparentSubHeaderNumberColor5;
				}
			}
		}
		.main-header {
			background: transparent;

			.main-menu-ctn {
				.main-menu {
					.item-menu {
						> a {
							color: $transparentHeaderLinkColor5;
						}
					}
				}
			}
		}
	}

	&.special {
		.sub-header {
			background: transparent;

			ul {
				li {
					a {
						color: $specialSubHeaderLinkColor5;
					}
				}
			}

			.number-ctn {
				a {
					color: $specialSubHeaderNumberColor5;
				}
			}
		}
		.main-header {
			background: transparent;

			.main-menu-ctn {
				.main-menu {
					.item-menu {
						> a {
							color: $specialHeaderLinkColor5;
						}
					}
				}
			}
		}
	}
}

//Opened Menu
body {
	&.-open-menu {
		#header {
			.header-menu-toggle {
				height: 25px;

				span {
					&:first-child {
						transform: rotate(45deg);
						top: 12px;
					}

					&:nth-child(2) {
						opacity: 0;
					}

					&:nth-child(3) {
						transform: rotate(-45deg);
						top: 12px;
					}
				}
			}
		}
	}
}
