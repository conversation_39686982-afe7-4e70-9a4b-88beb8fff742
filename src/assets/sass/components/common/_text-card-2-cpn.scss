.text-card-2-cpn {
	margin: 80px 0;

	.card-ctn {
		display: flex;
		align-items: center;
		padding: 80px 60px;
		background-color: #F6F6F6;

		.img-ctn {
			text-align: center;
		}

		.text-wrap {
			.title {
				margin-top: 0;
			}

			p {
				margin-bottom: 30px;
				color: $secondaryMainColor;
				font-family: $secondaryFont;
				font-size: $subTextSize;
				line-height: $subTextLineHeight;
			}
		}
	}

	.left {
		.img-ctn {
			padding-left: 0;
			text-align: center;
		}

		.text-wrap {
			padding-right: 0;
		}
	}

	.right {
		.img-ctn {
			padding-right: 0;
			text-align: center;
		}

		.text-wrap {
			padding-left: 0;
		}
	}

	@media (max-width: 768px) {
		margin: 60px 0;

		.card-ctn {
			padding: 30px 0px;
			flex-direction: column;

			.img-ctn {
				margin-top: 30px;
				margin-bottom:40px;
			}

			.text-wrap {
				a.main-button {
					margin: auto;
				}
			}
		}

		.left {
			.img-ctn {
				padding-left: 15px;
			}

			.text-wrap {
				padding-right: 15px;
				text-align: center;
			}
		}
	}
}
