

.reasons-slider-cpn{
    padding: 100px 0;
    background-color: white;
    
    .swipers-ctn{
        position: relative;
        min-height: 500px;
        margin-top: 40px;
    }

    .swiper-content-img{
        position: relative;
        display: flex;
        justify-content: center;
        max-height:570px;

        .swiper-container{
            max-width: 1000px;
            

            .swiper-slide{
                width: 100% !important;
                img{
                    width: 100%;
                    max-width: 100%;
                }
                
            }
        }


        .swiper-nav{
            width: 100%;
            position: absolute;
            top:65%;
            display: flex;
            justify-content: space-between;
            transform: translate(00%,-50%);
            z-index: 1;

            @media(max-width: 1199px){
                width: 95%;
             }
        }

        .swiper-btn{
            width: 50px;
            height: 50px;
            background: $secondaryMainColor;
            border-radius: 50%;
            top: initial;
            margin-top: 0;
            position: relative;
            display: inline-block;
            left: initial;
            right: initial;
    
            &:before {
                position: absolute;
                font-family: 'icomoon' !important;
                speak: none;
                font-style: normal;
                font-weight: normal;
                font-variant: normal;
                text-transform: none;
                line-height: 1;
                font-size: 20px;
                color: $propertyHeroSettingsColor;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
    
            }
    
            &.swiper-button-prev {
    
                &:before {
                    content: "\e909";
                }
            }
    
            &.swiper-button-next {

                margin-left: 10px;

                &:before {
                    content: "\e90a";
                }
            }

            &.swiper-button-disabled{
                opacity: 0.5 !important;
            }
        }

        
    }

    .row{
        display: flex;
        justify-content: center;
        @media(max-width: 1199px){
            display: block;
         }
    }

    .swiper-content{
        background-color: white;
        padding: 50px 50px 30px;
        z-index: 1;
        position: relative;
        max-width:1000px;
        

        .swiper-slide{
            width: 100% !important;
            .head{
                display: flex;
                align-items: center;
                justify-content: space-between;
                flex-wrap: wrap;

                .title{
                    font-size: 42px;
                    line-height: 40px;
                    text-align: left;
                    width: calc(100% - 100px);
                    color: #002758;
                    text-transform: uppercase;
                    font-family: $primaryFont;
                    font-weight: 600;
                    margin-top:10px;
                    margin-bottom:20px;

                    &:first-child{
                        font-weight: unset;
                    }

                    @media(max-width: 500px){
                       
                            width: 100%;
                            font-size: 30px;
                            margin-bottom:10px;
                        }
                     
                    
                }


            }


            .content{
                margin-top:0;
                color: #002758;
                font-size: 21px;
                text-transform: uppercase;
                font-family: $primaryFont;
                line-height: 27px;

                span{
                    display: block;
                    margin-top: 20px;
                    font-size: 10px;
                }

                a{
                    color: $secondaryMainColor;
                    font-weight: 700;
                }
            }
        }

    }

    @media(max-width: 992px){

        .swipers-ctn{
            display: block;
        }

        .swiper-content-img{
            position: relative;

            .swiper-container{
                max-width: 100%;
            }
        }
    }

    @media(max-width: 425px){

        padding: 60px 0;
        
        .swiper-content{
            padding: 20px;

            .swiper-slide{
                .head {
                    align-items: end;
                    flex-direction: column-reverse;

                   

                    .step{
                        margin: 20px 0 0;
                    }
                }                
            }

            .swiper-nav{
                margin-bottom: 10px;
                .swiper-btn{
                    width: 35px;
                    height: 35px;

                    &:before{
                        font-size: 16px;
                    }

                }
            }
        }
    }
    
    .team-info-box{

        margin-bottom:50px;
        .description{
            max-width:550px;
        }
    }
}


