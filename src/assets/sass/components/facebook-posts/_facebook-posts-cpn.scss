.facebook-posts-cpn {
	background-color: #fff;
	padding:          100px 0 170px;

	@media (max-width: 992px) {
		padding: 50px 0 100px;
	}

	.container { position: relative; }

	.title {
		color: $secondaryMainColor;
		text-align: center;
		font-family: $primaryFont;
		font-style: normal;
		text-transform: uppercase;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 20px;
		margin: 0;

		.icon-logo-facebook {
			font-size: 30px;
			margin-bottom: 5px;
		}

		@media (max-width: 992px) {
			font-size: 24px;
			gap: 15px;
		}
	}

	.facebook-posts-container {
		//grid de 3 elements
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-gap: 30px;
		margin: 60px 0;

		.post {

			img {
				width: auto;
				min-height: 330px;
				object-fit: cover;
			}

			p {
				color: $secondaryMainColor;
				font-size: 15px;
				line-height: 24px;
			}
		}

		@media screen and (max-width: 768px) {
			grid-template-columns: 1fr;

			.post {
				width: 70%;
				margin: auto;
			}
		}

		@media screen and (max-width: 576px) {
			.post {
				width: 100%;
			}
		}
	}

	.button-ctn {
		text-align: center;
	}
}

