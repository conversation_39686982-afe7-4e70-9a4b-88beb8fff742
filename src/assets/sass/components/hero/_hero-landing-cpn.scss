.hero-landing-cpn{
    position: relative;
    overflow: hidden;

    .banner{
        position: relative;

        .banner-image{
            max-height: 650px;

            @media (min-width:1200px) {
                height: 650px;
            }

            img{
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }

        .container{
            position: absolute;
            z-index: 3;
            top: 50%;
            left: 50%;
            transform: translate(-50%,-50%);

            .inner{
                max-width: 600px;
                margin: 0 auto;
                text-align: center;

                .title{
                    color: white;
                    font-family: $primaryFont;
                    margin-top: 0;
                    margin-bottom: 0;
                    text-transform: uppercase;
                }

                @media (max-width:992px) {
                    padding: 30px 50px;

                    .name{
                        font-size: 30px;
                    }
                }
            }

        }
    }
}


.header-reasons{

    h1{
        font-size: 60px !important;
        line-height: 70px !important;
    }
    .inner{
        max-width: 1000px !important;
    }
}
