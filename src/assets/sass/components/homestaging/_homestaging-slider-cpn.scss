$homestagingSliderBg:white;
$homestagingSliderPadding:100px 0;


.homestaging-slider-cpn{
    background: $homestagingSliderBg;
    padding: $homestagingSliderPadding;

    .title {
        text-transform: uppercase;
        color: $secondaryMainColor;
    }

    .description{
        color: $secondaryMainColor;
        font-size: 17px;
        line-height: 28px;
    }

    .container{

        .row{
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            margin-bottom: 100px;

            @media (max-width:992px) {
                display: block;
            }
        }

    }

    .slider-default-cpn{
        @media (max-width:992px) {
            margin-top: 40px;
        }
    }

    .team-info-box{


        @media (max-width:992px) {
            margin-bottom: 60px;
        }
    }

}
