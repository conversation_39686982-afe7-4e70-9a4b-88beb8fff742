// transition
@mixin transition($transition) {
  transition: $transition;
  -o-transition: $transition;
  -moz-transition: $transition;
  -webkit-transition: $transition;
}

// transform
@mixin transform($transform) {
  transform: $transform;
  -o-transform: $transform;
  -ms-transform: $transform;
  -moz-transform: $transform;
  -webkit-transform: $transform;
}

// animation
@mixin animation($animation) {
  animation: #{$animation};   
  -o-animation: #{$animation};
  -moz-animation: #{$animation};
  -webkit-animation: #{$animation};
}

// flex
@mixin flex($align-vert: center, $align-horiz: center, $direction: row) {
  display: -webkit-box;
  display: flex;
  display: -ms-flexbox;
  display: -webkit-flex;
  align-items: $align-vert;
  -webkit-align-items: $align-vert;
  justify-content: $align-horiz;
  -webkit-justify-content: $align-horiz;
  flex-direction: $direction
}

// Keyframes
@mixin keyframes($animation-name) {
  @keyframes #{$animation-name} {
    @content;
  }
  @-o-keyframes #{$animation-name} {
    @content;
  }
  @-moz-keyframes #{$animation-name} {
    @content;
  }
  @-webkit-keyframes #{$animation-name} {
    @content;
  }
}

@mixin clearfix {
  &:after {
    content: "";
    display: table;
    clear: both;
  }
}

@mixin fontSize($size, $line-height: null, $weight: null, $spacing: null) {
  font-size: $size;
  font-size: calculateRem($size);

  @if ($line-height) {
    line-height: #{$line-height};
    line-height: calculateRem($line-height);
  }

  @if ($weight) {
    font-weight: #{$weight};
  }

  @if ($spacing) {
    letter-spacing: #{$spacing};
  }
}

@mixin pseudoElement($position: absolute, $display: block, $content: '') {
  position: $position;
  display: $display;
  content: $content;
}

@mixin before($position: absolute, $display: block, $content: '') {
  &::before { @include pseudoElement($position, $display, $content) };
}

@mixin after($position: absolute, $display: block, $content: '') {
  &::after { @include pseudoElement($position, $display, $content) };
}

@function calculateRem($value)  {
  $value: calc($value / 16px * 1rem);
  @if ($value == 0rem) { $value: 0; }
  @return $value;
}

@mixin img() {
  width: 100%;
  height: 100%;
  object-fit: cover;
}