::selection {
	background: lighten( $primaryColor, 30% ); /* WebKit/Blink Browsers */
}
::-moz-selection {
	background: lighten( $primaryColor, 30% ); /* Gecko Browsers */
}

.loading {
	opacity: 0 !important;
}

.clear{
	clear: both;
}

.row {
	margin-bottom: 0;
}

.swiper-button-disabled {
	opacity: 0 !important;
}

.ap-input {
	background: transparent !important;
	border: none !important;
}

.tab-content {
	.algolia-places {
		background: white;
		border-radius: 3px;
	}
}

.search-sell-cpn{
	.algolia-places {
		background: white;
		border-radius: 3px;
	}
}

.container {
	position: relative;
	display: block;

	&.-small {
		max-width: 980px;
	}

	&.-smaller {
		max-width: 780px;
	}
}

.leaflet-top {
	z-index: 5 !important;
}

.title.with-icon {
	display: flex;
	align-items: center;
}

.-center{
	text-align: center;
}

.-hide {
	display: none !important;
}

.hide-mobile {
	@media (max-width: 992px) {
		display: none;
	}
}

.show-mobile {
	display: none;

	@media (max-width: 992px) {
		display: block;
	}
}

/* Section Title */
.emphase-title {
	padding-top: 4px;
	padding-bottom: 3px;
	padding-left: 16px;
	color: $secondaryMainColor;
	font-size: $emphaseTitleSize;
	font-weight: 400;
	line-height: $emphaseLineHeight;
	border-left: 8px solid $emphaseBorderColor;
	text-transform: uppercase;

	@media (max-width: 768px) {
		font-size: $primaryTextSize;
		line-height: 22px;
	}
}

.single-title {
	font-size: 32px;
	line-height: 39px;
	text-align: center;
	color: $secondaryMainColor;
	margin-top: 50px;
	margin-bottom: 40px;
}


/* Button Round */
.main-button {
	display: inline-block;

	.icon-alerte{
		font-size: $alertButtonIconSize;
		vertical-align: middle;
		margin-right: 10px;
	}

	&:hover{
		cursor: pointer;
	}

	&.-primary {
		background: $primaryButtonBG;
		color: $primaryButtonTextColor;
		line-height: $primaryButtonLineHeight;
		text-align: $primaryButtonAlign;
		font-size: $primaryButtonSize;
		font-weight: $primaryButtonWeight;
		font-family: $primaryButtonFont;
		padding: $primaryButtonPadding;
		border: $primaryButtonBorder;
		border-radius: $primaryButtonBorderRadius;
		text-transform: $primaryButtonTextTransform;
		min-width: $primaryButtonMinWidth;
		transition: $primaryAnimation;

		&:hover {
			background: $primaryButtonBGHover;
			color: $primaryButtonTextColorHover;
		}

		@media (max-width:450px) {
			min-width: initial;
		}
	}

	&.-previous {
		color: $primaryButtonTextColor;
		line-height: $primaryButtonLineHeight;
		text-align: $primaryButtonAlign;
		font-size: $primaryButtonSize;
		font-weight: $primaryButtonWeight;
		font-family: $primaryButtonFont;
		padding: $primaryButtonPadding;
		text-transform: $primaryButtonTextTransform;
		min-width: $primaryButtonMinWidth;
		transition: $primaryAnimation;

		&:hover {
			opacity: 0.7;
		}

		@media (max-width:450px) {
			min-width: initial;
		}
	}

	&.-secondary {
		background: $secondaryButtonBG;
		color: $secondaryButtonTextColor;
		line-height: $secondaryButtonLineHeight;
		text-align: $secondaryButtonAlign;
		font-size: $secondaryButtonSize;
		font-weight: $secondaryButtonWeight;
		font-family: $secondaryButtonFont;
		padding: $secondaryButtonPadding;
		border: $secondaryButtonBorder;
		border-radius: $secondaryButtonBorderRadius;
		text-transform: $secondaryButtonTextTransform;
		min-width: $secondaryButtonMinWidth;
		transition: $primaryAnimation;

		&:hover {
			background: $secondaryButtonBGHover;
			color: $secondaryButtonTextColorHover;
		}

		@media (max-width:450px) {
			min-width: initial;
		}
	}

	&.-ghost {
		background: $ghostButtonBG;
		color: $ghostButtonTextColor;
		line-height: $ghostButtonLineHeight;
		text-align: $ghostButtonAlign;
		font-size: $ghostButtonSize;
		font-weight: $ghostButtonWeight;
		font-family: $ghostButtonFont;
		padding: $ghostButtonPadding;
		border: $ghostButtonBorder;
		border-radius: $ghostButtonBorderRadius;
		text-transform: $ghostButtonTextTransform;
		min-width: $ghostButtonMinWidth;
		transition: $primaryAnimation;



		&:hover {
			background: $ghostButtonBGHover;
			color: $ghostButtonTextColorHover;
			border: $ghostButtonBorderHover;
		}

		@media (max-width:450px) {
			min-width: initial;
		}
	}

	&.-grey{
		color: #666;
		border-color: #666;

		&:hover {
			background: transparent;
			border-color: $primaryColor;
		}
	}

	&.-white {
		background: $whiteButtonBG;
		color: $whiteButtonTextColor;
		line-height: $whiteButtonLineHeight;
		text-align: $whiteButtonAlign;
		font-size: $whiteButtonSize;
		font-weight: $whiteButtonWeight;
		font-family: $whiteButtonFont;
		padding: $whiteButtonPadding;
		border: $whiteButtonBorder;
		border-radius: $whiteButtonBorderRadius;
		text-transform: $whiteButtonTextTransform;
		min-width: $whiteButtonMinWidth;
		transition: $primaryAnimation;

		&:hover {
			background: $whiteButtonBGHover;
			color: $whiteButtonTextColorHover;
			border: $whiteButtonBorderHover;
		}

		@media (max-width:450px) {
			min-width: initial;
		}
	}

	&.-primary-small {
		background: $primarySmallButtonBG;
		color: $primarySmallButtonTextColor;
		line-height: $primarySmallButtonLineHeight;
		text-align: $primarySmallButtonAlign;
		font-size: $primarySmallButtonSize;
		font-weight: $primarySmallButtonWeight;
		font-family: $primarySmallButtonFont;
		padding: $primarySmallButtonPadding;
		border: $primarySmallButtonBorder;
		border-radius: $primarySmallButtonBorderRadius;
		text-transform: $primarySmallButtonTextTransform;
		min-width: $primarySmallButtonMinWidth;
		transition: $primaryAnimation;

		&:hover {
			background: $primarySmallButtonBGHover;
			color: $primarySmallButtonTextColorHover;
		}
	}

	&.-secondary-small {
		background: $secondarySmallButtonBG;
		color: $secondarySmallButtonTextColor;
		line-height: $secondarySmallButtonLineHeight;
		text-align: $secondarySmallButtonAlign;
		font-size: $secondarySmallButtonSize;
		font-weight: $secondarySmallButtonWeight;
		font-family: $secondarySmallButtonFont;
		padding: $secondarySmallButtonPadding;
		border: $secondarySmallButtonBorder;
		border-radius: $secondarySmallButtonBorderRadius;
		text-transform: $secondarySmallButtonTextTransform;
		min-width: $secondarySmallButtonMinWidth;
		transition: $primaryAnimation;

		&:hover {
			background: $secondarySmallButtonBGHover;
			color: $secondarySmallButtonTextColorHover;
		}
	}

	&.-alert{
		background: $alertButtonBG;
		color: $alertButtonTextColor;
		text-align: $alertButtonAlign;
		font-size: $alertButtonSize;
		font-weight: $alertButtonWeight;
		font-family: $alertButtonFont;
		padding: $alertButtonPadding;
		border: $alertButtonBorder;
		border-radius: $alertButtonBorderRadius;
		text-transform: $alertButtonTextTransform;
		min-width: $alertButtonMinWidth;
		transition: $primaryAnimation;

		&:hover {
			background: $alertButtonBGHover;
			color: $alertButtonTextColorHover;
		}
	}

	&.-favorite {
		background: $favoriteButtonBG;
		color: $favoriteButtonTextColor;
		line-height: $favoriteButtonLineHeight;
		text-align: $favoriteButtonAlign;
		font-size: $favoriteButtonSize;
		font-weight: $favoriteButtonWeight;
		font-family: $favoriteButtonFont;
		padding: $favoriteButtonPadding;
		border: $favoriteButtonBorder;
		border-radius: $favoriteButtonBorderRadius;
		text-transform: $favoriteButtonTextTransform;
		min-width: $favoriteButtonMinWidth;
		transition: $primaryAnimation;

		&:hover {
			background: $favoriteButtonBGHover;
			color: $favoriteButtonTextColorHover;
		}
	}

	&.-phone {
		background: $phoneButtonBG;
		color: $phoneButtonTextColor;
		line-height: $phoneButtonLineHeight;
		text-align: $phoneButtonAlign;
		font-size: $phoneButtonSize;
		font-weight: $phoneButtonWeight;
		font-family: $phoneButtonFont;
		padding: $phoneButtonPadding;
		border: $phoneButtonBorder;
		border-radius: $phoneButtonBorderRadius;
		text-transform: $phoneButtonTextTransform;
		min-width: $phoneButtonMinWidth;
		transition: $primaryAnimation;

		&:hover {
			background: $phoneButtonBGHover;
			color: $phoneButtonTextColorHover;
		}
	}

	&.unable {
		cursor: default;
		pointer-events: none;
		opacity: 0.5;
	}

	&:disabled{
		cursor: default;
		pointer-events: none;
		opacity: 0.5;
	}
}

/* Small Link */
.small-link {
	position: relative;
	color: $smallLinkColor;
	font-family: $smallLinkFont;
	font-size: $smallLinkSize;
	font-weight: $smallLinkWeight;
	line-height: $smallLinkLineHeight;
	text-transform: $smallLinkTextTransform;

	&.right {
		padding-right: 0px;

		&:hover {
			i {
				right: -5px;
			}
		}
	}

	i {
		position: relative;
		right: 0;
		transition: $primaryAnimation;
	}

	.icon-map-plus {
		font-size: 10px;
		margin-right: 7px;
	}

	.icon-arrow-right {
		padding-left: 5px;
	}

	&.-no-uppercase {
		text-transform: none;
	}
}

/* Tabs Ctn */
.tabs-ctn {
	.input-tab {
		display: none;
	}
}

.tabs {
	padding-left: 0;
}

/* List */

.page-description, .-page-description{
	ul{
		padding-left: 0px;
		padding-bottom: 30px;

		li {
			position: relative;
			padding: 16px 0px 16px 40px;
			color: $listColor;
			font-family: $secondaryFont;
			font-size: $listSize;
			line-height: $listLineHeight;
			border-bottom: $listBorderBottom;

			&:before {
				color: $primaryColor;
				font-family: 'icomoon';
				speak: none;
				font-style: normal;
				font-weight: normal;
				font-variant: normal;
				text-transform: none;
				line-height: 1;
				-webkit-font-smoothing: antialiased;
				-moz-osx-font-smoothing: grayscale;
				content: "\e90d";
				position: absolute;
				left: 0;
				top: 22px;
				//transform: translate(0%, -50%);
			}
		}
	}
}
.list {
	padding-left: 0px;
	padding-bottom: 30px;

	li {
		position: relative;
		padding: 16px 0px 16px 40px;
		color: $listColor;
		font-family: $secondaryFont;
		font-size: $listSize;
		line-height: $listLineHeight;
		border-bottom: $listBorderBottom;

		&:before {
			color: $primaryColor;
			font-family: 'icomoon';
			speak: none;
			font-style: normal;
			font-weight: normal;
			font-variant: normal;
			text-transform: none;
			line-height: 1;
			-webkit-font-smoothing: antialiased;
			-moz-osx-font-smoothing: grayscale;
			content: "\e90d";
			position: absolute;
			left: 0;
			top: 22px;
			//transform: translate(0%, -50%);
		}
	}
}
/* Sortable List */
.sortable-list {
	.input-tab {
		display: none;
	}

	input:checked + label {
		color: $filterTabActiveColor;

		&:after {
			width: 100%;
		}
	}

	input:hover + label {
		color: $filterTabActiveColor;

		&:after {
			width: 100%;
		}
	}

	label {
		margin-right: 25px;
		position: relative;
		padding: 0 0px 7px;
		color: $filterTabColor;
		font-family: $filterTabFont;
		font-size: $filterTabSize;
		font-weight: $filterTabWeight;
		text-align: center;
		text-transform: $filterTabTextTransform;
		overflow: inherit;
		display: inline-block;
		line-height: initial;
		margin-bottom: 5px;

		&:hover{
			cursor: pointer;
		}

		@media (max-width: 1050px) {
			font-size: 13px;
			margin-right: 15px;
		}

		&:last-child {
			margin-right: 0;
		}

		&:after {
			position: absolute;
			bottom: 0;
			left: 0;
			content: "";
			background: $filterTabActiveBorderColor;
			height: $filterTabActiveBorderSize;
			display: block;
			width: 0px;
			transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
		}
	}
}

/*
	Page Head
*/

.-page-head {
	padding-top: 60px;
	text-align: center;

	.page-title {
		margin-top: 0;
		margin-bottom: 30px;
	}

	.page-description {
		margin: 0 auto;
		max-width: 510px;
		color: #777777;
		font-family: $secondaryFont;
		font-size: $subTextSize;
		line-height: 28px;
		text-align: center;
	}
}

/*
	Contact CTA
*/

.contact-cta {
	padding: 35px 30px;
	background-color: #F4F4F4;
	display: flex;
	align-items: center;
	justify-content: space-between;

	.text-ctn{
		margin-right: 30px;
	}

	@media (max-width: 768px) {
		display: block;
	}

	.button-ctn {
		@media (max-width: 768px) {
			margin-top: 20px;
		}
	}

	.title {
		margin-top: 0px;
		margin-bottom: 18px;
	}

	.description {
		margin: 0;
		color: $secondaryMainColor;
		font-family: $secondaryFont;
		font-size: 13px;
		line-height: $detailTextLineHeight;
	}
}

/*
	Share Social Cpn
*/

.share-social-cpn {
	display: flex;
	align-items: center;

	.sb-button {
		margin: 0;
		cursor: pointer;
	}

	.fa, i, .share-btn {
		margin-right: 40px;
	}

	.sb-wrapper {
		min-width: 0;
		height: auto;
		border: 0;
		background: none;
		color: $secondaryMainColor;

		.sb-icon {
			min-width: 0;
		}
	}

	.sb-group {
		display: flex;

		.sb-wrapper {
			margin-right: 40px;
		}

		.sb-pinterest {
			margin-right: 0;
		}
	}
}

/*
	Previous Button
*/

.previous-button {
	margin-right: 40px;
	color: $secondaryMainColor;
	font-family: $primaryFont;
	font-size: $detailTextSize;
	text-transform: uppercase;
	font-weight: 500;
	line-height: 17px;
	cursor: pointer;
}


/*
	Loading
*/

.lds-ripple {
	display: inline-block;
	position: relative;
	width: 64px;
	height: 64px;
}

.lds-ripple div {
	position: absolute;
	border: 4px solid rgba(0, 0, 0, 0.35);
	opacity: 1;
	border-radius: 50%;
	transform-origin: center;
	animation: lds-ripple 1.3s cubic-bezier(0, 0.2, 0.8, 1) infinite;
}

.lds-ripple div:nth-child(2) {
	animation-delay: -0.5s;
}

@keyframes lds-ripple {
	0% {
		top: 28px;
		left: 28px;
		width: 0;
		height: 0;
		opacity: 1;
	}
	100% {
		top: -1px;
		left: -1px;
		width: 58px;
		height: 58px;
		opacity: 0;
	}
}


/*
	Loading circle
*/

$base-line-height: 50px;
$color: $primaryColor;
$off: rgba($primaryColor, 0);
$spin-duration: 1s;
$pulse-duration: 750ms;

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

.loading-circle {
	border-radius: 50%;
	width: $base-line-height;
	height: $base-line-height;
	border: .25rem solid $off;
	border-top-color: $color;
	animation: spin $spin-duration infinite linear;
}


/*
	Sb share
*/
.sb-wrapper {
	.sb-inner {
		display: none !important;
	}
}

.sb-email, .sb-facebook, .sb-twitter {
	font-family: 'icomoon';
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;
	-webkit-font-smoothing: antialiased;
	font-size: 15px !important;
	color: #666666;

	&:hover {
		color: #999999;
	}
}

.sb-email {
	&:before {
		content: "\e904";
	}
}

.sb-facebook {
	&:before {
		content: "\e900";
	}
}

.sb-twitter {
	margin-right: 0 !important;

	&:before {
		content: "\e952";
	}
}

// Hide duplicated swiper icon
.swiper-button-next::after,
.swiper-button-prev::after {
	content: none;
}

// Fix swiper overflow
.swiper-container { overflow: hidden; }
