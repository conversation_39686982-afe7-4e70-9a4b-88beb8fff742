/*
	Buttons
*/
$defaultButtonHeight: 60px;

$primaryButtonBG: transparent;
$primaryButtonBGHover: $secondaryMainColor;
$primaryButtonTextColor: $secondaryMainColor;
$primaryButtonTextColorHover: $white;
$primaryButtonSize: $detailTextSize;
$primaryButtonLineHeight: 17px;
$primaryButtonWeight: 600;
$primaryButtonAlign: center;
$primaryButtonFont: $primaryFont;
$primaryButtonPadding: 17px 52px;
$primaryButtonBorder: solid 2px $secondaryMainColor;
$primaryButtonBorderRadius: 0;
$primaryButtonTextTransform: uppercase;
$primaryButtonMinWidth: 250px;

$secondaryButtonBG: transparent;
$secondaryButtonBGHover: $primaryColor;
$secondaryButtonTextColor: $secondaryMainColor;
$secondaryButtonTextColorHover: $white;
$secondaryButtonSize: $detailTextSize;
$secondaryButtonLineHeight: 17px;
$secondaryButtonWeight: 500;
$secondaryButtonAlign: center;
$secondaryButtonFont: $primaryFont;
$secondaryButtonPadding: 17px 52px;
$secondaryButtonBorder: 1px solid #979797;
$secondaryButtonBorderRadius: 100px;
$secondaryButtonTextTransform: uppercase;
$secondaryButtonMinWidth: 250px;

$ghostButtonBG: transparent;
$ghostButtonBGHover: white;
$ghostButtonTextColor: $white;
$ghostButtonTextColorHover: $primaryColor;
$ghostButtonSize: $detailTextSize;
$ghostButtonLineHeight: 17px;
$ghostButtonWeight: 500;
$ghostButtonAlign: center;
$ghostButtonFont: $primaryFont;
$ghostButtonPadding: 17px 52px;
$ghostButtonBorder: 2px solid white;
$ghostButtonBorderHover: 2px solid white;
$ghostButtonBorderRadius: 0;
$ghostButtonTextTransform: uppercase;
$ghostButtonMinWidth: 250px;

$whiteButtonBG: transparent;
$whiteButtonBGHover: $primaryColor;
$whiteButtonTextColor: white;
$whiteButtonTextColorHover: $white;
$whiteButtonSize: $detailTextSize;
$whiteButtonLineHeight: 17px;
$whiteButtonWeight: 500;
$whiteButtonAlign: center;
$whiteButtonFont: $primaryFont;
$whiteButtonPadding: 17px 52px;
$whiteButtonBorder: 2px solid white;
$whiteButtonBorderHover: 2px solid white;
$whiteButtonBorderRadius: 100px;
$whiteButtonTextTransform: uppercase;
$whiteButtonMinWidth: 250px;

$primarySmallButtonBG: $primaryColor;
$primarySmallButtonBGHover: darken($primaryColor, 10);
$primarySmallButtonTextColor: $white;
$primarySmallButtonTextColorHover: $white;
$primarySmallButtonSize: 13px;
$primarySmallButtonLineHeight: 17px;
$primarySmallButtonWeight: 500;
$primarySmallButtonAlign: center;
$primarySmallButtonFont: $primaryFont;
$primarySmallButtonPadding: 7px 20px;
$primarySmallButtonBorder: none;
$primarySmallButtonBorderRadius: 15px;
$primarySmallButtonTextTransform: none;
$primarySmallButtonMinWidth: 140px;

$secondarySmallButtonBG: #AAAAAA;
$secondarySmallButtonBGHover: $primaryColor;
$secondarySmallButtonTextColor: $white;
$secondarySmallButtonTextColorHover: $white;
$secondarySmallButtonSize: 13px;
$secondarySmallButtonLineHeight: 17px;
$secondarySmallButtonWeight: 500;
$secondarySmallButtonAlign: center;
$secondarySmallButtonFont: $primaryFont;
$secondarySmallButtonPadding: 7px 20px;
$secondarySmallButtonBorder: none;
$secondarySmallButtonBorderRadius: 15px;
$secondarySmallButtonTextTransform: none;
$secondarySmallButtonMinWidth: 140px;

$alertButtonBG: #AAAAAA;
$alertButtonBGHover: $primaryColor;
$alertButtonTextColor: $white;
$alertButtonTextColorHover: $white;
$alertButtonSize: 14px;
$alertButtonIconSize: 22px;
$alertButtonWeight: 500;
$alertButtonAlign: center;
$alertButtonFont: $primaryFont;
$alertButtonPadding: 10px 20px;
$alertButtonBorder: 1px solid #FFFFFF;
$alertButtonBorderRadius: 3px;
$alertButtonTextTransform: uppercase;
$alertButtonMinWidth: 140px;

$favoriteButtonBG: transparent;
$favoriteButtonBGHover: $primaryColor;
$favoriteButtonTextColor: $primaryColor;
$favoriteButtonTextColorHover: $white;
$favoriteButtonSize: $detailTextSize;
$favoriteButtonLineHeight: 17px;
$favoriteButtonWeight: 500;
$favoriteButtonAlign: center;
$favoriteButtonFont: $primaryFont;
$favoriteButtonPadding: 17px 52px;
$favoriteButtonBorder: 1px solid $primaryColor;
$favoriteButtonBorderRadius: 100px;
$favoriteButtonTextTransform: none;
$favoriteButtonMinWidth: 280px;

$phoneButtonBG: transparent;
$phoneButtonBGHover: white;
$phoneButtonTextColor: white;
$phoneButtonTextColorHover: $secondaryMainColor;
$phoneButtonSize: $primaryTextSize;
$phoneButtonLineHeight: 32px;
$phoneButtonWeight: 500;
$phoneButtonAlign: center;
$phoneButtonFont: $primaryFont;
$phoneButtonPadding: 9px 26px;
$phoneButtonBorder: 1px solid white;
$phoneButtonBorderRadius: 50px;
$phoneButtonTextTransform: none;
$phoneButtonMinWidth: 140px;

$smallLinkColor: $secondaryMainColor;
$smallLinkFont: $primaryFont;
$smallLinkSize: $detailTextSize;
$smallLinkWeight: 400;
$smallLinkLineHeight: 16px;
$smallLinkTextTransform: uppercase;

.btn-ctn {
    //
}

/* Button Round */
.main-button {
	display: inline-block;

	.icon-alerte{
		font-size: $alertButtonIconSize;
		vertical-align: middle;
		margin-right: 10px;
	}

	&:hover{
		cursor: pointer;
	}

	&.-primary {
		background: $primaryButtonBG;
		color: $primaryButtonTextColor;
		line-height: $primaryButtonLineHeight;
		text-align: $primaryButtonAlign;
		font-size: $primaryButtonSize;
		font-weight: $primaryButtonWeight;
		font-family: $primaryButtonFont;
		padding: $primaryButtonPadding;
		border: $primaryButtonBorder;
		border-radius: $primaryButtonBorderRadius;
		text-transform: $primaryButtonTextTransform;
		min-width: $primaryButtonMinWidth;
		transition: $primaryAnimation;

		&:hover {
			background: $primaryButtonBGHover;
			color: $primaryButtonTextColorHover;
		}

		@media (max-width:450px) {
			min-width: initial;
		}
	}

	&.-secondary {
		background: $secondaryButtonBG;
		color: $secondaryButtonTextColor;
		line-height: $secondaryButtonLineHeight;
		text-align: $secondaryButtonAlign;
		font-size: $secondaryButtonSize;
		font-weight: $secondaryButtonWeight;
		font-family: $secondaryButtonFont;
		padding: $secondaryButtonPadding;
		border: $secondaryButtonBorder;
		border-radius: $secondaryButtonBorderRadius;
		text-transform: $secondaryButtonTextTransform;
		min-width: $secondaryButtonMinWidth;
		transition: $primaryAnimation;

		&:hover {
			background: $secondaryButtonBGHover;
			color: $secondaryButtonTextColorHover;
		}

		@media (max-width:450px) {
			min-width: initial;
		}
	}

	&.-ghost {
		background: $ghostButtonBG;
		color: $ghostButtonTextColor;
		line-height: $ghostButtonLineHeight;
		text-align: $ghostButtonAlign;
		font-size: $ghostButtonSize;
		font-weight: $ghostButtonWeight;
		font-family: $ghostButtonFont;
		padding: $ghostButtonPadding;
		border: $ghostButtonBorder;
		border-radius: $ghostButtonBorderRadius;
		text-transform: $ghostButtonTextTransform;
		min-width: $ghostButtonMinWidth;
		transition: $primaryAnimation;



		&:hover {
			background: $ghostButtonBGHover;
			color: $ghostButtonTextColorHover;
			border: $ghostButtonBorderHover;
		}

		@media (max-width:450px) {
			min-width: initial;
		}
	}

	&.-grey{
		color: #666;
		border-color: #666;

		&:hover {
			background: transparent;
			border-color: $primaryColor;
		}
	}

	&.-white {
		background: $whiteButtonBG;
		color: $whiteButtonTextColor;
		line-height: $whiteButtonLineHeight;
		text-align: $whiteButtonAlign;
		font-size: $whiteButtonSize;
		font-weight: $whiteButtonWeight;
		font-family: $whiteButtonFont;
		padding: $whiteButtonPadding;
		border: $whiteButtonBorder;
		border-radius: $whiteButtonBorderRadius;
		text-transform: $whiteButtonTextTransform;
		min-width: $whiteButtonMinWidth;
		transition: $primaryAnimation;

		&:hover {
			background: $whiteButtonBGHover;
			color: $whiteButtonTextColorHover;
			border: $whiteButtonBorderHover;
		}

		@media (max-width:450px) {
			min-width: initial;
		}
	}

	&.-primary-small {
		background: $primarySmallButtonBG;
		color: $primarySmallButtonTextColor;
		line-height: $primarySmallButtonLineHeight;
		text-align: $primarySmallButtonAlign;
		font-size: $primarySmallButtonSize;
		font-weight: $primarySmallButtonWeight;
		font-family: $primarySmallButtonFont;
		padding: $primarySmallButtonPadding;
		border: $primarySmallButtonBorder;
		border-radius: $primarySmallButtonBorderRadius;
		text-transform: $primarySmallButtonTextTransform;
		min-width: $primarySmallButtonMinWidth;
		transition: $primaryAnimation;

		&:hover {
			background: $primarySmallButtonBGHover;
			color: $primarySmallButtonTextColorHover;
		}
	}

	&.-secondary-small {
		background: $secondarySmallButtonBG;
		color: $secondarySmallButtonTextColor;
		line-height: $secondarySmallButtonLineHeight;
		text-align: $secondarySmallButtonAlign;
		font-size: $secondarySmallButtonSize;
		font-weight: $secondarySmallButtonWeight;
		font-family: $secondarySmallButtonFont;
		padding: $secondarySmallButtonPadding;
		border: $secondarySmallButtonBorder;
		border-radius: $secondarySmallButtonBorderRadius;
		text-transform: $secondarySmallButtonTextTransform;
		min-width: $secondarySmallButtonMinWidth;
		transition: $primaryAnimation;

		&:hover {
			background: $secondarySmallButtonBGHover;
			color: $secondarySmallButtonTextColorHover;
		}
	}

	&.-alert{
		background: $alertButtonBG;
		color: $alertButtonTextColor;
		text-align: $alertButtonAlign;
		font-size: $alertButtonSize;
		font-weight: $alertButtonWeight;
		font-family: $alertButtonFont;
		padding: $alertButtonPadding;
		border: $alertButtonBorder;
		border-radius: $alertButtonBorderRadius;
		text-transform: $alertButtonTextTransform;
		min-width: $alertButtonMinWidth;
		transition: $primaryAnimation;

		&:hover {
			background: $alertButtonBGHover;
			color: $alertButtonTextColorHover;
		}
	}

	&.-favorite {
		background: $favoriteButtonBG;
		color: $favoriteButtonTextColor;
		line-height: $favoriteButtonLineHeight;
		text-align: $favoriteButtonAlign;
		font-size: $favoriteButtonSize;
		font-weight: $favoriteButtonWeight;
		font-family: $favoriteButtonFont;
		padding: $favoriteButtonPadding;
		border: $favoriteButtonBorder;
		border-radius: $favoriteButtonBorderRadius;
		text-transform: $favoriteButtonTextTransform;
		min-width: $favoriteButtonMinWidth;
		transition: $primaryAnimation;

		&:hover {
			background: $favoriteButtonBGHover;
			color: $favoriteButtonTextColorHover;
		}
	}

	&.-phone {
		background: $phoneButtonBG;
		color: $phoneButtonTextColor;
		line-height: $phoneButtonLineHeight;
		text-align: $phoneButtonAlign;
		font-size: $phoneButtonSize;
		font-weight: $phoneButtonWeight;
		font-family: $phoneButtonFont;
		padding: $phoneButtonPadding;
		border: $phoneButtonBorder;
		border-radius: $phoneButtonBorderRadius;
		text-transform: $phoneButtonTextTransform;
		min-width: $phoneButtonMinWidth;
		transition: $primaryAnimation;

		&:hover {
			background: $phoneButtonBGHover;
			color: $phoneButtonTextColorHover;
		}
	}

	&.unable {
		cursor: default;
		pointer-events: none;
		opacity: 0.5;
	}

	&:disabled{
		cursor: default;
		pointer-events: none;
		opacity: 0.5;
	}
}

/* Small Link */
.small-link {
	position: relative;
	color: $smallLinkColor;
	font-family: $smallLinkFont;
	font-size: $smallLinkSize;
	font-weight: $smallLinkWeight;
	line-height: $smallLinkLineHeight;
	text-transform: $smallLinkTextTransform;

	&.right {
		padding-right: 0;

		&:hover {
			i {
				right: -5px;
			}
		}
	}

	i {
		position: relative;
		right: 0;
		transition: $primaryAnimation;
	}

	.icon-map-plus {
		font-size: 10px;
		margin-right: 7px;
	}

	.icon-arrow-right {
		padding-left: 5px;
	}

	&.-no-uppercase {
		text-transform: none;
	}
}

