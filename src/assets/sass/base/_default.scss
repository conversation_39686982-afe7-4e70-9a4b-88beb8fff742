*, *:after, *:before {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;

	outline: none !important;
}

html {
	font-size: 1em;
	line-height: 1.4;
}

html,
body {
	position: relative;
	min-width: 320px;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	-webkit-overflow-scrolling: touch;
	font-family: $secondaryFont;

	.grecaptcha-badge {
		visibility: hidden;
	}

	&.-no-scroll {
		overflow: hidden;
	}

	&.-open-menu {
		overflow: hidden;
	}
}

h1 {
	font-family: $h1TitleFont;
	@include fontSize($h1TitleSize, $h1TitleLineHeight, $h1TitleWeight, $h1TitleTransform);
	text-transform: uppercase;
	color: $secondaryMainColor;

	@media (max-width: 768px) {
		font-size: calc(0.75 * $h1TitleSize);
	}
}

h2 {
	font-family: $h2TitleFont;
	@include fontSize($h2TitleSize, $h2TitleLineHeight, $h2TitleWeight, $h2TitleTransform);
	text-transform: uppercase;
	color: $secondaryMainColor;

	@media (max-width: 768px) {
		font-size: calc(0.75 * $h2TitleSize);
	}
}

h3 {
	font-family: $h3TitleFont;
	@include fontSize($h3TitleSize, $h3TitleLineHeight, $h3TitleWeight, $h3TitleTransform);

	@media (max-width: 768px) {
		font-size: calc(0.75 * $h3TitleSize);
	}
}

h4 {
	font-family: $h4TitleFont;
	@include fontSize($h4TitleSize, $h4TitleLineHeight, $h4TitleWeight, $h4TitleTransform);

	@media (max-width: 768px) {
		font-size: calc(0.75 * $h4TitleSize);
	}
}

a {
	@include transition(background 0.3s ease-in);
	text-decoration: none;
	outline: none;
}

img {
	max-width: 100%;
}

audio,
canvas,
img,
video {
	vertical-align: middle;
}

iframe {
	max-width: 100%;
}

code {
	white-space: normal;
}

strong {
	font-weight: 700;
}

ul {
	list-style: none;
}

nav {
	background: white;
	box-shadow: none;

	ul li {
		float: none;
	}
}

input, textarea {
	&:focus {
		border-color: inherit;
		box-shadow: none;
		outline: none;
	}
}

a, button {
	&:focus {
		outline: none;
	}
}

input[type=search]:not(.browser-default):focus:not([readonly]) {
	border-color: inherit;
	box-shadow: none;
}

input[type=search]:not(.browser-default) {
	margin-bottom: 0;
}

#hbl-live-chat-wrapper {
	display: none;
}

.swiper-btn {
	&:focus {
		outline: none;
	}
}

body {
	> div {
		&:last-child {
			display: block !important;
		}
	}
}

#banner-ie {
	display: none;
	background: #fdf2ab;
    background-position: 8px 17px;
    z-index: 111111;
    border-bottom: 1px solid #a29330;
    text-align: left;
    cursor: pointer;
    background-color: #fff8ea;
    font: 17px Calibri,Helvetica,Arial,sans-serif;

	div {
		padding: 11px 12px 11px 30px;
		line-height: 1.7em;

		a {
			text-indent: 0;
			color: #fff;
			text-decoration: none;
			box-shadow: 0 0 2px rgba(0,0,0,.4);
			padding: 1px 10px;
			border-radius: 4px;
			font-weight: 400;
			background: #029df7;
			white-space: nowrap;
			margin: 0 2px;
			display: inline-block;
		}

		#chrome {
			background-color: #5ab400;
		}

		#firefox {
			background-color: #f7ae02;
		}
	}

	@media all and (-ms-high-contrast:none)
     {
     	display:block !important;
     	*::-ms-backdrop { display:block !important;} /* IE11 */
     }
}
