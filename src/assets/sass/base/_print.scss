@media print {
	* {
		background: transparent !important;
		color: #000 !important;
		/* Black prints faster: h5bp.com/s */
		box-shadow: none !important;
		text-shadow: none !important;
	}

	a,
	a:visited {
		text-decoration: underline;
	}

	/* Don't show links for images, or javascript/internal links */

	.ir a:after,
	a[href^="javascript:"]:after,
	a[href^="#"]:after {
		content: "";
	}

	pre,
	blockquote {
		border: 1px solid #999;
		page-break-inside: avoid;
	}

	thead {
		display: table-header-group;
		/* h5bp.com/t */
	}

	tr,
	img {
		page-break-inside: avoid;
	}

	img {
		max-width: 100% !important;
    display: block;
	}

	@page {
    size: auto;
    margin:0.5cm;
	}

	p,
	h2,
	h3 {
		orphans: 3;
		widows: 3;
	}

	h2,
	h3 {
		page-break-after: avoid;
	}

	.-no-print {
		display: none !important;
	}

	/*
		Fiche Specific Print Style
	*/

  #header{
    position: relative !important;
    height: 100px !important;

    .main-header{
      height: 80px !important;
    }
  }



	.sub-header, .sub-footer {
		display: none;
	}

	.property-hero-cpn {
		.button-ctn {
			display: none;
		}
		.container {
			left: 0;
			transform: none;
      right: 0;
      bottom: 0;
      position: relative;

			.absolute-ctn {
				position: relative;
        display: block;
				text-align: right;
        width: 100% !important;
        right: 0;

        .button-ctn{
          display: none !important;
        }

        .price-tag{
          padding-left: 0;
          float: right;

          span{
            padding-top: 10px;
            padding-bottom: 10px;
          }
        }
			}
		}

    .swiper-wrapper{
      transform: translate(0,0);
    }

    .swiper-slide{
      display: none;
    }

    .swiper-slide-active{
      width: 100% !important;
      display: block;

      img{
        width: 100% !important;
      }
    }

	}

  .property-sheet{
    padding-top: 0;

    .info{
      padding-top: 10px;
    }

    .description{
      margin-top: 20px;
      page-break-after: always;
    }

    .emphase-title{
      margin-top: 40px;
      margin-bottom: 20px;
    }
  }

	.table-ctn {
		.table-wrap {
			max-height: inherit !important;
		}

		.small-link {
			display: none !important;
		}
	}

	.property-tools-cpn {
		.tabs-ctn {
			max-width: 60%;
		}
	}

	.properties-list-cpn {
		display: none !important;
	}

	.property-share-cpn {
		display: none !important;
	}

	.property-navigation-cpn {
		display: none !important;
	}

  .property-tools-cpn {
    display: none !important;
  }

  .property-map-cpn {
    display: none !important;
  }

  .characteristics-ctn{

  }

	.team-card-cpn {
		.main-button {
			display: none;
		}
	}
}
