form {
	.form-step {
		display: none;
		&.show { display: block; }
	}

	.form-row {
		display: flex;
		flex-flow: row wrap;
		width: 100%;
		margin-bottom: 20px;

		&.-bottom { align-items: flex-end; }
	}

	.-radio-ctn {
		input[type="radio"] {
			float: left;
			position: absolute;
			left: -9999px;

			&:checked + label:after { opacity: 1; }
			&:not(:checked) + label:after { opacity: 0; }
		}

		label {
			position: relative;
			top: 0;
			left: 0;
			display: inline-block;
			padding-left: 30px;
			color: $formLabelColor;
			@include fontSize($formLabelSize, $formLabelLineHeight);
			cursor: pointer;

			&:before {
				content: "";
				position: absolute;
				top: 50%;
				left: 0;
				width: 20px;
				height: 20px;
				border: 1px solid $secondaryComplementaryColor;
				border-radius: 10px;
				transform: translateY(-50%);
			}

			&:after {
				content: "";
				position: absolute;
				top: 50%;
				left: 4px;
				width: 12px;
				height: 12px;
				background-color: $primaryColor;
				border-radius: 6px;
				transform: translateY(-50%);
				transition: all .4s ease-in-out;
			}
		}
	}

	.input-ctn {
		width: 100%;

		input {
			display: block;
			width: 100%;
		}

		label:not(.main-button) {
			display: inline-block;
			margin-bottom: 15px;
			font-family: $secondaryFont;
			color: $formLabelColor;
			@include fontSize($formLabelSize, $formLabelLineHeight);
		}

		&.-dual {
			width: calc(100% * 1/2 - 10px);
			margin-right: 20px;
			&:nth-child(2n) { margin-right: 0; }

			@media (max-width:425px) {
				width: 100%;
				margin-right: 0;
				margin-bottom: 20px;
				&:nth-child(2n) { margin-bottom: 0; }
			}
		}

		&.-half {
			width: 50%;
			@media (max-width:425px) { width: 100%; }
		}

		&.-small {
			width: 130px;
		}
	}

	// INPUT
	input{
		height: $inputHeight;
		padding: $inputPadding;
		border: $inputBorder;
		border-radius: $inputRadius;
		font-family: $inputFont;
		@include fontSize($inputSize, $inputLineHeight);
		color: $inputColor;

		&.-error { border-color: $error; }
	}

	textarea {
		width: 100%;
		height: 100px;
		padding: 15px 15px 0px;
		border: $inputBorder;
		border-radius: $inputRadius;
		font-family: $inputFont;
		@include fontSize($inputSize);
		color: $inputColor;
		resize: vertical;

		&.-error { border-color: $error; }
	}

	// CHECKBOX
	.custom-checkbox, .custom-radio {
		position: absolute;
		opacity: 0;

		& + label {
			position: relative;
			cursor: pointer;
			padding: 0;
			font-family: $secondaryFont;
			@include fontSize(15px, 40px);
			color: $secondaryMainColor;
		}

		// Box.
		& + label:before {
			content: '';
			margin-right: 10px;
			display: inline-block;
			vertical-align: middle;
			width: 16px;
			height: 16px;
			background: white;
			border-radius: 3px;
			box-sizing: border-box;
			border: solid 1px #DDDDDD;
			transition: $primaryAnimation;
		}

		// Box hover
		&:hover + label:before {
			background: $primaryColor;
			border-color: $primaryColor;
		}

		// Box focus
		&:focus + label:before {
			// box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.12);
		}

		// Box checked
		&:checked + label:before {
			background: $primaryColor;
			border: none;
		}

		// Disabled state label.
		&:disabled + label {
			color: #b8b8b8;
			cursor: auto;
		}

		// Disabled box.
		&:disabled + label:before {
			box-shadow: none;
			background: #ddd;
		}

		// Checkmark. Could be replaced with an image
		&:checked + label:after {
			content: '';
			position: absolute;
			left: 3px;
			top: 10px;
			background: white;
			width: 2px;
			height: 2px;
			box-shadow:
							2px 0 0 white,
					 4px 0 0 white,
					 4px -2px 0 white,
					 4px -4px 0 white,
					 4px -6px 0 white,
					 4px -8px 0 white;
			transform: rotate(45deg);
		}
	}

	.custom-radio{
		& + label:before {
			border-radius: 50%;
		}

		&:checked + label:after {
			content: '';
			position: absolute;
			left: 4px;
			top: 7px;
			background: white;
			width: 8px;
			height: 8px;
			box-shadow: none;
			transform: none;
			border-radius: 50%;
		}
	}
}

// Alter global ng-select styling
.ng-select {
	outline: none;
	background-color: white;
	border-radius: $inputRadius;

	// Global text
	.ng-select-container,
	.ng-dropdown-panel .ng-dropdown-panel-items .ng-option {
		font-family: $inputFont;
		@include fontSize($inputSize);
		color: $inputColor;
	}

	// Field wrapper borders
	.ng-select-container,
	&.ng-select-opened > .ng-select-container {
		border: $inputBorder;
		border-radius: $inputRadius;
	}

	// Placeholder
	.ng-select-container .ng-value-container .ng-placeholder {
		color: $placeHolderColor;
		opacity: $placeHolderOpacity;
	}

	// Select arrow color
	.ng-arrow-wrapper .ng-arrow { border-color: $inputArrowColor transparent transparent; }
	.ng-arrow-wrapper:hover .ng-arrow { border-top-color: $inputArrowColor; }
	&.ng-select-opened > .ng-select-container .ng-arrow { border-color: transparent transparent $inputArrowColor; }

	// Input wrapper height and position
	&.ng-select-single .ng-select-container {
		height: $inputHeight;

		.ng-value-container { padding-left: 15px; }
		.ng-value-container .ng-input { top: 0px; }
	}

	// Dropdown borders and padding
	.ng-dropdown-panel {
		overflow: hidden;
		border-right: $inputBorder;
		border-left: $inputBorder;
		border-bottom: $inputBorder;
		border-bottom-left-radius: $inputRadius;
		border-bottom-right-radius: $inputRadius;

		.ng-dropdown-panel-items .ng-option { padding: 10px 15px; }
	}
}

// Datepickers
.datepicker-ctn {
	position: relative;

	&::after {
		@include pseudoElement($content: "\e90b");
		bottom: 8px;
		right: 12px;
		font-family: "icomoon";
		@include fontSize(18px);
		color: $grey;
		pointer-events: none;
	}
}

// Add 'large' class to inputs or ng-select for alternate input height
input.large,
.ng-select.large .ng-select-container {
	height: $inputHeightLarge;
	input { height: $inputHeightLarge; }
}

// Placeholders
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder { /* Chrome/Opera/Safari */
	color: $placeHolderColor;
	opacity: $placeHolderOpacity;
	font-family: $secondaryFont;
	@include fontSize($inputSize);
}
input::-moz-placeholder,
textarea::-moz-placeholder { /* Firefox 19+ */
	color: $placeHolderColor;
	opacity: $placeHolderOpacity;
	font-family: $secondaryFont;
	@include fontSize($inputSize);
}
input:-ms-input-placeholder,
textarea:-ms-input-placeholder { /* IE 10+ */
	color: $placeHolderColor;
	opacity: $placeHolderOpacity;
	font-family: $secondaryFont;
	@include fontSize($inputSize);
}
input:-moz-placeholder,
textarea:-moz-placeholder { /* Firefox 18- */
	color: $placeHolderColor;
	opacity: $placeHolderOpacity;
	font-family: $secondaryFont;
	@include fontSize($inputSize);
}

// Messages
.form-control-feedback{
	color: $error;
	font-size: 0.85rem;
	width: 100%;

	p { margin-bottom: 0; }
}

.form-response{
	left: 0;
	right: 0;
	padding: 0 15px;
	.message{
		text-align: center;
		margin: 35px auto 20px;
		color: #555555;
		font-family: $secondaryFont;
		@include fontSize(14px, 24px);
		width: 100%;
	}
}

// Google search dropdown
.pac-container {
	.pac-item{
		height: 40px;
		padding: 5px 14px;
		&:first-child { border-top: 0 none; }
		&:hover { background-color: #ebf5ff; }
	}
}
