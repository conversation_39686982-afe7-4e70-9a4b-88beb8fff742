/*
	Contact Sheet Components
*/

.contact-sheet-cpn {
	padding-top: 60px;
	padding-bottom: 100px;
	background-color: #F2F2F2;

	@media (max-width: 768px) {
		padding-bottom: 45px;
	}

	.container {
		//border-radius: 3px;
		//box-shadow: 0 2px 4px 0 rgba(0,0,0,0.1);
	}

	.form-container {
		position: relative;
		&.send {
			display: flex;
			justify-content: center;
			align-items: center;

			.contact-form,
			.contact-form-ctn {
				opacity: 0;
			}
		}

		.loading-inner {
			opacity: 0.5;
		}

		.form-loader {
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%,-50%);
		}

		.form-response {
			position: absolute;
			text-align: center;
			opacity: 0;
			padding: 0 15px;

			.button-ctn {
				justify-content: center;

				a{
					margin: 0 auto;
				}
			}

			&.show {
				opacity: 1;
				transition: all 0.5s ease-in-out;
			}
		}
	}

	.contact-form-ctn {
		padding-left: 0;
		padding: 80px 100px;

		&.no-padding {
			padding: 0;
		}

		@media (max-width: 768px) {
			padding: 60px 0;
		}
	}

	.info-box-ctn {
		padding-right: 0;

		@media (max-width: 768px) {
			padding-left: 0;
			margin-top: 40px;
		}
	}

	.-page-title {
		margin-top: 0;
		margin-bottom: 20px;
		font-size: 38px;
		line-height: 49px;
	}

	.-page-description {
		margin: 20px 0 0;
		color: $secondaryMainColor;
		font-family: $secondaryFont;
		font-size: $primaryTextSize;
		line-height: $primaryTextLineHeight;
	}

	.-page-required {
		margin: 25px 0 0;
		color: $secondaryMainColor;
		font-family: $secondaryFont;
		font-size: 12px;

		&--right {
			text-align: right;
		}
	}

	.-form-result {
		.success {
			color: $primaryColor;
			font-size: 0.9rem;
		}
		.error {
			color: #bb0303;
			font-size: 0.9rem;
		}
	}

	.info-box {
		.info-text-ctn {
			padding: 35px 0;

			@media (max-width: 992px) {
				padding: 20px 0;
			}

			.logo-ctn {
				margin-bottom: 20px;
			}

			.title {
				margin-top: 0;
			}

			.block {
				display: flex;
				margin-bottom: 20px;

				&:last-child {
					margin-bottom: 0;
				}

				i {
					font-size: $subTextSize;
					color: $secondaryMainColor;
					position: relative;
					top: 4px;
				}

				p, a {
					margin: 0;
					font-family: $secondaryFont;
					color: $secondaryMainColor;
				}

				.text {
					padding-left: 15px;
				}
			}

			.location-ctn {
				i {
					font-size: $primaryTextSize;
				}

				.text {
					font-size: $detailTextSize;
					line-height: 19px;

					.links {
						margin-top: 6px;
					}

					a {
						margin-right: 20px;
						color: $secondaryMainColor;
						font-family: $primaryFont;
						font-size: 12px;
						font-weight: 600;
						text-transform: uppercase;
						line-height: $detailTextLineHeight;
					}
				}
			}

			.email-ctn {
				.text {
					font-size: $detailTextSize;
					line-height: 19px;
				}
			}

			.phone-ctn {
				.text a {
					font-size: $subTextSize;
					font-weight: 600;
					line-height: 22px;
					font-family: $primaryFont;
					display: block;
				}
			}
		}
	}

	.contact-form {
		padding-top: 60px;

		@media (max-width: 768px) {
			padding-top: 20px;
		}

		button {
			margin-top: 30px;

			@media (max-width: 768px) {
				display: block;
				margin: 0 auto;
			}
		}
	}

	&.-contact-sheet-2 {
		padding-bottom: 75px;

		.contact-form {
			padding-top: 0;
			@include clearfix;
		}
	}

	&.-contact-sheet-3 {
		padding-bottom: 75px;

		.contact-form {
			padding-top: 0;
			@include clearfix;

			button {
				margin-top: 5px;
				float: right;
			}

			textarea {
				height: 200px;
			}
		}
	}

	.form-head {
		position: relative;
		padding-bottom: 40px;

		.-page-title {
			color: $primaryColor;
			text-transform: uppercase;
		}
	}

	.step {
		position: absolute;
		top: 0;
		right: 0;
		border: 2px solid $secondaryMainColor;
		border-radius: 20px;
		padding: 12px 26px;

		p {
			margin: 0;
			color: $secondaryMainColor;
			font-family: $primaryFont;
			font-size: $detailTextSize;
			font-weight: bold;
			line-height: 17px;
			text-align: center;
		}
	}

	.input-ctn {
		padding-top: 40px;
	}

	.form-row {
		margin: 0;
	}

	.button-ctn {
		display: flex;
		align-items: center;
		justify-content: flex-start;
		padding-top: 40px;
	}
}

.contact-map {
	height: 500px;

	.mapbox-logo,
	.leaflet-control-attribution {
		display: none;
	}
}

#career-contact {
	background-color: #F4F4F4;

	.block {
		display: block;
	}

	.form-container {
		background: white;
	}

	form .ng-select.ng-select-single .ng-select-container {
		height: 50px;
	}

	form .ng-select.align-center .ng-option {
		text-align: left;
	}

	.emphase-title {
		margin-top: 50px;
	}

	@media (max-width: 768px) {
		.contact-form-ctn {
			padding: 60px 15px;
		}
	}

	#step2 {
		.emphase-title {
			margin-top: 30px;
		}
	}

	.dual-ctn {
		.input-ctn {
			width: 50%;
			padding-top: 25px;

			@media (max-width: 768px) {
				width: 100%;

				&:first-child {
					padding-right: 0px;
				}

				&:last-child {
					padding-left: 0px;
				}
			}

			&:first-child {
				padding-left: 0px;
			}

			&:last-child {
				padding-right: 0px;
			}
		}
	}

	.mydp {
		height: 50px;

		.selbtngroup,
		input {
			height: 48px !important;
		}

		.btnpicker {
			width: 30px;
		}

		input {
			padding-left: 20px;
		}

		input::placeholder {
			color: #666666;
			font-family: "Open Sans", Arial, sans-serif;
			font-size: 15px;
		}
	}
}
