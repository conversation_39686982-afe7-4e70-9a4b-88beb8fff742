/*
	Property Groups Page
*/

$propertyGroupsPagePaddingTop: 80px;
$propertyGroupsPageBG: #F4F4F4;

$propertyGroupsPageDescription: #777777;

$propertyGroupsPageCardPaddingTop: 90px;
$propertyGroupsPageCardPaddingBottom: 60px;

.propertygroups-page {
	padding-top: $propertyGroupsPagePaddingTop;
	background-color: $paleBackground;

	.property-group-head {
		text-align: center;

		.page-title {
			margin-top: 0;
			margin-bottom: 30px;
		}

		.page-description {
			margin: 0 auto 30px;
			max-width: 510px;
			color: $propertyGroupsPageDescription;
			font-family: $secondaryFont;
			font-size: $subTextSize;
			line-height: $subTextLineHeight;
			text-align: center;
		}

		.main-button {
			margin: auto;
		}
	}

	.property-group-card-ctn {
		padding-top: $propertyGroupsPageCardPaddingTop;
		padding-bottom: $propertyGroupsPageCardPaddingBottom;
	}
}

.propertygroup-description {
	padding-bottom: 30px;
}

.property-downloads-cpn {
	padding-bottom: 40px;
}