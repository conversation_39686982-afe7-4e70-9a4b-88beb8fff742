/*
	Buy Page
*/

$buyPageInfoBoxDescriptionColor: $secondaryMainColor;


.buy-sheet-page {
	padding-top: 80px;

	opacity: 0;
	transition: 0.5s all ease;

	&.show{
		opacity: 1;
	}

	@media (max-width: 768px) {
		padding-top: 60px;
	}

	.page-list-ctn {
		padding-left: 0;
	}

	.team-info-wrap {
		padding-right: 0;
		padding-bottom: 30px;

		@media (max-width: 992px) {
			padding-left: 0px;
		}
	}

	.page-title {
		margin-top: 0px;
	}

	.page-description {
		color: $buyPageInfoBoxDescriptionColor;
		font-family: $secondaryFont;
		font-size: $subTextSize;
		line-height: $subTextLineHeight;
	}
}

/*
Text Card 1 Component
*/

.buy-guide-cta-1-cpn.text-card-1-cpn{
	padding: 80px 0;

	@media (max-width: 768px) {
		padding: 60px 0;
	}

	&.homestaging-cta-cpn {
		@media (max-width: 768px) {
			.text-wrap {
				margin-bottom: 20px;
			}
		}
	}

	&.-grey {
		background-color: #F2F2F2;
	}

	.text-wrap {
		@media (max-width: 768px) {
			margin-top: 40px;
		}

		.title {
			text-transform: uppercase;
			color: $secondaryMainColor;
		}

		p {
			margin-bottom: 30px;
			color: $secondaryMainColor;
			font-family: $secondaryFont;
			font-size: $subTextSize;
			line-height: $subTextLineHeight;
		}
	}

	.img-ctn{
		text-align: center;
	}

	.container {
		display: flex;
		align-items: center;

		@media (max-width: 768px) {
			display: block;
		}
	}

	.col-sm-6 {
		@media (max-width: 768px) {
			padding: 0;
			text-align: center;

			&.col-sm-offset-1 {
				padding: 0 15px;
			}
		}
	}
}
