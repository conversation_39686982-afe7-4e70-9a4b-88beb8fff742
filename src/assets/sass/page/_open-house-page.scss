/*
	Open House Page
*/

$openHouseBackground: #fbf7ee;

.open-house-page {
	padding-bottom: 74px;
	background: $openHouseBackground;

	.open-house-head {
		form {
      position: relative;
      width: max-content;
      margin: 30px auto;
      
      &::after {
        position: absolute;
        display: block;
        font-family: 'icomoon';
        content: "\e90b";
        top: 10px;
        right: 14px;
        font-size: $primaryTextSize;
      }

      input { width: 290px; }
		}
	}

	.visit-date {
		color: $black;
		font-family: $primaryFont;
		font-size: 22px;
		line-height: 27px;
		text-align: center;
	}

	.no-results{
		text-align: center;
		margin-top: 70px;
		color: #777777;
	}

	.button-ctn {
		padding-top: 70px;
		text-align: center;
    .main-button { margin: auto; }
	}
}
