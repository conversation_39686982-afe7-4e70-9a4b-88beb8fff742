/*
	Blog Details Page
*/

$articleShareSize: 16px;
$articleShareColor: $secondaryMainColor;

.blog-details-page {
  padding-top: 20px;
  padding-bottom: 100px;

  @media (max-width: 768px) {
    padding-top: 0px;
    padding-bottom: 70px;
  }

  .page-title {
    text-transform: uppercase;
    color: $secondaryMainColor;
  }

  .main-img-ctn {
    text-align: center;

    img{
      width: 100%;
    }
  }

  .article-share-cpn {
    display: flex;
    justify-content: space-between;

    div {
      display: flex;
      align-items: baseline;
    }

    .small-link{
      i{
        color: $secondaryComplementaryColor;
      }
    }
  }

  .details-info {
    padding-bottom: 40px;

    p {
      margin: 0px;
      display: inline-block;
      color: $secondaryComplementaryColor;
      font-family: $secondaryFont;
      font-size: 13px;
      line-height: 24px;
    }

    .date {
      padding-right: 20px;
      border-right: 1px solid #D8D8D8;
      color:$secondaryMainColor;
      font-weight: bold;

    }

    .categories {
      margin-left: 20px;
      color:$secondaryMainColor;
      font-weight: bold;
    }
  }

  .main-text-ctn {
    padding-top: 15px;

    p {
      color: $secondaryMainColor;
      font-family: $secondaryFont;
      font-size: $subTextSize;
      line-height: $subTextLineHeight;
    }

    .btn {
      background: $primarySmallButtonBG;
      color: $primarySmallButtonTextColor;
      line-height: $primarySmallButtonLineHeight;
      text-align: $primarySmallButtonAlign;
      font-size: $primarySmallButtonSize;
      font-weight: $primarySmallButtonWeight;
      font-family: $primarySmallButtonFont;
      padding: $primarySmallButtonPadding;
      border: $primarySmallButtonBorder;
      border-radius: $primarySmallButtonBorderRadius;
      text-transform: $primarySmallButtonTextTransform;
      min-width: $primarySmallButtonMinWidth;
      transition: $primaryAnimation;

      &:hover {
        background: $primarySmallButtonBGHover;
        color: $primarySmallButtonTextColorHover;
      }
    }

    ul {
      padding-left: 0px;
      padding-bottom: 30px;

      li {
        position: relative;
        padding: 16px 0px 16px 40px;
        color: $listColor;
        font-family: $secondaryFont;
        font-size: $listSize;
        line-height: $listLineHeight;
        border-bottom: $listBorderBottom;

        &:before {
          color: $primaryColor;
          font-family: 'icomoon';
          speak: none;
          font-style: normal;
          font-weight: normal;
          font-variant: normal;
          text-transform: none;
          line-height: 1;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
          content: "\e90c";
          position: absolute;
          left: 0;
          top: 22px;
          //transform: translate(0%, -50%);
        }

        p{
          margin: 0;
        }
      }
    }
  }

  .article-share-cpn {
    padding-top: 20px;
    padding-bottom: 60px;

    @media (max-width: 768px) {
      padding-bottom: 40px;
    }

    .fa, i, .share-btn:before {
      font-size: $articleShareSize;
      color: $articleShareColor;
    }

    .share-btn {
      background: none;
      border: none;
    }
  }

  .fb-comments {
    padding-top: 100px;
    width: 100% !important;

    @media (max-width: 768px) {
      padding-top: 50px;
    }

    > span {
      width: 100% !important;
    }

    iframe {
      width: 100% !important;
    }
  }
}
