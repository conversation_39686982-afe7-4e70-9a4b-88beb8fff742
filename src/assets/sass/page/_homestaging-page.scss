/*
	Homestaging Page
*/

$homestagingPageInfoBoxBorder: 5px solid #CCCCCC;

$homestagingPageInfoBoxTitleSize: 22px;
$homestagingPageInfoBoxTitleColor: #000000;
$homestagingPageInfoBoxTitleLineHeight: 27px;

$homestagingPageInfoBoxDescriptionSize: 14px;
$homestagingPageInfoBoxDescriptionColor: #666666;
$homestagingPageInfoBoxDescriptionLineHeight: 24px;

.homestaging-sheet-page {
	padding-top: 80px;
	padding-bottom: 80px;

	@media (max-width: 768px) {
		padding-top: 60px;
	}

	.page-list-ctn {
		padding-left: 0;
	}

	.team-info-wrap {
		padding-right: 0;

		@media (max-width: 992px) {
			padding-left: 0px;
			padding-bottom: 30px;
		}
	}

	.page-title {
		margin-top: 0px;
	}

	.page-description {
		color: $homestagingPageInfoBoxDescriptionColor;
		font-family: $secondaryFont;
		font-size: $subTextSize;
		line-height: $subTextLineHeight;
	}

	.team-info-box {
		padding: 40px 20px 50px;
		border: $homestagingPageInfoBoxBorder;
		text-align: center;
		@include clearfix;

		.team-info-box-content {
			margin: 0 auto;
			float: none;
		}

		.title {
			margin: 0;
			margin-bottom: 30px;
			color: $homestagingPageInfoBoxTitleColor;
			font-family: $primaryFont;
			font-size: $homestagingPageInfoBoxTitleSize;
			line-height: $homestagingPageInfoBoxTitleLineHeight;
		}

		.description {
			margin-top: 0;
			margin-bottom: 30px;
			color: $homestagingPageInfoBoxDescriptionColor;
			font-family: $secondaryFont;
			font-size: $homestagingPageInfoBoxDescriptionSize;
			line-height: $homestagingPageInfoBoxDescriptionLineHeight;
			text-align: center;
		}

		.main-button {
			min-width: 100%;

			@media (max-width: 992px) {
				min-width: inherit;
			}
		}
	}
}


.homestaging-card-ctn {
	padding: 80px 0px;
	background-color: #F4F4F4;

	@media (max-width: 768px) {
		padding: 60px 0px;
	}

	.title {
		margin-top: 0;
		margin-bottom: 50px;
		text-align: center;
	}

	.homestaging {
		padding-bottom: 40px;

		@media (max-width: 768px) {
			justify-content: center;
		}

		.wrap {
			display: flex;
			flex-flow: row wrap;
		}
	}

	.button-ctn {
		text-align: center;
    .main-button { margin: auto; }
	}
}
