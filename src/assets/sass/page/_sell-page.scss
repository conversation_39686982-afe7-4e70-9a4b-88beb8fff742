/*
	Sell Page
*/

$sellPageInfoBoxDescriptionColor: $secondaryMainColor;

.sell-sheet-page {
	padding-top: 80px;
	padding-bottom: 40px;
	opacity: 0;
	transition: 0.5s all ease;

	&.show{
		opacity: 1;
	}

	@media (max-width: 768px) {
		padding-top: 60px;
	}

	.page-list-ctn {
		padding-left: 0;
	}

	.team-info-wrap {
		padding-right: 0;

		@media (max-width: 992px) {
			padding-left: 0;
			padding-bottom: 30px;
		}
	}

	.page-title {
		margin-top: 0px;
	}

	.page-description {
		color: $sellPageInfoBoxDescriptionColor;
		font-family: $secondaryFont;
		font-size: $subTextSize;
		line-height: $subTextLineHeight;
	}
}
