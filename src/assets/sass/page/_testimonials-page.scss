/*
	Testimonials Page
*/

.testimonials-page {
	padding-bottom: 75px;
	background: #F6F6F6;

	.page-title {
		text-transform: uppercase;
		color: $primaryColor;
	}

	.testimonials-wrap {
		margin-top: 40px;
		-moz-column-count:2; /* Firefox */
		-webkit-column-count:2; /* Safari and Chrome */
		column-count:2;

		@media (max-width: 992px) {
			display: block;
			column-count: inherit;
			column-gap: inherit;
		}

		iframe {
			position: relative;
			z-index: 12;
			width: 500px;
			height: 200px;
			transform: translate3d(0,0,0) !important;
			display: block;
		}

		.iframe-container {
			margin-bottom: 20px;
			position: relative;
			padding-bottom: 56.25%; /* 16:9 */
			padding-top: 25px;
			height: 0;

			iframe {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				transform: translate3d(0,0,0);
			}
		}

		&.-full{
			column-count: initial;
		}
	}


}
