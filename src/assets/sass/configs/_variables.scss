/*
	Here you can change the main font which is used for Titles and Emphase
*/
$primaryFont: 'Montserrat', Arial, sans-serif;
$primaryBold: 600;

/*
	Here you can change the main font which is used for all the texts
*/
$secondaryFont: '<PERSON>ra', serif;
/*
	Here you can change the secondary font for Landing Pages
*/
$thirdFont: 'Open Sans', Arial, sans-serif;

/*
	Here you can change the colors of the site
*/
$primaryColor: #002758;
$primaryColorDarker: darken($primaryColor, 10);
$complementaryEmphase: #971A1A;
$secondaryMainColor: #00377C;
$secondaryComplementaryColor: #999999;

/*
	Other colors for efficiency
*/
$black: #000000;
$white: #FFFFFF;
$grey: #888888;
$error: #bb0303;
$warning: #f7c111;

$paleBackground: #fbf7ee;

/*
	Animation / Transitions
*/
$primaryAnimation: all 0.45s ease;
$primaryTextAnimation: color 0.35s ease;
$primaryOpacityAnimation: opacity 0.45s ease;
$propertyAnimation: all 0.45s cubic-bezier(.72,.1,.14,1);


/*
	Titles
*/
$h1TitleFont: $primaryFont;
$h1TitleSize: 65px;
$h1TitleLineHeight: normal;
$h1TitleTransform: uppercase;
$h1TitleWeight: 500;
$h1TitleTransform: uppercase;

$h2TitleFont: $primaryFont;
$h2TitleSize: 38px;
$h2TitleLineHeight: 36px;
$h2TitleWeight: 600;
$h2TitleTransform: none;

$h3TitleFont: $primaryFont;
$h3TitleSize: 24px;
$h3TitleLineHeight: normal;
$h3TitleTransform: uppercase;
$h3TitleWeight: 600;
$h3TitleTransform: none;

$h4TitleFont: $primaryFont;
$h4TitleSize: 18px;
$h4TitleLineHeight: 28px;
$h4TitleTransform: uppercase;
$h4TitleWeight: normal;
$h4TitleTransform: none;

$emphaseTitleSize: 24px;
$emphaseLineHeight: 29px;
$emphaseBorderColor: $secondaryMainColor;

/*
	Textes
*/
$primaryTextSize: 18px;
$primaryTextLineHeight: 32px;

$subTextSize: 17px;
$subTextLineHeight: 28px;

$detailTextSize: 14px;
$detailTextLineHeight: 24px;

/*
	List
*/
$listHeight: 56px;
$listSize: $subTextSize;
$listLineHeight: 28px;
$listColor: $secondaryMainColor;
$listBorderBottom: 2px solid #D8D8D8;
$listPaddingLeft: 40px;

/* Open Visit Table */
$OpenVisitTitleSize: $detailTextSize;
$OpenVisitTitleColor: $black;
$OpenVisitDesciptionSize: $detailTextSize;
$OpenVisitDesciptionColor: $black;

/*
	Filters
*/
$filterTabSize: 14px;
$filterTabLineHeight: normal;
$filterTabFont: $primaryFont;
$filterTabWeight: 500;
$filterTabTextTransform: uppercase;
$filterTabActiveColor: $complementaryEmphase;
$filterTabActiveBorderSize: 2px;
$filterTabActiveBorderColor: $complementaryEmphase;
$filterTabColor: $secondaryMainColor;

/*
	Forms & Input
*/
$inputHeight: 50px;
$inputHeightLarge: 60px;
$inputColor: $secondaryMainColor;
$inputPaddingLeft: 15px;
$inputFont: $secondaryFont;
$inputSize: 15px;
$inputLineHeight: 40px;
$inputBorder: 1px solid #DDDDDD;
$inputPadding: 0 15px;
$inputArrowColor: $secondaryMainColor;
$inputRadius: 5px;

$placeHolderColor: $secondaryMainColor;
$placeHolderOpacity: 1;

$formLabelSize: 14px;
$formLabelLineHeight: 19px;
$formLabelFont: $secondaryFont;
$formLabelColor: $secondaryMainColor;

/*
	NOT CUSTOMIZABLE.
	DON'T CHANGE THOSE.
	EXPECIALLY YOU.
	YES I'M TALKING ABOUT YOU.
	..but I made this.
	I SAID NO.
*/

/*
	Grid system
*/
$columns: 12;
$gutter: 20px;
$outsideGutter: 40px;
$columnsWidth: 80px;
