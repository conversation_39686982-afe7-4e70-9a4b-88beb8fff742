/* You can add global styles to this file, and also import other style files */

/* Fonts */
//@import url('//fonts.googleapis.com/css?family=Open+Sans:400,400i,600,700');
@import url('//fonts.googleapis.com/css?family=Roboto:500');

/* Reset */
@import "assets/sass/reset";

/* Configs */

/* If you have/need local fonts */
@import "assets/sass/configs/typography";
@import "assets/sass/configs/variables";
@import "assets/sass/configs/icons";
@import "assets/sass/configs/grid";
@import "assets/sass/configs/laraberg-base";

/* Vendors */
/* If you need to import scss from public directories. */
@import "~@ng-select/ng-select/themes/default.theme.css";
// @import "~@fortawesome/fontawesome-free/css/brands.min.css";
// @import "~@fortawesome/fontawesome-free/css/solid.min.css";
// @import "~@fortawesome/fontawesome-free/css/fontawesome.min.css";
// @import '~@ngx-share/button/themes/default/default-theme';
// @import '~@ngx-share/button/styles/share-buttons';
// @import '~@ngx-share/button/styles/themes/default/default-theme';

/* Mixins + Icons */
@import "assets/sass/helpers/mixins";
/* @import "assets/sass/helpers/animations"; */

/* Layout Default / Shared Sytles */
@import "assets/sass/base/default";
@import "assets/sass/base/buttons";
@import "assets/sass/base/commons";
@import "assets/sass/base/forms";

/* Here you list the css of all the components you need and using */
/* Components */
@import "assets/sass/components/alert/alert-form-cpn";

@import "assets/sass/components/blog/blog-list-cpn";
@import "assets/sass/components/blog/custom-content-cpn";

@import "assets/sass/components/common/footer-cpn";
@import "assets/sass/components/common/pagination-cpn";
@import "assets/sass/components/common/text-card-1-cpn";
@import "assets/sass/components/common/text-card-2-cpn";

@import "assets/sass/components/common/headers/header1/header-cpn";
@import "assets/sass/components/common/headers/header2/header-2";
@import "assets/sass/components/common/headers/header3/header-3";
@import "assets/sass/components/common/headers/header4/header-4";
@import "assets/sass/components/common/headers/header5/header-5";

@import "assets/sass/components/common/headers/header1/panel-cpn";
@import "assets/sass/components/common/headers/header-last-properties-cpn";

@import "assets/sass/components/contact/broker-contact-form.scss";
@import "assets/sass/components/contact/broker-contact-header-1.scss";
@import "assets/sass/components/contact/broker-contact-header-2.scss";
@import "assets/sass/components/contact/broker-contact-list.scss";

@import "assets/sass/components/cta/cta-alert-cpn";
@import "assets/sass/components/cta/cta-evaluation-cpn";
@import "assets/sass/components/cta/cta-alert-small-cpn";
@import "assets/sass/components/cta/cta-evaluation-small-cpn";
@import "assets/sass/components/cta/cta-broker-cpn";
@import "assets/sass/components/cta/cta-info-box-cpn";
@import "assets/sass/components/cta/cta-pdf-cpn";
@import "assets/sass/components/cta/cta-small-cpn";
@import "assets/sass/components/cta/cta-bloc-cpn";

@import "assets/sass/components/evaluation/evaluation-form-cpn";
@import "assets/sass/components/evaluation/evaluation-form-single-page-cpn";
@import "assets/sass/components/evaluation/evaluation-start-cpn";

@import "assets/sass/components/hero/hero-landing-cpn";

@import "assets/sass/components/home-hero-cpn";

@import "assets/sass/components/homestaging/homestaging-card-cpn";
@import "assets/sass/components/homestaging/homestaging-slider-cpn";

@import "assets/sass/components/landing/landing-footer-cpn";
@import "assets/sass/components/landing/landing-form-cpn";
@import "assets/sass/components/landing/landing-header-cpn";
@import "assets/sass/components/landing/landing-sheet-cpn";

@import "assets/sass/components/openhouse/openhouses-list-cpn";
@import "assets/sass/components/openhouse/openhouse-card-large-cpn";

@import "assets/sass/components/programs-list-cpn";
@import "assets/sass/components/instagram-gallery-cpn";

@import "assets/sass/components/property/property-addenda-cpn";
@import "assets/sass/components/property/property-characteristics-cpn";
@import "assets/sass/components/property/property-description-cpn";
@import "assets/sass/components/property/property-details-cpn";
@import "assets/sass/components/property/property-downloads-cpn";
@import "assets/sass/components/property/property-exclusion-cpn";
@import "assets/sass/components/property/property-favorite-cpn";
@import "assets/sass/components/property/properties-featured-cpn";
@import "assets/sass/components/property/properties-slider-cpn";
@import "assets/sass/components/property/properties-big-slider-cpn";
@import "assets/sass/components/property/property-form-contact";
@import "assets/sass/components/property/property-hero-cpn";
@import "assets/sass/components/property/property-inclusion-cpn";
@import "assets/sass/components/property/properties-list-cpn";
@import "assets/sass/components/property/properties-sold-map-cpn";
@import "assets/sass/components/property/property-map-cpn";
@import "assets/sass/components/property/property-navigation-cpn";
@import "assets/sass/components/property/property-pane-cpn";
@import "assets/sass/components/property/property-room-cpn";
@import "assets/sass/components/property/property-statistics-cpn";
@import "assets/sass/components/property/property-share-cpn";
@import "assets/sass/components/property/property-sheet-cpn";
@import "assets/sass/components/property/property-tools-cpn";
@import "assets/sass/components/property/property-openhouse-cpn";
@import "assets/sass/components/property/properties-type-choice-cpn";

@import "assets/sass/components/propertygroup/propertygroup-card-cpn";

@import "assets/sass/components/search/search-buy-cpn";
@import "assets/sass/components/search/search-filters-cpn";
@import "assets/sass/components/search/search-full-cpn";
@import "assets/sass/components/search/search-map-pane-cpn";
@import "assets/sass/components/search/search-properties-pane-cpn";
@import "assets/sass/components/search/search-sell-cpn";
@import "assets/sass/components/search/search-simple-cpn";
@import "assets/sass/components/search/search-sortable-cpn";
@import "assets/sass/components/laraberg-modifs";

@import "assets/sass/components/specialists/specialist-card-cpn";

@import "assets/sass/components/slider/slider-content-cpn";
@import "assets/sass/components/slider/slider-default-cpn";
@import "assets/sass/components/slider/reasons-slider";

@import "assets/sass/components/team/award-card-cpn";
@import "assets/sass/components/team/awards-slider-cpn";
@import "assets/sass/components/team/team-card-1-cpn";
@import "assets/sass/components/team/team-card-2-cpn";
@import "assets/sass/components/team/team-card-3-cpn";
@import "assets/sass/components/team/team-card-4-cpn";
@import "assets/sass/components/team/team-hero-1-cpn";
@import "assets/sass/components/team/team-hero-2-cpn";
@import "assets/sass/components/team/team-hero-3-cpn";
@import "assets/sass/components/team/team-hero-4-cpn";
@import "assets/sass/components/team/team-hero-5-cpn";
@import "assets/sass/components/team/team-interstice-1-cpn";
@import "assets/sass/components/team/team-interstice-2-cpn";
@import "assets/sass/components/team/team-interstice-3-cpn";

@import "assets/sass/components/testimonials/testimonial-card-cpn";
@import "assets/sass/components/testimonials/testimonials-slider-cpn";

@import "assets/sass/components/neighborhood/neighborhood-map-cpn";
@import "assets/sass/components/neighborhood/neighborhood-pane-cpn";
@import "assets/sass/components/neighborhood/neighborhood-hero-cpn";
@import "assets/sass/components/neighborhood/neighborhood-highlights-cpn";
@import "assets/sass/components/neighborhood/neighborhood-photos-cpn";
@import "assets/sass/components/neighborhood/neighborhood-demographics-cpn";
@import "assets/sass/components/neighborhood/neighborhood-avgcost-cpn";
@import "assets/sass/components/neighborhood/neighborhood-grid-cpn";

@import "assets/sass/components/video-viewer-cpn";

@import "assets/sass/components/googlemybusiness/google-my-business-cpn";

@import "assets/sass/components/facebook-posts/facebook-posts-cpn";

/* Here the list of css for page specific styles */
/* Pages */
@import "assets/sass/page/blog-details-page";
@import "assets/sass/page/buy-page";
@import "assets/sass/page/career-contact-page";
@import "assets/sass/page/favorites-page";
@import "assets/sass/page/homestaging-page";
@import "assets/sass/page/open-house-page";
@import "assets/sass/page/propertygroups-page";
@import "assets/sass/page/sell-page";
@import "assets/sass/page/specialists-page";
@import "assets/sass/page/testimonials-page";
@import "assets/sass/page/404-page";
@import "assets/sass/page/privacy-policy-cpn";




/* Print */
@import "assets/sass/base/print";

