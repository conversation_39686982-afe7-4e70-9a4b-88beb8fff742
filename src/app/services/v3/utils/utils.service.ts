import { Injectable } from '@angular/core';
import { throwError } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from '@/../environments/environment';
import { map, catchError } from 'rxjs/operators';

@Injectable({ providedIn: 'root' })

export class UtilsService {
  postOptions = { headers: new HttpHeaders({ 'Content-Type': 'application/json' }) };

  constructor (
    private http: HttpClient,
    private translate: TranslateService
  ) { }

  private getAPIUrl () {
    console.log('Current language:', this.translate.currentLang);
    // Utiliser la langue courante ou la langue par défaut si pas encore initialisée
    const currentLang = this.translate.currentLang || this.translate.getDefaultLang() || 'fr';
    return environment.apiUrl + currentLang + '/api/';
  }

  public apiGet (endpoint, successHandler = null, errorHandler = null) {
    const url = this.getAPIUrl();
    const request: any = this.http.get(url + endpoint) as any;
    return request.pipe(
      map(successHandler || (response => response)),
      catchError(errorHandler || this.apiHError)
    );
  }

  public apiPost (endpoint, body, successHandler = null, errorHandler = null) {
    const url = this.getAPIUrl();
    const request: any = this.http.post(url + endpoint, body, this.postOptions) as any;
    return request.pipe(
      map(successHandler || (response => response)),
      catchError(errorHandler || this.apiHError)
    );
  }

  private apiHError (error: any) {
    // console.log('ERROR');
    const errMsg = error.message || error.status ? error.status + ' - ' + error.statusText : 'Server error';
    return throwError(errMsg);
  }

  // Slugify a string
  public slugify (str) {
    // Trim and make lowercase
    str = str.replace(/^\s+|\s+$/g, '').toLowerCase();

    // Remove accents, swap ñ for n, etc
    const from = 'ÁÄÂÀÃÅČÇĆĎÉĚËÈÊẼĔȆÍÌÎÏŇÑÓÖÒÔÕØŘŔŠŤÚŮÜÙÛÝŸŽáäâàãåčçćďéěëèêẽĕȇíìîïňñóöòôõøðřŕšťúůüùûýÿžþÞĐđßÆa·/_,:;';
    const to = 'AAAAAACCCDEEEEEEEEIIIINNOOOOOORRSTUUUUUYYZaaaaaacccdeeeeeeeeiiiinnooooooorrstuuuuuyyzbBDdBAa______';
    for (let i = 0, l = from.length; i < l; i++) {
      str = str.replace(new RegExp(from.charAt(i), 'g'), to.charAt(i));
    }

    // Remove invalid chars
    str = str.replace(/[^a-z0-9 -]/g, '-').replace(/\s+/g, '-').replace(/-+/g, '-');

    return str;
  }

  // Generate a ld+json script tag inside the page head which contains
  //  structured data about the current page (property)
  public generateStructuredData ({
    title: name = '',
    description = '',
    image = '',
    price = ''
  }) {
    const data = {
      '@context': 'http://schema.org/',
      '@type': 'Product',
      name,
      description,
      image,
      url: window.location.href,
      offers: {
        '@type': 'Offer',
        price,
        priceCurrency: 'CAD',
        availability: 'http://schema.org/InStock'
      }
    };

    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.text = JSON.stringify(data);
    document.head.appendChild(script);
  }
}
