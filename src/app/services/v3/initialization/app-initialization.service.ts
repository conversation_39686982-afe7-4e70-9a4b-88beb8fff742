import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Observable, BehaviorSubject, timer } from 'rxjs';
import { take, filter } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class AppInitializationService {
  private isInitialized = new BehaviorSubject<boolean>(false);
  private initializationStarted = false;

  constructor(private translate: TranslateService) {}

  /**
   * Initialize the application (language, etc.)
   */
  initialize(): Observable<boolean> {
    if (this.isInitialized.value) {
      return this.isInitialized.asObservable();
    }

    if (!this.initializationStarted) {
      this.initializationStarted = true;
      this.performInitialization();
    }

    return this.isInitialized.pipe(
      filter(initialized => initialized),
      take(1)
    );
  }

  /**
   * Check if the app is initialized
   */
  isAppInitialized(): Observable<boolean> {
    return this.isInitialized.asObservable();
  }

  /**
   * Get the current initialization status
   */
  getInitializationStatus(): boolean {
    return this.isInitialized.value;
  }

  private performInitialization(): void {
    // Detect language from URL or use default
    const urlLang = window.location.pathname.split('/')[1];
    const userLang = ['en', 'fr'].includes(urlLang) ? urlLang : 'fr';
    
    // Set default language
    this.translate.setDefaultLang('fr');
    
    // Use detected language
    this.translate.use(userLang).subscribe({
      next: () => {
        console.log('Language initialized:', userLang);
        this.markAsInitialized();
      },
      error: (error) => {
        console.warn('Error initializing language:', error);
        // Fallback to default language
        this.translate.use('fr').subscribe({
          next: () => {
            console.log('Fallback language initialized: fr');
            this.markAsInitialized();
          },
          error: () => {
            // Even if translation fails, mark as initialized to prevent blocking
            console.warn('Translation service failed, continuing with initialization');
            this.markAsInitialized();
          }
        });
      }
    });

    // Fallback timeout - mark as initialized after 3 seconds regardless
    timer(3000).subscribe(() => {
      if (!this.isInitialized.value) {
        console.warn('Initialization timeout reached, forcing initialization');
        this.markAsInitialized();
      }
    });
  }

  private markAsInitialized(): void {
    // Petit délai pour s'assurer que tous les services sont prêts
    setTimeout(() => {
      this.isInitialized.next(true);
      console.log('App initialization completed, current language:', this.translate.currentLang);
    }, 100);
  }

  /**
   * Reset initialization (useful for testing or language changes)
   */
  reset(): void {
    this.isInitialized.next(false);
    this.initializationStarted = false;
  }
}
