import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';

import { environment } from '@/../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class FacebookPostsService {
  private accessToken = environment.facebookToken;
  private pageId = environment.facebookPageId

  constructor(private http: HttpClient) { }

  getPosts() {
    return this.http.get(`https://graph.facebook.com/v20.0/${this.pageId}/posts?access_token=${this.accessToken}`);
  }

  getPostImage(postId) {
    return this.http.get(`https://graph.facebook.com/v20.0/${postId}/attachments?access_token=${this.accessToken}`);
  }
}
