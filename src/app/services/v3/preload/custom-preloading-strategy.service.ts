import { Injectable } from '@angular/core';
import { PreloadingStrategy, Route } from '@angular/router';
import { Observable, of, timer } from 'rxjs';
import { mergeMap } from 'rxjs/operators';
import { PreloadConfig, isHighPriorityRoute, isMediumPriorityRoute } from '../../../config/preload.config';

@Injectable({
  providedIn: 'root'
})
export class CustomPreloadingStrategy implements PreloadingStrategy {

  preload(route: Route, load: () => Observable<any>): Observable<any> {
    // Check if route has data indicating it should be preloaded
    if (route.data && route.data['preload']) {
      return load();
    }

    // Check if this is a high priority route
    if (isHighPriorityRoute(route.path)) {
      console.log('Preloading high priority route:', route.path);
      return load();
    }

    // Check if this is a medium priority route
    if (isMediumPriorityRoute(route.path)) {
      console.log('Preloading medium priority route with delay:', route.path);
      // Delay preloading based on configuration
      return timer(PreloadConfig.timing.mediumPriorityDelay).pipe(
        mergeMap(() => load())
      );
    }

    // For all other routes, don't preload
    return of(null);
  }


}
