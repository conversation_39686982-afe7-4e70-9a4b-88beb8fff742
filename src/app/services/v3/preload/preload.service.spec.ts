import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { of } from 'rxjs';

import { PreloadService } from './preload.service';
import { InscriptionsService } from '../inscriptions/inscriptions.service';
import { HomePageService } from '../home-page-block/home-page-block.service';
import { NeighborhoodsService } from '../neighborhoods/neighborhoods.service';
import { BlogService } from '../blog/blog.service';
import { TestimonialsService } from '../testimonials/testimonials.service';
import { ImagePreloadService } from './image-preload.service';

describe('PreloadService', () => {
  let service: PreloadService;
  let httpMock: HttpTestingController;
  let inscriptionsService: jasmine.SpyObj<InscriptionsService>;
  let homePageService: jasmine.SpyObj<HomePageService>;
  let neighborhoodsService: jasmine.SpyObj<NeighborhoodsService>;
  let blogService: jasmine.SpyObj<BlogService>;
  let testimonialsService: jasmine.SpyObj<TestimonialsService>;
  let imagePreloadService: jasmine.SpyObj<ImagePreloadService>;

  beforeEach(() => {
    const inscriptionsSpy = jasmine.createSpyObj('InscriptionsService', ['getInscriptions']);
    const homePageSpy = jasmine.createSpyObj('HomePageService', ['getHomePage']);
    const neighborhoodsSpy = jasmine.createSpyObj('NeighborhoodsService', ['getNeighborhoods']);
    const blogSpy = jasmine.createSpyObj('BlogService', ['getPosts']);
    const testimonialsSpy = jasmine.createSpyObj('TestimonialsService', ['getTestimonials']);
    const imagePreloadSpy = jasmine.createSpyObj('ImagePreloadService', ['preloadPropertyImages']);

    TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule,
        TranslateModule.forRoot()
      ],
      providers: [
        PreloadService,
        { provide: InscriptionsService, useValue: inscriptionsSpy },
        { provide: HomePageService, useValue: homePageSpy },
        { provide: NeighborhoodsService, useValue: neighborhoodsSpy },
        { provide: BlogService, useValue: blogSpy },
        { provide: TestimonialsService, useValue: testimonialsSpy },
        { provide: ImagePreloadService, useValue: imagePreloadSpy }
      ]
    });

    service = TestBed.inject(PreloadService);
    httpMock = TestBed.inject(HttpTestingController);
    inscriptionsService = TestBed.inject(InscriptionsService) as jasmine.SpyObj<InscriptionsService>;
    homePageService = TestBed.inject(HomePageService) as jasmine.SpyObj<HomePageService>;
    neighborhoodsService = TestBed.inject(NeighborhoodsService) as jasmine.SpyObj<NeighborhoodsService>;
    blogService = TestBed.inject(BlogService) as jasmine.SpyObj<BlogService>;
    testimonialsService = TestBed.inject(TestimonialsService) as jasmine.SpyObj<TestimonialsService>;
    imagePreloadService = TestBed.inject(ImagePreloadService) as jasmine.SpyObj<ImagePreloadService>;
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should preload critical data successfully', (done) => {
    // Mock service responses
    homePageService.getHomePage.and.returnValue(of({ data: { title: 'Home' } }));
    inscriptionsService.getInscriptions.and.returnValue(of({ data: [{ id: 1, title: 'Property 1' }] }));
    neighborhoodsService.getNeighborhoods.and.returnValue(of({ data: [{ id: 1, name: 'Downtown' }] }));
    blogService.getPosts.and.returnValue(of({ data: [{ id: 1, title: 'Blog Post 1' }] }));
    testimonialsService.getTestimonials.and.returnValue(of({ data: [{ id: 1, content: 'Great service!' }] }));

    service.preloadCriticalData().subscribe(data => {
      expect(data.homePageContent).toBeDefined();
      expect(data.featuredProperties).toBeDefined();
      expect(data.newProperties).toBeDefined();
      expect(data.openHouseProperties).toBeDefined();
      expect(data.neighborhoods).toBeDefined();
      expect(data.blogPosts).toBeDefined();
      expect(data.testimonials).toBeDefined();
      done();
    });
  });

  it('should handle errors gracefully', (done) => {
    // Mock service errors
    homePageService.getHomePage.and.returnValue(of(null));
    inscriptionsService.getInscriptions.and.returnValue(of({ data: [] }));
    neighborhoodsService.getNeighborhoods.and.returnValue(of({ data: [] }));
    blogService.getPosts.and.returnValue(of({ data: [] }));
    testimonialsService.getTestimonials.and.returnValue(of({ data: [] }));

    service.preloadCriticalData().subscribe(data => {
      expect(data).toBeDefined();
      done();
    });
  });

  it('should cache data with shareReplay', () => {
    homePageService.getHomePage.and.returnValue(of({ data: { title: 'Home' } }));

    // First call
    service.getHomePageContent().subscribe();
    // Second call should use cached data
    service.getHomePageContent().subscribe();

    // Service should only be called once due to shareReplay
    expect(homePageService.getHomePage).toHaveBeenCalledTimes(1);
  });

  it('should preload property images', () => {
    const properties = [
      { ext_coverphoto: 'image1.jpg', ext_coverfull: 'image1_full.jpg' },
      { ext_coverphoto: 'image2.jpg', ext_coverfull: 'image2_full.jpg' }
    ];

    service.preloadPropertyImages(properties);

    expect(imagePreloadService.preloadPropertyImages).toHaveBeenCalledWith(properties);
  });

  it('should track preloading status', (done) => {
    service.isPreloadingData().subscribe(isLoading => {
      expect(typeof isLoading).toBe('boolean');
      done();
    });
  });

  it('should track preload completion', (done) => {
    service.isPreloadComplete().subscribe(isComplete => {
      expect(typeof isComplete).toBe('boolean');
      done();
    });
  });

  it('should clear cache properly', () => {
    service.clearCache();
    
    service.isPreloadComplete().subscribe(isComplete => {
      expect(isComplete).toBe(false);
    });
  });
});
