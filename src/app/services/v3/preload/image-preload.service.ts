import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface ImagePreloadStatus {
  url: string;
  loaded: boolean;
  error?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class ImagePreloadService {
  private preloadedImages = new Map<string, ImagePreloadStatus>();
  private preloadQueue: string[] = [];
  private isProcessing = false;
  private maxConcurrent = 3; // Maximum concurrent image loads
  private currentLoading = 0;

  private preloadStatus = new BehaviorSubject<Map<string, ImagePreloadStatus>>(new Map());

  constructor() {
    this.preloadCriticalImages();
  }

  /**
   * Preload critical images that are needed immediately
   */
  private preloadCriticalImages(): void {
    const criticalImages = [
      'assets/images/common/logo-equipe_bourdon.svg',
      'assets/images/common/logo-equipe_bourdon-blanc.svg',
      'assets/images/placeholder/propriete-nb.jpg',
      'assets/images/placeholder/propriete-nb-wide.jpg',
      'assets/images/SVG/icons/bed.svg',
      'assets/images/SVG/icons/sink.svg',
      'assets/images/SVG/UI/arrow-left.svg',
      'assets/images/SVG/UI/arrow-right.svg'
    ];

    this.preloadImages(criticalImages, true);
  }

  /**
   * Preload an array of image URLs
   */
  preloadImages(urls: string[], highPriority: boolean = false): void {
    const newUrls = urls.filter(url => !this.preloadedImages.has(url));
    
    if (highPriority) {
      // Add to front of queue for high priority images
      this.preloadQueue.unshift(...newUrls);
    } else {
      // Add to end of queue for normal priority images
      this.preloadQueue.push(...newUrls);
    }

    // Initialize status for new URLs
    newUrls.forEach(url => {
      this.preloadedImages.set(url, { url, loaded: false });
    });

    this.processQueue();
  }

  /**
   * Preload a single image
   */
  preloadImage(url: string, highPriority: boolean = false): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.preloadedImages.has(url)) {
        const status = this.preloadedImages.get(url);
        if (status.loaded) {
          resolve();
          return;
        } else if (status.error) {
          reject(new Error(`Failed to load image: ${url}`));
          return;
        }
      }

      const img = new Image();
      
      img.onload = () => {
        this.preloadedImages.set(url, { url, loaded: true });
        this.updateStatus();
        resolve();
      };

      img.onerror = () => {
        this.preloadedImages.set(url, { url, loaded: false, error: true });
        this.updateStatus();
        reject(new Error(`Failed to load image: ${url}`));
      };

      // Set high priority hint for critical images
      if (highPriority) {
        img.setAttribute('fetchpriority', 'high');
      }

      img.src = url;
    });
  }

  /**
   * Process the preload queue
   */
  private processQueue(): void {
    if (this.isProcessing || this.preloadQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    while (this.currentLoading < this.maxConcurrent && this.preloadQueue.length > 0) {
      const url = this.preloadQueue.shift();
      if (url && !this.preloadedImages.get(url)?.loaded) {
        this.currentLoading++;
        this.loadImage(url);
      }
    }

    if (this.currentLoading === 0) {
      this.isProcessing = false;
    }
  }

  /**
   * Load a single image from the queue
   */
  private loadImage(url: string): void {
    const img = new Image();
    
    img.onload = () => {
      this.preloadedImages.set(url, { url, loaded: true });
      this.currentLoading--;
      this.updateStatus();
      this.processQueue();
    };

    img.onerror = () => {
      this.preloadedImages.set(url, { url, loaded: false, error: true });
      this.currentLoading--;
      this.updateStatus();
      this.processQueue();
    };

    img.src = url;
  }

  /**
   * Update the status observable
   */
  private updateStatus(): void {
    this.preloadStatus.next(new Map(this.preloadedImages));
  }

  /**
   * Check if an image is preloaded
   */
  isImagePreloaded(url: string): boolean {
    const status = this.preloadedImages.get(url);
    return status ? status.loaded : false;
  }

  /**
   * Get preload status for all images
   */
  getPreloadStatus(): Observable<Map<string, ImagePreloadStatus>> {
    return this.preloadStatus.asObservable();
  }

  /**
   * Get preload status for a specific image
   */
  getImageStatus(url: string): ImagePreloadStatus | null {
    return this.preloadedImages.get(url) || null;
  }

  /**
   * Preload property images based on property data
   */
  preloadPropertyImages(properties: any[], limit: number = 6): void {
    if (!properties || properties.length === 0) return;

    const imageUrls: string[] = [];
    
    properties.slice(0, limit).forEach(property => {
      if (property.ext_coverphoto) {
        imageUrls.push(property.ext_coverphoto);
      }
      if (property.ext_coverfull) {
        imageUrls.push(property.ext_coverfull);
      }
    });

    if (imageUrls.length > 0) {
      this.preloadImages(imageUrls);
    }
  }

  /**
   * Clear all preloaded images (useful for memory management)
   */
  clearCache(): void {
    this.preloadedImages.clear();
    this.preloadQueue.length = 0;
    this.currentLoading = 0;
    this.isProcessing = false;
    this.updateStatus();
  }
}
