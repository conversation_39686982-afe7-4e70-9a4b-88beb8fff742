import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { InscriptionsService } from '../inscriptions/inscriptions.service';
import { HomePageService } from '../home-page-block/home-page-block.service';
import { NeighborhoodsService } from '../neighborhoods/neighborhoods.service';
import { BlogService } from '../blog/blog.service';
import { TestimonialsService } from '../testimonials/testimonials.service';

@Injectable({
  providedIn: 'root'
})
export class PreloadDebugService {

  constructor(
    private translate: TranslateService,
    private inscriptionsService: InscriptionsService,
    private homePageService: HomePageService,
    private neighborhoodsService: NeighborhoodsService,
    private blogService: BlogService,
    private testimonialsService: TestimonialsService
  ) {}

  /**
   * Test all services to ensure they're properly injected and functional
   */
  testAllServices(): void {
    console.log('=== Service Injection Test ===');
    
    // Test TranslateService
    try {
      console.log('✓ TranslateService:', {
        currentLang: this.translate.currentLang,
        defaultLang: this.translate.getDefaultLang(),
        hasInstance: !!this.translate
      });
    } catch (error) {
      console.error('✗ TranslateService error:', error);
    }

    // Test InscriptionsService
    try {
      const testCall = this.inscriptionsService.getInscriptions(1, { featured: 1 });
      console.log('✓ InscriptionsService:', {
        hasInstance: !!this.inscriptionsService,
        methodExists: typeof this.inscriptionsService.getInscriptions === 'function',
        testCallResult: !!testCall
      });
    } catch (error) {
      console.error('✗ InscriptionsService error:', error);
    }

    // Test HomePageService
    try {
      const testCall = this.homePageService.getHomePage();
      console.log('✓ HomePageService:', {
        hasInstance: !!this.homePageService,
        methodExists: typeof this.homePageService.getHomePage === 'function',
        testCallResult: !!testCall
      });
    } catch (error) {
      console.error('✗ HomePageService error:', error);
    }

    // Test NeighborhoodsService
    try {
      const testCall = this.neighborhoodsService.getNeighborhoods(1);
      console.log('✓ NeighborhoodsService:', {
        hasInstance: !!this.neighborhoodsService,
        methodExists: typeof this.neighborhoodsService.getNeighborhoods === 'function',
        testCallResult: !!testCall
      });
    } catch (error) {
      console.error('✗ NeighborhoodsService error:', error);
    }

    // Test BlogService
    try {
      const testCall = this.blogService.getPosts(1);
      console.log('✓ BlogService:', {
        hasInstance: !!this.blogService,
        methodExists: typeof this.blogService.getPosts === 'function',
        testCallResult: !!testCall
      });
    } catch (error) {
      console.error('✗ BlogService error:', error);
    }

    // Test TestimonialsService
    try {
      const testCall = this.testimonialsService.getTestimonials(1);
      console.log('✓ TestimonialsService:', {
        hasInstance: !!this.testimonialsService,
        methodExists: typeof this.testimonialsService.getTestimonials === 'function',
        testCallResult: !!testCall
      });
    } catch (error) {
      console.error('✗ TestimonialsService error:', error);
    }

    console.log('=== End Service Test ===');
  }

  /**
   * Test a single service call to see what happens
   */
  testSingleServiceCall(): void {
    console.log('=== Testing Single Service Call ===');
    
    try {
      console.log('Testing HomePageService.getHomePage()...');
      const result = this.homePageService.getHomePage();
      
      if (result) {
        console.log('Service call returned:', typeof result);
        console.log('Is Observable?', result.subscribe !== undefined);
        
        result.subscribe({
          next: (data) => {
            console.log('✓ Service call successful:', data);
          },
          error: (error) => {
            console.error('✗ Service call error:', error);
          }
        });
      } else {
        console.error('✗ Service call returned undefined/null');
      }
    } catch (error) {
      console.error('✗ Exception during service call:', error);
    }
  }
}
