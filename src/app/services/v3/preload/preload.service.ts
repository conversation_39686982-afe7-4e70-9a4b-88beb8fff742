import { Injectable } from '@angular/core';
import { Observable, forkJoin, of, BehaviorSubject } from 'rxjs';
import { catchError, tap, shareReplay } from 'rxjs/operators';
import { TranslateService } from '@ngx-translate/core';

// Import all services that provide critical data
import { InscriptionsService } from '@/services/v3/inscriptions/inscriptions.service';
import { HomePageService } from '@/services/v3/home-page-block/home-page-block.service';
import { NeighborhoodsService } from '@/services/v3/neighborhoods/neighborhoods.service';
import { BlogService } from '@/services/v3/blog/blog.service';
import { TestimonialsService } from '@/services/v3/testimonials/testimonials.service';
import { ImagePreloadService } from './image-preload.service';

export interface PreloadData {
  homePageContent?: any;
  featuredProperties?: any;
  newProperties?: any;
  openHouseProperties?: any;
  neighborhoods?: any;
  blogPosts?: any;
  testimonials?: any;
}

@Injectable({
  providedIn: 'root'
})
export class PreloadService {
  private preloadedData = new BehaviorSubject<PreloadData>({});
  private isPreloading = new BehaviorSubject<boolean>(false);
  private preloadComplete = new BehaviorSubject<boolean>(false);

  // Cache for preloaded data with shareReplay
  private homePageCache$: Observable<any>;
  private featuredPropertiesCache$: Observable<any>;
  private newPropertiesCache$: Observable<any>;
  private openHousePropertiesCache$: Observable<any>;
  private neighborhoodsCache$: Observable<any>;
  private blogPostsCache$: Observable<any>;
  private testimonialsCache$: Observable<any>;

  constructor(
    private inscriptionsService: InscriptionsService,
    private homePageService: HomePageService,
    private neighborhoodsService: NeighborhoodsService,
    private blogService: BlogService,
    private testimonialsService: TestimonialsService,
    private translate: TranslateService,
    private imagePreloadService: ImagePreloadService
  ) {
    this.initializeCache();
  }

  private initializeCache(): void {
    // Initialize cached observables with shareReplay to prevent multiple API calls
    this.homePageCache$ = this.homePageService.getHomePage().pipe(
      shareReplay(1),
      catchError(error => {
        console.warn('Failed to preload home page content:', error);
        return of(null);
      })
    );

    this.featuredPropertiesCache$ = this.inscriptionsService.getInscriptions(-1, { 
      featured: 1, 
      sold: 0, 
      sort: 'rand' 
    }).pipe(
      shareReplay(1),
      catchError(error => {
        console.warn('Failed to preload featured properties:', error);
        return of({ data: [] });
      })
    );

    this.newPropertiesCache$ = this.inscriptionsService.getInscriptions(-1, { 
      sold: 0, 
      sort: "newest" 
    }).pipe(
      shareReplay(1),
      catchError(error => {
        console.warn('Failed to preload new properties:', error);
        return of({ data: [] });
      })
    );

    this.openHousePropertiesCache$ = this.inscriptionsService.getInscriptions(-1, { 
      featured: 0, 
      flags: ["openhouse"] 
    }).pipe(
      shareReplay(1),
      catchError(error => {
        console.warn('Failed to preload open house properties:', error);
        return of({ data: [] });
      })
    );

    this.neighborhoodsCache$ = this.neighborhoodsService.getNeighborhoods(10).pipe(
      shareReplay(1),
      catchError(error => {
        console.warn('Failed to preload neighborhoods:', error);
        return of({ data: [] });
      })
    );

    this.blogPostsCache$ = this.blogService.getPosts(5).pipe(
      shareReplay(1),
      catchError(error => {
        console.warn('Failed to preload blog posts:', error);
        return of({ data: [] });
      })
    );

    this.testimonialsCache$ = this.testimonialsService.getTestimonials(5, 1).pipe(
      shareReplay(1),
      catchError(error => {
        console.warn('Failed to preload testimonials:', error);
        return of({ data: [] });
      })
    );
  }

  /**
   * Preload all critical data for the application
   */
  preloadCriticalData(): Observable<PreloadData> {
    if (this.preloadComplete.value) {
      return this.preloadedData.asObservable();
    }

    this.isPreloading.next(true);

    return forkJoin({
      homePageContent: this.homePageCache$,
      featuredProperties: this.featuredPropertiesCache$,
      newProperties: this.newPropertiesCache$,
      openHouseProperties: this.openHousePropertiesCache$,
      neighborhoods: this.neighborhoodsCache$,
      blogPosts: this.blogPostsCache$,
      testimonials: this.testimonialsCache$
    }).pipe(
      tap(data => {
        this.preloadedData.next(data);
        this.preloadComplete.next(true);
        this.isPreloading.next(false);
        console.log('Critical data preloaded successfully');
      }),
      catchError(error => {
        console.error('Error preloading critical data:', error);
        this.isPreloading.next(false);
        return of({});
      })
    );
  }

  /**
   * Get cached home page content
   */
  getHomePageContent(): Observable<any> {
    return this.homePageCache$;
  }

  /**
   * Get cached featured properties
   */
  getFeaturedProperties(): Observable<any> {
    return this.featuredPropertiesCache$;
  }

  /**
   * Get cached new properties
   */
  getNewProperties(): Observable<any> {
    return this.newPropertiesCache$;
  }

  /**
   * Get cached open house properties
   */
  getOpenHouseProperties(): Observable<any> {
    return this.openHousePropertiesCache$;
  }

  /**
   * Get cached neighborhoods
   */
  getNeighborhoods(): Observable<any> {
    return this.neighborhoodsCache$;
  }

  /**
   * Get cached blog posts
   */
  getBlogPosts(): Observable<any> {
    return this.blogPostsCache$;
  }

  /**
   * Get cached testimonials
   */
  getTestimonials(): Observable<any> {
    return this.testimonialsCache$;
  }

  /**
   * Check if preloading is in progress
   */
  isPreloadingData(): Observable<boolean> {
    return this.isPreloading.asObservable();
  }

  /**
   * Check if preload is complete
   */
  isPreloadComplete(): Observable<boolean> {
    return this.preloadComplete.asObservable();
  }

  /**
   * Get all preloaded data
   */
  getPreloadedData(): Observable<PreloadData> {
    return this.preloadedData.asObservable();
  }

  /**
   * Preload images for a list of properties
   */
  preloadPropertyImages(properties: any[]): void {
    this.imagePreloadService.preloadPropertyImages(properties);
  }

  /**
   * Clear all cached data (useful for language changes)
   */
  clearCache(): void {
    this.preloadComplete.next(false);
    this.preloadedData.next({});
    this.initializeCache();
  }
}
