import { Injectable } from '@angular/core';
import { Observable, forkJoin, of, BehaviorSubject } from 'rxjs';
import { catchError, tap, shareReplay } from 'rxjs/operators';
import { TranslateService } from '@ngx-translate/core';

// Import all services that provide critical data
import { InscriptionsService } from '@/services/v3/inscriptions/inscriptions.service';
import { HomePageService } from '@/services/v3/home-page-block/home-page-block.service';
import { NeighborhoodsService } from '@/services/v3/neighborhoods/neighborhoods.service';
import { BlogService } from '@/services/v3/blog/blog.service';
import { TestimonialsService } from '@/services/v3/testimonials/testimonials.service';
import { ImagePreloadService } from './image-preload.service';
import { BlocksService } from '@/services/v3/contentblocks/contentblocks.service';

export interface PreloadData {
  homePageContent?: any;
  featuredProperties?: any;
  newProperties?: any;
  openHouseProperties?: any;
  neighborhoods?: any;
  blogPosts?: any;
  testimonials?: any;
  teamBlock?: any;
}

@Injectable({
  providedIn: 'root'
})
export class PreloadService {
  private preloadedData = new BehaviorSubject<PreloadData>({});
  private isPreloading = new BehaviorSubject<boolean>(false);
  private preloadComplete = new BehaviorSubject<boolean>(false);

  // Cache for preloaded data with shareReplay
  private homePageCache$: Observable<any>;
  private featuredPropertiesCache$: Observable<any>;
  private newPropertiesCache$: Observable<any>;
  private openHousePropertiesCache$: Observable<any>;
  private neighborhoodsCache$: Observable<any>;
  private blogPostsCache$: Observable<any>;
  private testimonialsCache$: Observable<any>;
  private teamBlockCache$: Observable<any>;

  constructor(
    private inscriptionsService: InscriptionsService,
    private homePageService: HomePageService,
    private neighborhoodsService: NeighborhoodsService,
    private blogService: BlogService,
    private testimonialsService: TestimonialsService,
    private translate: TranslateService,
    private imagePreloadService: ImagePreloadService,
    private blocksService: BlocksService
  ) {
    // Attendre que la langue soit initialisée avant de démarrer le cache
    this.waitForLanguageInitialization();
  }

  /**
   * Attendre que la langue soit initialisée avant de démarrer le cache
   */
  private waitForLanguageInitialization(): void {
    // Vérifier si la langue est déjà initialisée
    if (this.translate.currentLang) {
      this.initializeCache();
    } else {
      // Attendre l'événement de changement de langue
      this.translate.onLangChange.subscribe(() => {
        if (!this.preloadComplete.value) {
          this.initializeCache();
        }
      });

      // Fallback: initialiser après un délai si la langue n'est toujours pas définie
      setTimeout(() => {
        if (!this.translate.currentLang && !this.preloadComplete.value) {
          // Utiliser la langue par défaut
          this.translate.use(this.translate.getDefaultLang() || 'fr');
          this.initializeCache();
        }
      }, 1000);
    }
  }

  private initializeCache(): void {
    console.log('Initializing cache with language:', this.translate.currentLang);

    // Initialize cached observables with shareReplay to prevent multiple API calls
    // Wrap each service call to ensure it returns a valid Observable
    this.homePageCache$ = this.safeServiceCall(
      () => this.homePageService.getHomePage(),
      'home page content',
      null
    );

    this.featuredPropertiesCache$ = this.safeServiceCall(
      () => this.inscriptionsService.getInscriptions(-1, {
        featured: 1,
        sold: 0,
        sort: 'rand'
      }),
      'featured properties',
      { data: [] }
    );

    this.newPropertiesCache$ = this.safeServiceCall(
      () => this.inscriptionsService.getInscriptions(-1, {
        sold: 0,
        sort: "newest"
      }),
      'new properties',
      { data: [] }
    );

    this.openHousePropertiesCache$ = this.safeServiceCall(
      () => this.inscriptionsService.getInscriptions(-1, {
        featured: 0,
        flags: ["openhouse"]
      }),
      'open house properties',
      { data: [] }
    );

    this.neighborhoodsCache$ = this.safeServiceCall(
      () => this.neighborhoodsService.getNeighborhoods(10),
      'neighborhoods',
      { data: [] }
    );

    this.blogPostsCache$ = this.safeServiceCall(
      () => this.blogService.getPosts(5),
      'blog posts',
      { data: [] }
    );

    this.testimonialsCache$ = this.safeServiceCall(
      () => this.testimonialsService.getTestimonials(5, 1),
      'testimonials',
      { data: [] }
    );

    this.teamBlockCache$ = this.safeServiceCall(
      () => this.blocksService.getBlock('bloc-equipe'),
      'team block',
      { title: '', text: '' }
    );
  }

  /**
   * Safely call a service method and ensure it returns a valid Observable
   */
  private safeServiceCall<T>(
    serviceCall: () => Observable<T> | undefined | null,
    resourceName: string,
    fallbackValue: T
  ): Observable<T> {
    try {
      const result = serviceCall();

      if (!result) {
        console.warn(`Service call for ${resourceName} returned undefined/null, using fallback`);
        return of(fallbackValue).pipe(shareReplay(1));
      }

      return result.pipe(
        shareReplay(1),
        catchError(error => {
          console.warn(`Failed to preload ${resourceName}:`, error);
          return of(fallbackValue);
        })
      );
    } catch (error) {
      console.warn(`Error calling service for ${resourceName}:`, error);
      return of(fallbackValue).pipe(shareReplay(1));
    }
  }

  /**
   * Preload all critical data for the application
   */
  preloadCriticalData(): Observable<PreloadData> {
    if (this.preloadComplete.value) {
      return this.preloadedData.asObservable();
    }

    // Vérifier que la langue est initialisée
    if (!this.translate.currentLang) {
      console.warn('Language not initialized, waiting...');
      // Retourner un observable vide et réessayer plus tard
      return new Observable(observer => {
        setTimeout(() => {
          this.preloadCriticalData().subscribe(observer);
        }, 500);
      });
    }

    this.isPreloading.next(true);

    // Ensure all cache observables are initialized
    if (!this.homePageCache$ || !this.featuredPropertiesCache$ || !this.newPropertiesCache$ ||
        !this.openHousePropertiesCache$ || !this.neighborhoodsCache$ || !this.blogPostsCache$ ||
        !this.testimonialsCache$ || !this.teamBlockCache$) {
      console.warn('Some cache observables are not initialized, reinitializing...');
      this.initializeCache();
    }

    const forkJoinData = {
      homePageContent: this.homePageCache$ || of(null),
      featuredProperties: this.featuredPropertiesCache$ || of({ data: [] }),
      newProperties: this.newPropertiesCache$ || of({ data: [] }),
      openHouseProperties: this.openHousePropertiesCache$ || of({ data: [] }),
      neighborhoods: this.neighborhoodsCache$ || of({ data: [] }),
      blogPosts: this.blogPostsCache$ || of({ data: [] }),
      testimonials: this.testimonialsCache$ || of({ data: [] }),
      teamBlock: this.teamBlockCache$ || of({ title: '', text: '' })
    };

    return forkJoin(forkJoinData).pipe(
      tap(data => {
        this.preloadedData.next(data);
        this.preloadComplete.next(true);
        this.isPreloading.next(false);
        console.log('Critical data preloaded successfully:', data);
      }),
      catchError(error => {
        console.error('Error preloading critical data:', error);
        this.isPreloading.next(false);
        // Return empty data structure instead of empty object
        const emptyData = {
          homePageContent: null,
          featuredProperties: { data: [] },
          newProperties: { data: [] },
          openHouseProperties: { data: [] },
          neighborhoods: { data: [] },
          blogPosts: { data: [] },
          testimonials: { data: [] },
          teamBlock: { title: '', text: '' }
        };
        this.preloadedData.next(emptyData);
        return of(emptyData);
      })
    );
  }

  /**
   * Get cached home page content
   */
  getHomePageContent(): Observable<any> {
    if (!this.homePageCache$) {
      console.warn('Home page cache not initialized, initializing now...');
      this.initializeCache();
    }
    return this.homePageCache$ || of(null);
  }

  /**
   * Get cached featured properties
   */
  getFeaturedProperties(): Observable<any> {
    if (!this.featuredPropertiesCache$) {
      console.warn('Featured properties cache not initialized, initializing now...');
      this.initializeCache();
    }
    return this.featuredPropertiesCache$ || of({ data: [] });
  }

  /**
   * Get cached new properties
   */
  getNewProperties(): Observable<any> {
    if (!this.newPropertiesCache$) {
      console.warn('New properties cache not initialized, initializing now...');
      this.initializeCache();
    }
    return this.newPropertiesCache$ || of({ data: [] });
  }

  /**
   * Get cached open house properties
   */
  getOpenHouseProperties(): Observable<any> {
    if (!this.openHousePropertiesCache$) {
      console.warn('Open house properties cache not initialized, initializing now...');
      this.initializeCache();
    }
    return this.openHousePropertiesCache$ || of({ data: [] });
  }

  /**
   * Get cached neighborhoods
   */
  getNeighborhoods(): Observable<any> {
    if (!this.neighborhoodsCache$) {
      console.warn('Neighborhoods cache not initialized, initializing now...');
      this.initializeCache();
    }
    return this.neighborhoodsCache$ || of({ data: [] });
  }

  /**
   * Get cached blog posts
   */
  getBlogPosts(): Observable<any> {
    if (!this.blogPostsCache$) {
      console.warn('Blog posts cache not initialized, initializing now...');
      this.initializeCache();
    }
    return this.blogPostsCache$ || of({ data: [] });
  }

  /**
   * Get cached testimonials
   */
  getTestimonials(): Observable<any> {
    if (!this.testimonialsCache$) {
      console.warn('Testimonials cache not initialized, initializing now...');
      this.initializeCache();
    }
    return this.testimonialsCache$ || of({ data: [] });
  }

  /**
   * Get cached team block (bloc-equipe)
   */
  getTeamBlock(): Observable<any> {
    if (!this.teamBlockCache$) {
      console.warn('Team block cache not initialized, initializing now...');
      this.initializeCache();
    }
    return this.teamBlockCache$ || of({ title: '', text: '' });
  }

  /**
   * Check if preloading is in progress
   */
  isPreloadingData(): Observable<boolean> {
    return this.isPreloading.asObservable();
  }

  /**
   * Check if preload is complete
   */
  isPreloadComplete(): Observable<boolean> {
    return this.preloadComplete.asObservable();
  }

  /**
   * Get all preloaded data
   */
  getPreloadedData(): Observable<PreloadData> {
    return this.preloadedData.asObservable();
  }

  /**
   * Preload images for a list of properties
   */
  preloadPropertyImages(properties: any[]): void {
    this.imagePreloadService.preloadPropertyImages(properties);
  }

  /**
   * Clear all cached data (useful for language changes)
   */
  clearCache(): void {
    this.preloadComplete.next(false);
    this.preloadedData.next({});
    this.initializeCache();
  }

  /**
   * Force initialization with a specific language
   */
  forceInitialization(language?: string): void {
    if (language) {
      this.translate.use(language);
    } else if (!this.translate.currentLang) {
      // Détecter la langue depuis l'URL ou utiliser la langue par défaut
      const urlLang = window.location.pathname.split('/')[1];
      const defaultLang = ['en', 'fr'].includes(urlLang) ? urlLang : 'fr';
      this.translate.use(defaultLang);
    }

    // Réinitialiser le cache avec la nouvelle langue
    this.clearCache();
  }

  /**
   * Diagnostic method to check service status
   */
  diagnoseServices(): void {
    console.log('=== PreloadService Diagnosis ===');
    console.log('Current language:', this.translate.currentLang);
    console.log('Default language:', this.translate.getDefaultLang());
    console.log('Preload complete:', this.preloadComplete.value);
    console.log('Is preloading:', this.isPreloading.value);
    console.log('Cache observables status:');
    console.log('- homePageCache$:', !!this.homePageCache$);
    console.log('- featuredPropertiesCache$:', !!this.featuredPropertiesCache$);
    console.log('- newPropertiesCache$:', !!this.newPropertiesCache$);
    console.log('- openHousePropertiesCache$:', !!this.openHousePropertiesCache$);
    console.log('- neighborhoodsCache$:', !!this.neighborhoodsCache$);
    console.log('- blogPostsCache$:', !!this.blogPostsCache$);
    console.log('- testimonialsCache$:', !!this.testimonialsCache$);
    console.log('- teamBlockCache$:', !!this.teamBlockCache$);
    console.log('Services status:');
    console.log('- inscriptionsService:', !!this.inscriptionsService);
    console.log('- homePageService:', !!this.homePageService);
    console.log('- neighborhoodsService:', !!this.neighborhoodsService);
    console.log('- blogService:', !!this.blogService);
    console.log('- testimonialsService:', !!this.testimonialsService);
    console.log('- blocksService:', !!this.blocksService);
    console.log('================================');
  }
}
