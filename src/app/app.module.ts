// BASE & LOCALES
import { NgModule, LOCALE_ID } from '@angular/core';
import { registerLocaleData, DatePipe, CurrencyPipe } from '@angular/common';
import localeFr from '@angular/common/locales/fr';
import localeEn from '@angular/common/locales/en';

// MODULES
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { NgPipesModule } from 'ngx-pipes';
import { NgSelectModule } from '@ng-select/ng-select';
import { ChartsModule } from 'ng2-charts';
import { NgxWebstorageModule } from 'ngx-webstorage';
import { AngularMyDatePickerModule } from 'angular-mydatepicker';
import { NgxMaskModule } from 'ngx-mask';
import { NgxPaginationModule } from 'ngx-pagination';
import { NgxPageScrollModule } from 'ngx-page-scroll';
import { FileUploadModule } from 'ng2-file-upload';
import { BrowserModule } from '@angular/platform-browser';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AppComponent } from './app.component';
import { AppRoutingModule } from './app-routing.module';
import { HttpClient, HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { GoogleMapsModule } from '@angular/google-maps';
import { SwiperModule } from 'swiper/angular';
import { RECAPTCHA_V3_SITE_KEY, RecaptchaV3Module } from 'ng-recaptcha';
import { environment } from '../environments/environment';
import { ShareButtonsConfig, ShareModule } from '@ngx-share/core';
/*
  APP COMPONENTS IMPORTS - IMPORTANT
  For the sake of everyone's mental health - please maintain proper structure:
  Imports should be in alphabetical order
  Nested child components should have one tab indentation under parent
*/

// LIBRARY COMPONENTS
import { AlertCta1Component } from './library/alert-cta-1/alert-cta-1.component';
import { AlertCta2Component } from './library/alert-cta-2/alert-cta-2.component';
import { AlertFormComponent } from './library/alert-form/alert-form.component';
import { Article1Component } from './library/article-1/article-1.component';
import { Article2Component } from './library/article-2/article-2.component';
import { AwardCardComponent } from './library/award-card/award-card.component';
import { AwardsSliderComponent } from './library/awards-slider/awards-slider.component';
import { BlogDetailsComponent } from './library/blog-details/blog-details.component';
import { BlogList1Component } from './library/blog-list-1/blog-list-1.component';
import { BlogList2Component } from './library/blog-list-2/blog-list-2.component';
import { BlogList3Component } from './library/blog-list-3/blog-list-3.component';
import { BlogList4Component } from './library/blog-list-4/blog-list-4.component';
import { CustomContentComponent } from './library/custom-content/custom-content.component';
import { BrokerContactHeader1Component } from './library/broker-contact-header-1/broker-contact-header-1.component';
import { BrokerContactHeader2Component } from './library/broker-contact-header-2/broker-contact-header-2.component';
import { BrokerContactFormComponent } from './library/broker-contact-form/broker-contact-form.component';
import { BrokerContactListComponent } from './library/broker-contact-list/broker-contact-list.component';
import { BrokerContactMapComponent } from './library/broker-contact-map/broker-contact-map.component';
import { BrokerCardComponent } from './library/broker-card/broker-card.component';
import { BuySheetComponent } from './library/buy-sheet/buy-sheet.component';
import { CareerContactComponent } from './library/career-contact/career-contact.component';
import { CenteredCtaComponent } from './library/centered-cta/centered-cta.component';
import { CtaBrokerComponent } from './library/cta-broker/cta-broker.component';
import { CtaCenteredComponent } from './library/cta-centered/cta-centered.component';
import { CtaEvaluationComponent } from './library/cta-evaluation/cta-evaluation.component';
import { CtaEvaluationSmallComponent } from './library/cta-evaluation-small/cta-evaluation-small.component';
import { CtaAlertComponent } from './library/cta-alert/cta-alert.component';
import { CtaAlertSmallComponent } from './library/cta-alert-small/cta-alert-small.component';
import {CtaSmallComponent} from "@/library/cta-small/cta-small.component";
import {CtaLeftComponent} from "@/library/cta-left/cta-left.component";
import { EvaluationFormComponent } from './library/evaluation-form/evaluation-form.component';
import { EvaluationFormSinglePageComponent } from './library/evaluation-form-single-page/evaluation-form-single-page.component';
import { FooterComponent } from './library/footer/footer.component';
import { HeaderPanelComponent } from './library/header-panel/header-panel.component';
import { HeaderComponent } from './library/header/header.component';
import { Header2Component } from './library/header-2/header-2.component';
import { Header3Component } from './library/header-3/header-3.component';
import { Header4Component } from './library/header-4/header-4.component';
import { Header5Component } from './library/header-5/header-5.component';
import { HeaderLastPropertiesComponent } from './library/header-last-properties/header-last-properties.component';
import { Panel5Component } from './library/header-5/panel/panel-5.component';
import { HeroLandingComponent } from './library/hero-landing/hero-landing.component';
import { HomeHero1Component } from './library/home-hero-1/home-hero-1.component';
import { HomeHero2Component } from './library/home-hero-2/home-hero-2.component';
import { HomeHero3Component } from './library/home-hero-3/home-hero-3.component';
import { HomestagingCardComponent } from './library/homestaging-card/homestaging-card.component';
import { HomestagingCtaComponent } from './library/homestaging-cta/homestaging-cta.component';
import { HomestagingSheetComponent } from './library/homestaging-sheet/homestaging-sheet.component';
import { InstagramGalleryComponent } from './library/instagram-gallery/instagram-gallery.component';
import { LandingFooterComponent } from './library/landing/landing-footer/landing-footer.component';
import { LandingFormComponent } from './library/landing/landing-form/landing-form.component';
import { LandingHeaderComponent } from './library/landing/landing-header/landing-header.component';
import { LandingSheetComponent } from './library/landing/landing-sheet/landing-sheet.component';
import { NeighborhoodAvgcostsComponent } from './library/neighborhood-avgcosts/neighborhood-avgcosts.component';
import { NeighborhoodDemographicsComponent } from './library/neighborhood-demographics/neighborhood-demographics.component';
import { NeighborhoodHeroComponent } from './library/neighborhood-hero/neighborhood-hero.component';
import { NeighborhoodHighlightsComponent } from './library/neighborhood-highlights/neighborhood-highlights.component';
import { NeighborhoodGridComponent } from './library/neighborhood-grid/neighborhood-grid.component';
import { NeighborhoodMapComponent } from './library/neighborhood-map/neighborhood-map.component';
import { NeighborhoodPaneComponent } from './library/neighborhood-map/neighborhood-pane/neighborhood-pane.component';
import { NeighborhoodMap2Component } from './library/neighborhood-map-2/neighborhood-map-2.component';
import { NeighborhoodListComponent } from './library/neighborhood-list/neighborhood-list.component';
import { NeighborhoodNavigationComponent } from './library/neighborhood-navigation/neighborhood-navigation.component';
import { NeighborhoodPhotosComponent } from './library/neighborhood-photos/neighborhood-photos.component';
import { OpenhouseCardComponent } from './library/openhouse-card/openhouse-card.component';
import { PdfGuideCta1Component } from './library/pdf-guide-cta-1/pdf-guide-cta-1.component';
import { PdfGuideCta2Component } from './library/pdf-guide-cta-2/pdf-guide-cta-2.component';
import { ProgramsListComponent } from './library/programs-list/programs-list.component';
import { PropertiesComponent } from './library/properties/properties.component';
import { PropertyBigSliderComponent } from './library/property-big-slider/property-big-slider.component';
import { PropertiesListComponent } from './library/properties-list/properties-list.component';
import { PropertiesList2Component } from './library/properties-list-2/properties-list-2.component';
import { PropertiesSliderComponent } from './library/properties-slider/properties-slider.component';
import { PropertiesBigSliderComponent } from './library/properties-big-slider/properties-big-slider.component';
import { PropertiesSoldMapComponent } from './library/properties-sold-map/properties-sold-map.component';
import { PropertiesTypeChoiceComponent } from './library/properties-type-choice/properties-type-choice.component';
import { PropertySheetComponent } from './library/property-sheet/property-sheet.component';
import { Property360Component } from './library/property-360/property-360.component';
import { PropertyAddendaComponent } from './library/property-addenda/property-addenda.component';
import { PropertyBrokerCard1Component } from './library/property-broker-card-1/property-broker-card-1.component';
import { PropertyCardComponent } from './library/property-card/property-card.component';
import { PropertyCharacteristicsComponent } from './library/property-characteristics/property-characteristics.component';
import { PropertyDescriptionComponent } from './library/property-description/property-description.component';
import { PropertyDetailsComponent } from './library/property-details/property-details.component';
import { PropertyDownloadsComponent } from './library/property-downloads/property-downloads.component';
import { PropertyFormContactComponent } from './library/property-form-contact/property-form-contact.component';
import { PropertyGroupSheetComponent } from './library/propertygroup-sheet/propertygroup-sheet.component';
import { PropertyGroupCardComponent } from './library/propertygroup-card/propertygroup-card.component';
import { PropertyGroupHeroComponent } from './library/propertygroup-hero/propertygroup-hero.component';
import { PropertyGroupNavigationComponent } from './library/propertygroup-navigation/propertygroup-navigation.component';
import { PropertyHeroComponent } from './library/property-hero/property-hero.component';
import { PropertyInclusionComponent } from './library/property-inclusion/property-inclusion.component';
import { PropertyMapComponent } from './library/property-map/property-map.component';
import { LocationPaneComponent } from './library/property-map/location-pane/location-pane.component';
import { PropertyPhotosComponent } from './library/property-photos/property-photos.component';
import { PropertyRoomsComponent } from './library/property-rooms/property-rooms.component';
import { PropertyShareComponent } from './library/property-share/property-share.component';
import { PropertyFavoriteComponent } from './library/property-favorite/property-favorite.component';
import { PropertyOpenhouseComponent } from './library/property-openhouse/property-openhouse.component';
import { PropertyNavigationComponent } from './library/property-navigation/property-navigation.component';
import { PropertyToolsComponent } from './library/property-tools/property-tools.component';
import { ToolEvaluationComponent } from './library/property-tools/tool-evaluation/tool-evaluation.component';
import { ToolExpensesComponent } from './library/property-tools/tool-expenses/tool-expenses.component';
import { ToolIncomeComponent } from './library/property-tools/tool-income/tool-income.component';
import { PropertyStatisticsComponent } from './library/property-statistics/property-statistics.component';
import { StatisticsFinancyComponent } from './library/property-statistics/statistics-financy/statistics-financy.component';
import { StatisticsEvaluationComponent } from './library/property-statistics/statistics-evaluation/statistics-evaluation.component';
import { SellSheetComponent } from './library/sell-sheet/sell-sheet.component';
import { SearchFullComponent } from './library/search-full/search-full.component';
import { FiltersComponent } from './library/search-full/filters/filters.component';
import { MapPaneComponent } from './library/search-full/map-pane/map-pane.component';
import { SortableComponent } from './library/search-full/sortable/sortable.component';
import { PropertiesPaneComponent } from './library/search-full/properties-pane/properties-pane.component';
import { SearchSimpleComponent } from './library/search-simple/search-simple.component';
import { SearchBuyComponent } from './library/search-buy/search-buy.component';
import { SearchSellComponent } from './library/search-sell/search-sell.component';
import { SpecialistsCardComponent } from './library/specialists-card/specialists-card.component';
import { TeamCard1Component } from './library/team-card-1/team-card-1.component';
import { TeamCard2Component } from './library/team-card-2/team-card-2.component';
import { TeamCard3Component } from './library/team-card-3/team-card-3.component';
import { TeamHero1Component } from './library/team-hero-1/team-hero-1.component';
import { TeamHero2Component } from './library/team-hero-2/team-hero-2.component';
import { TeamHero3Component } from './library/team-hero-3/team-hero-3.component';
import { TeamHero4Component } from './library/team-hero-4/team-hero-4.component';
import { TeamHero5Component } from './library/team-hero-5/team-hero-5.component';
import { TeamInterstice1Component } from './library/team-interstice-1/team-interstice-1.component';
import { TeamInterstice2Component } from './library/team-interstice-2/team-interstice-2.component';
import { TeamInterstice3Component } from './library/team-interstice-3/team-interstice-3.component';
import { TeamCard4Component } from './library/team-card-4/team-card-4.component';
import { TestimonialsCardComponent } from './library/testimonials-card/testimonials-card.component';
import { TestimonialsSliderComponent } from './library/testimonials-slider/testimonials-slider.component';
import { PropertiesFeaturedComponent } from './library/properties-featured/properties-featured.component';
import { PropertyFeaturedComponent } from './library/property-featured/property-featured.component';
import { OpenhousesListComponent } from './library/openhouses-list/openhouses-list.component';
import { OpenhouseCardLargeComponent } from './library/openhouse-card-large/openhouse-card-large.component';
import { SliderContentComponent } from './library/slider-content/slider-content.component';
import { HomestagingSliderComponent } from './library/homestaging-slider/homestaging-slider.component';
import { SliderDefaultComponent } from './library/slider-default/slider-default.component';
import { VideoViewerComponent } from './library/video-viewer/video-viewer.component';
import { GooglemybusinessComponent } from './library/googlemybusiness/googlemybusiness.component';
import { FacebookPostsComponent } from './library/facebook-posts/facebook-posts.component';
import { ReasonsSliderComponent } from './library/reasons-slider/reasons-slider.component';

// VIEWS COMPONENTS
import { AlertComponent } from './views/alert/alert.component';
import { BlogComponent } from './views/blog/blog.component';
import { BlogPostComponent } from './views/blog-post/blog-post.component';
import { BuyComponent } from './views/buy/buy.component';
import { CareerComponent } from './views/career/career.component';
import { ContactComponent } from './views/contact/contact.component';
import { EvaluationComponent } from './views/evaluation/evaluation.component';
import { FavoritesComponent } from './views/favorites/favorites.component';
import { HomeComponent } from './views/home/<USER>';
import { HomestagingComponent } from './views/homestaging/homestaging.component';
import { NeighborhoodComponent } from './views/neighborhood/neighborhood.component';
import { NeighborhoodsComponent } from './views/neighborhoods/neighborhoods.component';
import { OpenhouseComponent } from './views/openhouse/openhouse.component';
import { PropertyComponent } from './views/property/property.component';
import { PropertygroupComponent } from './views/propertygroup/propertygroup.component';
import { PropertygroupsComponent } from './views/propertygroups/propertygroups.component';
import { SearchComponent } from './views/search/search.component';
import { SellComponent } from './views/sell/sell.component';
import { SpecialistsComponent } from './views/specialists/specialists.component';
import { TeamComponent } from './views/team/team.component';
import { Team2Component } from './views/team/team-2/team-2.component';
import { Team3Component } from './views/team/team-3/team-3.component';
import { Team4Component } from './views/team/team-4/team-4.component';
import { Team5Component } from './views/team/team-5/team-5.component';
import { Team6Component } from './views/team/team-6/team-6.component';
import { TeamMemberComponent } from './views/team-member/team-member.component';
import { TestimonialsComponent } from './views/testimonials/testimonials.component';
import { LandingComponent } from './views/landing/landing.component';
import { Page404Component } from './views/page-404/page-404.component';
import { PrivacyPolicyComponent } from './views/privacy-policy/privacy-policy.component';
import { ReasonsComponent } from './views/reasons/reasons.component';
import { PreloadStatusComponent } from './components/preload-status/preload-status.component';

// DIRECTIVES
import { VarnishInterceptor } from './services/v3/varnish.interceptor';

// SERVICES V3
import { MetatagsService } from './services/v3/metatags/metatags.service';
import { InscriptionsService } from './services/v3/inscriptions/inscriptions.service';
import { TestimonialsService } from './services/v3/testimonials/testimonials.service';
import { AwardsService } from './services/v3/awards/awards.service';
import { TeamsMembersService } from './services/v3/teammembers/teammembers.service';
import { FavoritesService } from './services/v3/favorites/favorites.service';
import { NeighborhoodsService } from './services/v3/neighborhoods/neighborhoods.service';
import { BlogService } from './services/v3/blog/blog.service';
import { BlocksService } from './services/v3/contentblocks/contentblocks.service';
import { HomePageService } from './services/v3/home-page-block/home-page-block.service';
import { HomestagingService } from './services/v3/homestaging/homestaging.service';
import { InscriptionGroupsService } from './services/v3/inscriptiongroups/inscriptiongroups.service';
import { CollaboratorsService } from './services/v3/collaborators/collaborators.service';
import { OpenhouseService } from './services/v3/openhouse/openhouse.service';
import { EvaluationService } from './services/v3/evaluation/evaluation.service';
import { ContactService } from './services/v3/contact/contact.service';
import { AlertService } from './services/v3/alert/alert.service';
import { CareerService } from './services/v3/career/career.service';
import { ProgramsService } from './services/v3/programs/programs.service';
import { LandingService } from './services/v3/landing/landing.service';
import { UtilsService } from './services/v3/utils/utils.service';
import { GooglemybusinessService } from '@/services/v3/googlemybusiness/googlemybusiness.service';
import { FacebookPostsService } from '@/services/v3/facebook-posts/facebook-posts.service';
import { PreloadService } from './services/v3/preload/preload.service';
import { ImagePreloadService } from './services/v3/preload/image-preload.service';

// CUSTOMS PIPES
import { DateRangePipe } from './pipes/date-range.pipe';
import { NumberFormatPipe } from './pipes/number-format.pipe';
import { PhonePipe } from './pipes/phone.pipe';
import { LocalizedDatePipe } from './pipes/localized-date.pipe';
// CUSTOMS Diretive
import { LazyImageDirective } from './directives/lazy-image.directive';

registerLocaleData(localeFr, 'fr');
registerLocaleData(localeEn, 'en');

export function createTranslateLoader (http: HttpClient) {
  return new TranslateHttpLoader(http, '../assets/i18n/', '.json?cb=' + new Date().getTime());
}
const customConfig: ShareButtonsConfig = {
  autoSetMeta: true
};

@NgModule({
  declarations: [
    AppComponent,

    // LIBRARY COMPONENTS
    AlertCta1Component,
    AlertCta2Component,
    AlertFormComponent,
    Article1Component,
    Article2Component,
    AwardCardComponent,
    AwardsSliderComponent,
    BlogDetailsComponent,
    BlogList1Component,
    BlogList2Component,
    BlogList3Component,
    BlogList4Component,
    CustomContentComponent,
    BrokerContactHeader1Component,
    BrokerContactHeader2Component,
    BrokerContactFormComponent,
    BrokerContactListComponent,
    BrokerContactMapComponent,
    BrokerCardComponent,
    BuySheetComponent,
    CareerContactComponent,
    CenteredCtaComponent,
    CtaBrokerComponent,
    CtaCenteredComponent,
    CtaEvaluationComponent,
    CtaEvaluationSmallComponent,
    CtaAlertComponent,
    CtaAlertSmallComponent,
    CtaSmallComponent,
    CtaLeftComponent,
    EvaluationFormComponent,
    EvaluationFormSinglePageComponent,
    FooterComponent,
    HeaderPanelComponent,
    HeaderComponent,
    Header2Component,
    Header3Component,
    Header4Component,
    Header5Component,
    Panel5Component,
    HeaderLastPropertiesComponent,
    HeroLandingComponent,
    HomeHero1Component,
    HomeHero2Component,
    HomeHero3Component,
    HomestagingCardComponent,
    HomestagingCtaComponent,
    HomestagingSheetComponent,
    InstagramGalleryComponent,
    LandingFooterComponent,
    LandingFormComponent,
    LandingHeaderComponent,
    LandingSheetComponent,
    NeighborhoodAvgcostsComponent,
    NeighborhoodDemographicsComponent,
    NeighborhoodHeroComponent,
    NeighborhoodHighlightsComponent,
    NeighborhoodGridComponent,
    NeighborhoodMapComponent,
    NeighborhoodPaneComponent,
    NeighborhoodMap2Component,
    NeighborhoodListComponent,
    NeighborhoodNavigationComponent,
    NeighborhoodPhotosComponent,
    OpenhouseCardComponent,
    PdfGuideCta1Component,
    PdfGuideCta2Component,
    ProgramsListComponent,
    PropertiesComponent,
    PropertyBigSliderComponent,
    PropertiesListComponent,
    PropertiesList2Component,
    PropertiesSliderComponent,
    PropertiesBigSliderComponent,
    PropertiesSoldMapComponent,
    PropertiesTypeChoiceComponent,
    PropertySheetComponent,
    Property360Component,
    PropertyAddendaComponent,
    PropertyBrokerCard1Component,
    PropertyCardComponent,
    PropertyCharacteristicsComponent,
    PropertyDescriptionComponent,
    PropertyDetailsComponent,
    PropertyDownloadsComponent,
    PropertyGroupSheetComponent,
    PropertyGroupCardComponent,
    PropertyGroupHeroComponent,
    PropertyGroupNavigationComponent,
    PropertyHeroComponent,
    PropertyInclusionComponent,
    PropertyMapComponent,
    LocationPaneComponent,
    PropertyPhotosComponent,
    PropertyRoomsComponent,
    PropertyShareComponent,
    PropertyFavoriteComponent,
    PropertyOpenhouseComponent,
    PropertyNavigationComponent,
    PropertyToolsComponent,
    ToolEvaluationComponent,
    ToolExpensesComponent,
    ToolIncomeComponent,
    PropertyStatisticsComponent,
    StatisticsFinancyComponent,
    StatisticsEvaluationComponent,
    SellSheetComponent,
    SearchFullComponent,
    FiltersComponent,
    MapPaneComponent,
    SortableComponent,
    PropertiesPaneComponent,
    SearchSimpleComponent,
    SearchBuyComponent,
    SearchSellComponent,
    SpecialistsCardComponent,
    TeamCard1Component,
    TeamCard2Component,
    TeamCard3Component,
    TeamHero1Component,
    TeamHero2Component,
    TeamHero3Component,
    TeamHero4Component,
    TeamHero5Component,
    TeamInterstice1Component,
    TeamInterstice2Component,
    TeamInterstice3Component,
    TeamCard4Component,
    TestimonialsCardComponent,
    TestimonialsSliderComponent,
    VideoViewerComponent,
    GooglemybusinessComponent,
    FacebookPostsComponent,
    ReasonsSliderComponent,

    // VIEW COMPONENTS
    AlertComponent,
    BlogComponent,
    BlogPostComponent,
    BuyComponent,
    CareerComponent,
    ContactComponent,
    EvaluationComponent,
    FavoritesComponent,
    HomeComponent,
    HomestagingComponent,
    NeighborhoodComponent,
    NeighborhoodsComponent,
    OpenhouseComponent,
    PropertyComponent,
    PropertygroupComponent,
    PropertygroupsComponent,
    SearchComponent,
    SellComponent,
    SpecialistsComponent,
    TeamComponent,
    Team2Component,
    Team3Component,
    Team4Component,
    Team5Component,
    Team6Component,
    TeamMemberComponent,
    TestimonialsComponent,
    LandingComponent,
    Page404Component,
    DateRangePipe,
    NumberFormatPipe,
    PhonePipe,
    LocalizedDatePipe,
    PropertyFormContactComponent,
    PdfGuideCta1Component,
    PropertiesSoldMapComponent,
    PropertiesTypeChoiceComponent,
    PropertiesFeaturedComponent,
    PropertyFeaturedComponent,
    OpenhousesListComponent,
    OpenhouseCardLargeComponent,
    SliderContentComponent,
    HomestagingSliderComponent,
    SliderDefaultComponent,
    PrivacyPolicyComponent,
    ReasonsComponent,
    PreloadStatusComponent,
    // CS DIRECTIVES
    LazyImageDirective
  ],
  // DIRECTIVES
  imports: [
    RecaptchaV3Module,
    BrowserModule,
    ReactiveFormsModule,
    ShareModule.withConfig(customConfig),
    FormsModule,
    AppRoutingModule,
    HttpClientModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: createTranslateLoader,
        deps: [HttpClient]
      }
    }),
    NgPipesModule,
    NgSelectModule,
    ChartsModule,
    SwiperModule,
    NgxWebstorageModule.forRoot(),
    AngularMyDatePickerModule,
    NgxPaginationModule,
    NgxPageScrollModule,
    FileUploadModule,
    GoogleMapsModule,
    NgxMaskModule.forRoot()
  ],
  providers: [
    {
      provide: RECAPTCHA_V3_SITE_KEY,
      useValue: environment.recaptchaSiteKey
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: VarnishInterceptor,
      multi: true
    },
    MetatagsService,
    InscriptionsService,
    TestimonialsService,
    AwardsService,
    TeamsMembersService,
    FavoritesService,
    NeighborhoodsService,
    BlogService,
    BlocksService,
    HomePageService,
    HomestagingService,
    InscriptionGroupsService,
    CollaboratorsService,
    OpenhouseService,
    EvaluationService,
    ContactService,
    AlertService,
    CareerService,
    ProgramsService,
    LandingService,
    UtilsService,
    GooglemybusinessService,
    FacebookPostsService,
    PreloadService,
    ImagePreloadService,
    DatePipe,
    CurrencyPipe,
    { provide: LOCALE_ID, useValue: 'fr' },
    { provide: LOCALE_ID, useValue: 'en' }
  ],
  bootstrap: [AppComponent]
})
export class AppModule { }
