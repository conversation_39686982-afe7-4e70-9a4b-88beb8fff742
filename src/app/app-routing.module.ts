import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';

import { HomeComponent } from './views/home/<USER>';
import { AlertComponent } from './views/alert/alert.component';
import { BlogComponent } from './views/blog/blog.component';
import { BlogPostComponent } from './views/blog-post/blog-post.component';
import { BuyComponent } from './views/buy/buy.component';
import { CareerComponent } from './views/career/career.component';
import { ContactComponent } from './views/contact/contact.component';
import { EvaluationComponent } from './views/evaluation/evaluation.component';
import { FavoritesComponent } from './views/favorites/favorites.component';
import { HomestagingComponent } from './views/homestaging/homestaging.component';
import { NeighborhoodComponent } from './views/neighborhood/neighborhood.component';
import { NeighborhoodsComponent } from './views/neighborhoods/neighborhoods.component';
import { OpenhouseComponent } from './views/openhouse/openhouse.component';
import { PropertyComponent } from './views/property/property.component';
import { PropertygroupComponent } from './views/propertygroup/propertygroup.component';
import { PropertygroupsComponent } from './views/propertygroups/propertygroups.component';
import { SearchComponent } from './views/search/search.component';
import { SellComponent } from './views/sell/sell.component';
import { SpecialistsComponent } from './views/specialists/specialists.component';
import { TeamComponent } from './views/team/team.component';
import { Team2Component } from './views/team/team-2/team-2.component';
import { Team3Component } from './views/team/team-3/team-3.component';
import { Team4Component } from './views/team/team-4/team-4.component';
import { Team5Component } from './views/team/team-5/team-5.component';
import { Team6Component } from './views/team/team-6/team-6.component';
import { TeamMemberComponent } from './views/team-member/team-member.component';
import { TestimonialsComponent } from './views/testimonials/testimonials.component';
import { LandingComponent } from './views/landing/landing.component';
import { Page404Component } from './views/page-404/page-404.component';
import { PrivacyPolicyComponent } from './views/privacy-policy/privacy-policy.component';
import { ReasonsComponent } from './views/reasons/reasons.component';

@NgModule({
  imports: [
    RouterModule.forRoot([
      // Default
      { path: '', redirectTo: 'fr', pathMatch: 'full' },

      // Home
      { path: 'fr', component: HomeComponent },
      { path: 'en', component: HomeComponent },

      // Alert
      { path: 'fr/alerte-immobiliere', component: AlertComponent },
      { path: 'en/real-estate-alert', component: AlertComponent },

      // Blog
      { path: 'fr/blogue-immobilier', component: BlogComponent },
      { path: 'en/blog-real-estate-news', component: BlogComponent },
      { path: 'fr/blogue-immobilier/:slug', component: BlogPostComponent },
      { path: 'en/blog-real-estate-news/:slug', component: BlogPostComponent },

      // Buy
      { path: 'fr/devenir-proprietaire', component: BuyComponent },
      { path: 'en/become-an-owner', component: BuyComponent },

      // Career
      { path: 'fr/carriere', component: CareerComponent },
      { path: 'en/career', component: CareerComponent },

      // Contact
      { path: 'fr/contact', component: ContactComponent },
      { path: 'en/contact', component: ContactComponent },

      // Evaluation
      { path: 'fr/estimation-immobiliere-en-ligne/:address', component: EvaluationComponent },
      { path: 'fr/estimation-immobiliere-en-ligne', component: EvaluationComponent },
      { path: 'en/online-property-evaluation/:address', component: EvaluationComponent },
      { path: 'en/online-property-evaluation', component: EvaluationComponent },

      // Favorite
      { path: 'fr/mes-favoris', component: FavoritesComponent },
      { path: 'en/my-favorites', component: FavoritesComponent },

      // Homestaging
      { path: 'fr/home-staging', component: HomestagingComponent },
      { path: 'en/home-staging', component: HomestagingComponent },

      // Neighborhoods
      { path: "fr/nos-secteurs", component: NeighborhoodsComponent },
      { path: "en/neighborhoods", component: NeighborhoodsComponent },
      { path: "fr/nos-secteurs/:slug", component: NeighborhoodComponent },
      { path: "en/neighborhoods/:slug", component: NeighborhoodComponent },

      // Openhouse
      { path: 'fr/visites-libres', component: OpenhouseComponent },
      { path: 'en/open-house', component: OpenhouseComponent },

      // Properties
      { path: 'fr/propriete/:municipality/:neighborhood/:address/:mls', component: PropertyComponent },
      { path: 'fr/propriete/:municipality/:address/:mls', component: PropertyComponent },
      { path: 'en/property/:municipality/:neighborhood/:address/:mls', component: PropertyComponent },
      { path: 'en/property/:municipality/:address/:mls', component: PropertyComponent },

      // Property groups
      { path: 'fr/projets-immobiliers', component: PropertygroupsComponent },
      { path: 'en/real-estate-projects', component: PropertygroupsComponent },
      { path: 'fr/projets-immobiliers/:slug', component: PropertygroupComponent },
      { path: 'en/real-estate-projects/:slug', component: PropertygroupComponent },

      // Search
      { path: 'fr/liste-proprietes-a-vendre/:municipality', component: SearchComponent },
      { path: 'fr/liste-proprietes-a-vendre', component: SearchComponent },
      { path: 'en/properties-for-sale/:municipality', component: SearchComponent },
      { path: 'en/properties-for-sale', component: SearchComponent },

      // Sell
      { path: 'fr/strategie-de-vente', component: SellComponent },
      { path: 'en/sales-strategy', component: SellComponent },

      // Specialists
      { path: 'fr/nos-specialistes', component: SpecialistsComponent },
      { path: 'en/our-specialists', component: SpecialistsComponent },

      // Team
      { path: 'fr/courtiers-immobiliers', component: TeamComponent },
      { path: 'en/real-estate-agents', component: TeamComponent },
      { path: 'fr/courtiers-immobiliers-2', component: Team2Component },
      { path: 'en/real-estate-agents-2', component: Team2Component },
      { path: 'fr/courtiers-immobiliers-3', component: Team3Component },
      { path: 'en/real-estate-agents-3', component: Team3Component },
      { path: 'fr/courtiers-immobiliers-4', component: Team4Component },
      { path: 'en/real-estate-agents-4', component: Team4Component },
      { path: 'fr/courtiers-immobiliers-5', component: Team5Component },
      { path: 'en/real-estate-agents-5', component: Team5Component },
      { path: 'fr/courtiers-immobiliers-6', component: Team6Component },
      { path: 'en/real-estate-agents-6', component: Team6Component },
      { path: 'fr/courtiers-immobiliers/:slug', component: TeamMemberComponent },
      { path: 'en/real-estate-agents/:slug', component: TeamMemberComponent },

      // Testimonials
      { path: 'fr/temoignages', component: TestimonialsComponent },
      { path: 'en/testimonials', component: TestimonialsComponent },

      // Custom Landing
      { path: 'fr/campagne/:slug', component: LandingComponent },
      { path: 'en/landing/:slug', component: LandingComponent },

      //Buy
      { path: "fr/50-raisons", component: ReasonsComponent },
      { path: "en/50-reasons", component: ReasonsComponent },

      // Privacy policy
      {
        path: 'fr/politique-de-confidentialite',
        component: PrivacyPolicyComponent
      },
      { path: 'en/privacy-policy', component: PrivacyPolicyComponent },

      // Page not found - KEEP LAST!!!
      { path: '**', component: Page404Component }
    ], { scrollPositionRestoration: 'enabled', relativeLinkResolution: 'legacy' })
  ],
  exports: [RouterModule]
})
export class AppRoutingModule { }
