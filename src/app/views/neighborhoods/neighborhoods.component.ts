import { Component, OnInit } from '@angular/core';
import { LocalStorageService } from 'ngx-webstorage';

import { NeighborhoodsService } from '@/services/v3/neighborhoods/neighborhoods.service';
import { MetatagsService } from '@/services/v3/metatags/metatags.service';

@Component({
  selector: 'view-neighborhoods',
  templateUrl: './neighborhoods.component.html'
})

export class NeighborhoodsComponent implements OnInit {
  public neighborhoods: any;

  constructor (
    private storage: LocalStorageService,
    private neighborhoodsService: NeighborhoodsService,
    private metatagsService: MetatagsService
  ) {
    this.metatagsService.updateMetatags('neighborhoods');
  }

  ngOnInit () {
    this.neighborhoodsService.getNeighborhoods().subscribe(({ data }) => {
      // this.neighborhoods = data;
      // this.addToLocalStorage();
      this.neighborhoods = data.features.map(f => ({ ...f.properties }));
      this.addToLocalStorage();
    });
  }

  addToLocalStorage () {
    const navigation = [];

    Object.keys(this.neighborhoods.features).forEach(key => {
      navigation.push(this.neighborhoods.features[key].properties.id);
    });

    this.storage.store('navigationneighborhood', navigation);
  }
}
