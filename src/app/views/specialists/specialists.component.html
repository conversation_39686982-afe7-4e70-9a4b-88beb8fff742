<lib-header></lib-header>

<div class="specialists-page">
	<div class="container">

		<div class="specialists-head -page-head">
			<h1 class="page-title">{{ 'views.specialists.title' | translate }}</h1>

			<ul class="sortable-list tabs">
				<input class="input-tab" id="tab1" type="radio" name="tabs" checked>
				<label (click)="onCategoryChange('')" for="tab1">{{ 'views.specialists.all' | translate }}</label>

				<ng-container *ngFor="let category of specialistCategories">
          <input class="input-tab" id="tab-{{ category.slug }}" type="radio" name="tabs">
          <label (click)="onCategoryChange(category.slug)" for="tab-{{ category.slug }}">{{ category.name }}</label>
				</ng-container>
			</ul>
		</div>

		<div class="grid-ctn" id="specialist-list">
			<div class="specialists-wrap">
				<div class="specialist-card-cpn" *ngFor="let specialist of specialists | filterBy: ['slug_category']: currentCategory | paginate: { id: 'specialists-pagination', itemsPerPage: limitPerPage, currentPage: cPage, totalItems:totalLength}">
					<lib-specialists-card [specialist]="specialist"></lib-specialists-card>
				</div>
			</div>
		</div>

		<pagination-controls id="specialists-pagination" class="paginiation-controls"
			(pageChange)="onPageChange($event)"
			maxSize="5"
			directionLinks="true"
			previousLabel=""
			nextLabel=""
			autoHide="true">
		</pagination-controls>

	</div>
</div>

<lib-footer></lib-footer>
