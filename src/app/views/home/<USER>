import { Component, OnInit, ViewChild } from '@angular/core';

import { InscriptionsService } from '@/services/v3/inscriptions/inscriptions.service';
import { MetatagsService } from '@/services/v3/metatags/metatags.service';
import { PropertiesListComponent } from '@/library/properties-list/properties-list.component';
import { HomePageService } from '@/services/v3/home-page-block/home-page-block.service';
import { PreloadService } from '@/services/v3/preload/preload.service';

@Component({
  selector: 'view-home',
  templateUrl: './home.component.html'
})

export class HomeComponent implements OnInit {
  @ViewChild(PropertiesListComponent)
  private propertiesListComponent: PropertiesListComponent;

  customContent: any;
  propertiesFeatured: any;
  properties: any;
  propertiesFree: any;

  constructor (
    private homePageService: HomePageService,
    private inscriptionsService: InscriptionsService,
    private metatagsService: MetatagsService,
    private preloadService: PreloadService
  ) {
    this.metatagsService.updateMetatags('home');

    // Use preloaded data if available, otherwise fallback to direct service call
    this.preloadService.getHomePageContent().subscribe(({ data }) => {
      this.customContent = data;
      // console.log(this.customContent)
    });
  }

  ngOnInit () {
    // Use preloaded data for better performance
    this.preloadService.getFeaturedProperties().subscribe(({ data }) => {
      this.propertiesFeatured = data;
      // Preload images for featured properties
      this.preloadService.preloadPropertyImages(data);
    });

    this.preloadService.getNewProperties().subscribe(({ data }) => {
      this.properties = data;
      // Preload images for new properties (first 6)
      this.preloadService.preloadPropertyImages(data.slice(0, 6));
    });

    this.preloadService.getOpenHouseProperties().subscribe(({ data }) => {
      this.propertiesFree = data;
      // Preload images for open house properties (first 6)
      this.preloadService.preloadPropertyImages(data.slice(0, 6));
    });
  }
}
