import { Component, OnInit, ViewChild } from '@angular/core';

import { InscriptionsService } from '@/services/v3/inscriptions/inscriptions.service';
import { MetatagsService } from '@/services/v3/metatags/metatags.service';
import { PropertiesListComponent } from '@/library/properties-list/properties-list.component';
import { HomePageService } from '@/services/v3/home-page-block/home-page-block.service';

@Component({
  selector: 'view-home',
  templateUrl: './home.component.html'
})

export class HomeComponent implements OnInit {
  @ViewChild(PropertiesListComponent)
  private propertiesListComponent: PropertiesListComponent;

  customContent: any;
  propertiesFeatured: any;
  properties: any;
  propertiesFree: any;

  constructor (
    private homePageService: HomePageService,
    private inscriptionsService: InscriptionsService,
    private metatagsService: MetatagsService
  ) {
    this.metatagsService.updateMetatags('home');

    this.homePageService.getHomePage().subscribe(({ data }) => {
      this.customContent = data;
      // console.log(this.customContent)
    });
  }

  ngOnInit () {
    this.inscriptionsService.getInscriptions(-1, { featured: 1, sold: 0, sort: 'rand' }).subscribe(({ data }) => {
      this.propertiesFeatured = data;
    });

    this.inscriptionsService.getInscriptions(-1, { sold: 0, sort: "newest" }).subscribe(({ data }) => {
      this.properties = data;
    });

    this.inscriptionsService.getInscriptions(-1, { featured: 0, flags: ["openhouse"] }).subscribe(({ data }) => {
      this.propertiesFree = data;
    });
  }
}
