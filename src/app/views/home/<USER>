import { Component, OnInit, ViewChild } from '@angular/core';

import { InscriptionsService } from '@/services/v3/inscriptions/inscriptions.service';
import { MetatagsService } from '@/services/v3/metatags/metatags.service';
import { PropertiesListComponent } from '@/library/properties-list/properties-list.component';
import { HomePageService } from '@/services/v3/home-page-block/home-page-block.service';
import { PreloadService } from '@/services/v3/preload/preload.service';

@Component({
  selector: 'view-home',
  templateUrl: './home.component.html'
})

export class HomeComponent implements OnInit {
  @ViewChild(PropertiesListComponent)
  private propertiesListComponent: PropertiesListComponent;

  customContent: any;
  propertiesFeatured: any;
  properties: any;
  propertiesFree: any;

  constructor (
    private homePageService: HomePageService,
    private inscriptionsService: InscriptionsService,
    private metatagsService: MetatagsService,
    private preloadService: PreloadService
  ) {
    this.metatagsService.updateMetatags('home');

    // Defer data loading to ngOnInit to ensure all services are ready
    // This prevents the "Cannot read properties of undefined" error
  }

  ngOnInit () {
    // Load home page content
    const homePageContent$ = this.preloadService.getHomePageContent();
    if (homePageContent$) {
      homePageContent$.subscribe({
        next: ({ data }) => {
          this.customContent = data;
        },
        error: (error) => {
          console.warn('Error loading home page content from preload, using direct service:', error);
          this.homePageService.getHomePage().subscribe(({ data }) => {
            this.customContent = data;
          });
        }
      });
    } else {
      // Fallback to direct service call
      console.warn('PreloadService not ready, using direct service call for home page');
      this.homePageService.getHomePage().subscribe(({ data }) => {
        this.customContent = data;
      });
    }

    // Use preloaded data for better performance with fallbacks
    const featuredProperties$ = this.preloadService.getFeaturedProperties();
    if (featuredProperties$) {
      featuredProperties$.subscribe(({ data }) => {
        this.propertiesFeatured = data;
        // Preload images for featured properties
        this.preloadService.preloadPropertyImages(data);
      });
    } else {
      // Fallback to direct service call
      this.inscriptionsService.getInscriptions(-1, { featured: 1, sold: 0, sort: 'rand' }).subscribe(({ data }) => {
        this.propertiesFeatured = data;
      });
    }

    const newProperties$ = this.preloadService.getNewProperties();
    if (newProperties$) {
      newProperties$.subscribe(({ data }) => {
        this.properties = data;
        // Preload images for new properties (first 6)
        this.preloadService.preloadPropertyImages(data.slice(0, 6));
      });
    } else {
      // Fallback to direct service call
      this.inscriptionsService.getInscriptions(-1, { sold: 0, sort: "newest" }).subscribe(({ data }) => {
        this.properties = data;
      });
    }

    const openHouseProperties$ = this.preloadService.getOpenHouseProperties();
    if (openHouseProperties$) {
      openHouseProperties$.subscribe(({ data }) => {
        this.propertiesFree = data;
        // Preload images for open house properties (first 6)
        this.preloadService.preloadPropertyImages(data.slice(0, 6));
      });
    } else {
      // Fallback to direct service call
      this.inscriptionsService.getInscriptions(-1, { featured: 0, flags: ["openhouse"] }).subscribe(({ data }) => {
        this.propertiesFree = data;
      });
    }
  }


}
