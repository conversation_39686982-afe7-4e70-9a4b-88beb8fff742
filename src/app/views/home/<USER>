<lib-header class="overwriteAbsolute"></lib-header>
<lib-properties-big-slider [properties]="propertiesFeatured"></lib-properties-big-slider>
<lib-properties-slider
        [properties]="properties"
        [title]="'library.properties-slider.title-new' | translate"
        [button]="'library.properties-slider.button-new' | translate"
        [class]="propertiesFree?.length === 0 ? '-pdb' : ''"
></lib-properties-slider>

<lib-properties-slider
        [properties]="propertiesFree"
        [title]="'library.properties-slider.title-free' | translate"
        [button]="'library.properties-slider.button-free' | translate"
        [class]="propertiesFree?.length > 0 ? '-pdb' : ''"
></lib-properties-slider>
<lib-cta-broker></lib-cta-broker>
<lib-cta-evaluation></lib-cta-evaluation>
<lib-cta-small></lib-cta-small>
<lib-cta-left></lib-cta-left>
<lib-properties-sold-map></lib-properties-sold-map>
<lib-googlemybusiness></lib-googlemybusiness>
<lib-facebook-posts></lib-facebook-posts>
<lib-footer></lib-footer>

