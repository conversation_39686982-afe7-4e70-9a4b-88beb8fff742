import { Component, OnInit } from '@angular/core';
import { LocalStorageService } from 'ngx-webstorage';

import { InscriptionsService } from '@/services/v3/inscriptions/inscriptions.service';
import { MetatagsService } from '@/services/v3/metatags/metatags.service';

@Component({
  selector: 'view-favorites',
  templateUrl: './favorites.component.html'
})

export class FavoritesComponent implements OnInit {
  public favoriteList: any[] = [];
  public arrayFavoriteList: any[] = [];

  properties: any;
  isLoad: boolean = false;

  constructor (
    private storage: LocalStorageService,
    private inscriptionsService: InscriptionsService,
    private metatagsService: MetatagsService
  ) {
    this.metatagsService.updateMetatags('favorites');
  }

  ngOnInit () {
    if (this.storage.retrieve('propertiesFavorite') == null || this.storage.retrieve('propertiesFavorite') === []) {
      this.favoriteList = [];
    } else {
      this.favoriteList = this.storage.retrieve('propertiesFavorite');
    }

    Object.keys(this.favoriteList).forEach(key => {
      this.arrayFavoriteList.push(this.favoriteList[key].id);
    });
  }

  ngAfterViewInit () {
    this.getProperties();
  }

  getProperties () {
    if (this.favoriteList.length === 0) return;

    this.inscriptionsService.getInscriptions(-1, { mls: this.arrayFavoriteList }).subscribe(data => {
      this.isLoad = true;
      this.properties = data;
    });
  }
}
