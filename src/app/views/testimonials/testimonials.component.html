<lib-header></lib-header>

<div class="testimonials-page">
	<div class="container">

		<div class="testimonials-head -page-head">
			<h1 class="page-title">{{ 'views.testimonials.title' | translate }}</h1>
            <ul class="sortable-list tabs">
                <input class="input-tab" id="tab1" type="radio" name="tabs" checked>
                <label (click)="onFilterChange('all')" for="tab1">{{ "global.all" | translate }}</label>
                <input class="input-tab" id="tab2" type="radio" name="tabs">
                <label (click)="onFilterChange('video')" for="tab2">{{ 'views.testimonials.videos' | translate }}</label>
            </ul>
		</div>

		<div class="grid-ctn">
			<div class="testimonials-wrap -full">
				<div class="testimonial-card-cpn" id="testimonial-list" *ngFor="let testimonial of testimonials | paginate: { id: 'testimonial-pagination', itemsPerPage: 8, currentPage: cPage}">
					<lib-testimonials-card [testimonial]="testimonial"></lib-testimonials-card>
				</div>
			</div>
		</div>

		<pagination-controls id="testimonial-pagination" class="paginiation-controls"
            (pageChange)="onPageChange($event)"
			maxSize="5"
			directionLinks="true"
			previousLabel=""
			nextLabel=""
			autoHide="true">
		</pagination-controls>

	</div>
</div>

<lib-footer></lib-footer>
