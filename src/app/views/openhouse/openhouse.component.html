<lib-header></lib-header>
<div class="open-house-page">
  <div class="container">
    <div class="open-house-head -page-head">
      <h1 class="page-title">{{ "views.openhouse.title" | translate }}</h1>
      <p class="page-description">
        {{ "views.openhouse.description" | translate }}
      </p>

      <form #myForm="ngForm" class="datepicker-ctn" novalidate>
        <input
          angular-mydatepicker
          class="datepicker"
          placeholder="{{ 'views.openhouse.placeholder' | translate }}"
          #dp="angular-mydatepicker"
          name="mydaterange"
          (click)="dp.toggleCalendar()"
          (dateChanged)="onDateRangeChanged($event)"
          [locale]="translate.currentLang"
          [options]="myDateRangePickerOptions"
        />
      </form>
    </div>

    <div class="propeties-wrap">
      <div
        class="propeties-per-day-ctn"
        *ngFor="
          let day of visits
            | dateRange : beginDate : endDate
            | paginate
              : {
                  id: 'visits-pagination',
                  itemsPerPage: 4,
                  currentPage: cPage
                } as result
        "
      >
        <p class="visit-date" *ngIf="!day.today">
          {{ day.start_date | localizedDate : "longDate" }}
        </p>
        <p class="visit-date" *ngIf="day.today">{{ day.today }}</p>
        <div class="properties-list-ctn">
          <div class="properties" *ngFor="let property of day.properties">
            <lib-openhouse-card
              [property]="property"
              [date]="day.start_date"
            ></lib-openhouse-card>
          </div>
        </div>
      </div>
      <p
        class="no-results"
        *ngIf="(visits | dateRange : beginDate : endDate)?.length === 0"
      >
        {{ "views.openhouse.no-result" | translate }}
      </p>
    </div>

    <pagination-controls
      id="visits-pagination"
      class="paginiation-controls"
      (pageChange)="onPageChange($event)"
      maxSize="5"
      directionLinks="true"
      previousLabel=""
      nextLabel=""
      autoHide="true"
    >
    </pagination-controls>

    <div class="button-ctn">
      <a
        class="main-button -primary"
        [routerLink]="['urls.search-properties' | translate]"
        >{{ "views.openhouse.all-our-properties" | translate }}</a
      >
    </div>
  </div>
</div>
<lib-footer></lib-footer>
