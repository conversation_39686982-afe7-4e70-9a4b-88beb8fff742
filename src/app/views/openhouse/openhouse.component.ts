import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

import { MetatagsService } from '@/services/v3/metatags/metatags.service';
import { OpenhouseService } from '@/services/v3/openhouse/openhouse.service';

@Component({
  selector: 'view-openhouse',
  templateUrl: './openhouse.component.html'
})

export class OpenhouseComponent implements OnInit {
  public cPage: number = 1;
  public visits: any = [];
  public beginDate: any;
  public endDate: any;

  myDateRangePickerOptions = { dateRange: 'true' };

  constructor (
    private openHouseService: OpenhouseService,
    public translate: TranslateService,
    private metatagsService: MetatagsService
  ) {
    this.openHouseService.getOpenhouse().subscribe(data => {
      this.visits = data;
    });

    if (this.translate.currentLang === 'en') {
      //   this.myDateRangePickerOptions.monthLabels = { 1: 'Jan', 2: 'Feb', 3: 'Mar', 4: 'Apr', 5: 'May', 6: 'Jun', 7: 'Jul', 8: 'Aug', 9: 'Sep', 10: 'Oct', 11: 'Nov', 12: 'Dec' };
      //   this.myDateRangePickerOptions.dayLabels = {su: 'Sun', mo: 'Mon', tu: 'Tue', we: 'Wed', th: 'Thu', fr: 'Fri', sa: 'Sat'};
    }

    // Set metas
    this.metatagsService.updateMetatags('openhouse');
  }

  ngOnInit () {
  }

  onDateRangeChanged ({ dateRange }) {
    this.beginDate = dateRange.beginJsDate;
    this.endDate = dateRange.endJsDate;
  }

  onPageChange (number: number) {
    this.cPage = number;
  }
}
