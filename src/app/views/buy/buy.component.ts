import { Component, OnInit, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ActivatedRoute } from '@angular/router';

import { PropertiesFeaturedComponent } from '@/library/properties-featured/properties-featured.component';
import { InscriptionsService } from '@/services/v3/inscriptions/inscriptions.service';
import { MetatagsService } from '@/services/v3/metatags/metatags.service';
import { OpenhouseService } from '@/services/v3/openhouse/openhouse.service';

@Component({
  selector: 'view-buy',
  templateUrl: './buy.component.html'
})

export class BuyComponent implements OnInit {
  @ViewChild(PropertiesFeaturedComponent)
  private propertiesFeaturedComponent: PropertiesFeaturedComponent;

  properties: any;
  propertiesOpenHouse: any;
  pdfGuide: any;
  category: any;

  property: any;
  propertyCollection: any;

  private fragment: string;

  constructor (
    private openHouseService: OpenhouseService,
    private inscriptionsService: InscriptionsService,
    private translate: TranslateService,
    private metatagsService: MetatagsService,
    private route: ActivatedRoute
  ) {
    this.metatagsService.updateMetatags('buy');
  }

  async ngOnInit () {
    this.getProperties();
    this.getOpenHousesProperties();
    this.getPropertyAmbiance();
    this.getPropertyCollection();

    this.pdfGuide = 'assets/images/common/buyers-guide-' + this.translate.currentLang + '.jpg';
    this.category = await this.translate.get('views.buy.blog-category').toPromise();

    this.route.fragment.subscribe(fragment => { this.fragment = fragment; });
  }

  ngAfterViewInit () {
    const f = document.getElementById(this.fragment);
    if (f) f.scrollIntoView({ behavior: 'smooth' });
  }

  getProperties () {
    this.inscriptionsService.getInscriptions(6, { featured: 1, sort: 'rand' }).subscribe(({ data }) => {
      this.properties = data;
      this.propertiesFeaturedComponent.properties = data;
      this.propertiesFeaturedComponent.initSlider();
    });
  }

  getOpenHousesProperties () {
    this.openHouseService.getOpenhouse('mls').subscribe(data => {
      this.propertiesOpenHouse = data;
    });
  }

  getPropertyAmbiance() {
    this.inscriptionsService.getInscriptions(1, { cover: 1 })
        .subscribe(result => {
          //If no result in cover Properties
          if (result.data.length) {
            this.property = result.data[0];
          }
          else{
            // Get the newest property
            this.inscriptionsService.getInscriptions(1, { cover: 0, sort: 'newest' })
                .subscribe(result => {
                  this.property = result.data[0];
                });
          }
        });
  }

  getPropertyCollection() {
    this.inscriptionsService.getInscriptions(100, { collection: 1 })
        .subscribe(({ data }) => {
          // Fetch first unsold property
          if (data.length) {
            this.propertyCollection = data[0];

            // Fallback to sold property if none
          } else {
            this.inscriptionsService.getInscriptions(1, { sold: 1 })
                .subscribe(({ data }) => {
                  this.propertyCollection = data[0];
                });
          }
        });
  }
}
