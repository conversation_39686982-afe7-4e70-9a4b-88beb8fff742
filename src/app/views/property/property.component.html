<lib-header></lib-header>
<lib-property-hero [property]="property" [photos]="photos"></lib-property-hero>

<lib-property-sheet 
  [property]="property" 
  [rooms]='rooms' 
  [brokers]='brokers' 
  [openHouses]='openHouses' 
  [characteristics]='characteristics' 
  [addenda]='addenda' 
  [expenses]="expenses" 
  [documentsList]="documentsList">
</lib-property-sheet>

<lib-property-statistics [property]="property"></lib-property-statistics>
<lib-property-map></lib-property-map>

<lib-properties-list-2 *ngIf="properties?.length > 0" [properties]="properties"></lib-properties-list-2>

<lib-property-navigation [previousNav]="previousNav" [nextNav]="nextNav"></lib-property-navigation>
<lib-footer></lib-footer>
