import { Component, OnInit, ViewChild } from '@angular/core';
import { Router, ActivatedRoute, NavigationEnd } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { SessionStorageService } from 'ngx-webstorage';

import { PropertyHeroComponent } from '@/library/property-hero/property-hero.component';
import { PropertyMapComponent } from '@/library/property-map/property-map.component';

import { InscriptionsService } from '@/services/v3/inscriptions/inscriptions.service';
import { MetatagsService } from '@/services/v3/metatags/metatags.service';
import { NeighborhoodsService } from '@/services/v3/neighborhoods/neighborhoods.service';
import { UtilsService } from '@/services/v3/utils/utils.service';

@Component({
  selector: 'view-property',
  templateUrl: './property.component.html'
})

export class PropertyComponent implements OnInit {
  @ViewChild(PropertyHeroComponent)
  private propertyHeroComponent: PropertyHeroComponent;

  @ViewChild(PropertyMapComponent)
  private propertyMapComponent: PropertyMapComponent;

  public property: any;
  public rooms: any;
  public brokers: any;
  public expenses: any;
  public openHouses: any;
  public characteristics: any;
  public addenda: any;
  public documentsList: any;
  public photos: any;
  public neighborhood: any;
  public neighborhoodId: any;
  public mapInfo: any;
  public notes: any;
  public navigationProperties: any;
  public previousNav: any;
  public nextNav: any;
  public mls: any;
  public properties: any;

  constructor (
    private session: SessionStorageService,
    private route: ActivatedRoute,
    private router: Router,
    private inscriptionsService: InscriptionsService,
    private translate: TranslateService,
    private metatagsService: MetatagsService,
    private neighborhoodService: NeighborhoodsService,
    private utils: UtilsService
  ) {
    this.mls = this.route.snapshot.paramMap.get('mls');
    this.router.routeReuseStrategy.shouldReuseRoute = function () { return false; };

    this.router.events.subscribe((evt) => {
      if (evt instanceof NavigationEnd) {
        // trick the Router into believing it's last link wasn't previously loaded
        this.router.navigated = false;
        // if you need to scroll back to top, here is the right place
        window.scrollTo(0, 0);
      }
    });
  }

  ngOnInit () {
    this.inscriptionsService.getInscription(this.mls).subscribe((data) => {
      if (!data.success) this.router.navigateByUrl(this.translate.instant('urls.home'));
      else {
        this.property = data.data;
        // console.log(this.property);

        this.addenda = this.property.addenda;

        if (this.property.inscriptions_extra) { this.documentsList = this.property.inscriptions_extra.documents_repeater; }

        setTimeout(() => this.propertyHeroComponent.initVideo(this.property), 200);

        // Set metas
        const title = this.property.ext_address + (this.property.neighborhood_label ? ', ' + this.property.neighborhood_label : '');
        const note = this.property.notes.length > 0 ? this.property.notes[0].note : '';
        const desc = note.slice(0, 150) + (note.length > 150 ? '...' : '');
        const image = this.property.ext_coverfull ? { image: this.property.ext_coverfull, width: 1920, height: 1280 } : {};
        this.metatagsService.updateMetatags({ title, desc, ...image });

        // Generate structured data
        this.utils.generateStructuredData({
          title,
          description: desc,
          image: this.property.ext_coverfull,
          price: this.property.price_sale
        });

        this.fetchNavigation();
        this.getProperties();
      }
    });

    this.neighborhoodService.getNeighborhoodByMLS(this.mls).subscribe(({ data }) => {
      this.neighborhood = data;
      this.propertyMapComponent.property = this.property;
      this.propertyMapComponent.neighborhood = data;
      this.propertyMapComponent.initMap();
    });

    this.inscriptionsService.getPhotos(this.mls).subscribe(data => { this.photos = data; });
    this.inscriptionsService.getRooms(this.mls).subscribe(data => { this.rooms = data; });
    this.inscriptionsService.getBrokers(this.mls).subscribe(({ data }) => { this.brokers = data; });
    this.inscriptionsService.getOpenHouses(this.mls).subscribe(({ data }) => { this.openHouses = data; });
    this.inscriptionsService.getCharacteristics(this.mls).subscribe(({ data }) => { this.characteristics = data; });
    this.inscriptionsService.getDocuments(this.mls).subscribe(({ data }) => { this.documentsList = data; });
  }

  // Fetch similar properties (price-wise)
  getProperties () {
    const search = {
      featured: false,
      sold: 0,
      sort: 'rand',
      pricemin: this.property.price_sale * 0.8,
      pricemax: this.property.price_sale
    };

    this.inscriptionsService.getInscriptions(7, search).subscribe(({ data }) => {
      this.properties = data
        .filter(p => p.mls !== this.property.mls)
        .slice(0, 6);
    });
  }

  // Fetch bottom next/prev properties
  async fetchNavigation () {
    const adjacentProperties = { prev: null, next: null };

    // Get previous/next properties MLS from session storage if it exists
    const sessionMLS = this.session.retrieve('navigationproperties');
    if (sessionMLS && sessionMLS.length) {
      const index = sessionMLS.indexOf(this.mls);
      const adjacentMls = {
        prev: sessionMLS[index - 1],
        next: sessionMLS[index + 1]
      };

      // ... then fetch them with API
      // TODO: Put both in single API call
      if (adjacentMls.prev) {
        adjacentProperties.prev = await this.inscriptionsService
          .getInscription(adjacentMls.prev)
          .toPromise()
          .then(({ data }) => data.length === undefined ? data : null)
          .catch(() => null);
      }

      if (adjacentMls.next) {
        adjacentProperties.next = await this.inscriptionsService
          .getInscription(adjacentMls.next)
          .toPromise()
          .then(({ data }) => data.length === undefined ? data : null)
          .catch(() => null);
      }

    // ... or fetch them via API if no session storage is active
    } else {
      await this.inscriptionsService
        .getPaginationByMls(this.mls)
        .toPromise()
        .then(({ data: { prev, next } }) => {
          adjacentProperties.prev = prev;
          adjacentProperties.next = next;
        })
        .catch(() => null);
    }

    // console.log(adjacentProperties);

    // Assign properties to navigation
    this.previousNav = adjacentProperties.prev;
    this.nextNav = adjacentProperties.next;
  }
}
