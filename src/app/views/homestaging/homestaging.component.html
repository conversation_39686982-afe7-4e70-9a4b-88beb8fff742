<lib-header></lib-header>
<lib-homestaging-sheet></lib-homestaging-sheet>
<div class="homestaging-card-ctn" *ngIf="homestagings?.length > 0">
	<div class="container">
		<h2 class="title">{{ 'views.homestaging.title' | translate }}</h2>
		<div class="homestaging">
			<div class="homestaging-ctn" *ngFor="let homestaging of homestagings; let i = index">
				<lib-homestaging-card [homestaging]='homestaging' [index]="i"></lib-homestaging-card>
			</div>
		</div>
		<div class="button-ctn">
			<h3 class="title">{{ 'views.homestaging.cta' | translate }}</h3>
			<a [routerLink]="['urls.contact' | translate ]" class="main-button -primary">{{ 'views.homestaging.button' | translate }}</a>
		</div>
	</div>
</div>
<lib-footer></lib-footer>
