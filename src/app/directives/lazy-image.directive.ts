import { Directive, ElementRef, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';

@Directive({
  selector: 'img'
})

export class LazyImageDirective implements OnInit, OnDestroy {
  private observer: IntersectionObserver;
  private img: HTMLImageElement;

  constructor ({ nativeElement }: ElementRef<HTMLImageElement>) {
    this.img = nativeElement;
  }

  ngOnInit() {
    const supports = 'loading' in HTMLImageElement.prototype;
    // Adding data-unlazy to an image prevents it from lazy-loading
    const lazy = !this.img.dataset.unlazy;

    // Check if image is above the fold (first 800px)
    const rect = this.img.getBoundingClientRect();
    const isAboveFold = rect.top < 800;

    if (supports && lazy && !isAboveFold) {
      // Use native lazy loading for images below the fold
      this.img.setAttribute('loading', 'lazy');
    } else if (!supports && lazy && !isAboveFold) {
      // Fallback to Intersection Observer for browsers without native lazy loading
      this.setupIntersectionObserver();
    } else if (isAboveFold) {
      // Preload images above the fold
      this.img.setAttribute('loading', 'eager');
      // Add high priority hint for critical images
      if (this.img.dataset.critical === 'true') {
        this.img.setAttribute('fetchpriority', 'high');
      }
    }
  }

  private setupIntersectionObserver() {
    if ('IntersectionObserver' in window) {
      this.observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            if (img.dataset.src) {
              img.src = img.dataset.src;
              img.removeAttribute('data-src');
            }
            this.observer.unobserve(img);
          }
        });
      }, {
        rootMargin: '50px 0px', // Start loading 50px before the image enters viewport
        threshold: 0.01
      });

      // Store original src in data-src and set placeholder
      if (this.img.src && !this.img.dataset.src) {
        this.img.dataset.src = this.img.src;
        this.img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMSIgaGVpZ2h0PSIxIiB2aWV3Qm94PSIwIDAgMSAxIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxIiBoZWlnaHQ9IjEiIGZpbGw9IiNGNUY1RjUiLz48L3N2Zz4=';
      }

      this.observer.observe(this.img);
    }
  }

  ngOnDestroy() {
    if (this.observer) {
      this.observer.disconnect();
    }
  }
}
