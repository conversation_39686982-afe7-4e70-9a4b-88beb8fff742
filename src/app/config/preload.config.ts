/**
 * Configuration for preloading strategies and resources
 */

export const PreloadConfig = {
  // Critical resources that should be preloaded immediately
  criticalResources: {
    fonts: [
      'assets/fonts/mem5YaGs126MiZpBA-UN7rgOUuhpKKSTjw.woff2', // Roboto Regular
      'assets/fonts/mem6YaGs126MiZpBA-UFUK0Xdc1GAK6bt6o.woff2', // Roboto Medium
      'assets/fonts/mem8YaGs126MiZpBA-UFVZ0bf8pkAg.woff2' // Roboto Bold
    ],
    images: [
      'assets/images/common/logo-equipe_bourdon.svg',
      'assets/images/common/logo-equipe_bourdon-blanc.svg',
      'assets/images/placeholder/propriete-nb.jpg',
      'assets/images/placeholder/propriete-nb-wide.jpg'
    ],
    icons: [
      'assets/images/SVG/icons/bed.svg',
      'assets/images/SVG/icons/sink.svg',
      'assets/images/SVG/UI/arrow-left.svg',
      'assets/images/SVG/UI/arrow-right.svg'
    ]
  },

  // Routes that should be preloaded with high priority
  highPriorityRoutes: [
    'fr', // Home page
    'en', // Home page English
    'fr/liste-proprietes-a-vendre', // Search properties
    'en/properties-for-sale', // Search properties English
    'fr/devenir-proprietaire', // Buy page
    'en/become-an-owner', // Buy page English
    'fr/strategie-de-vente', // Sell page
    'en/sales-strategy' // Sell page English
  ],

  // Routes that should be preloaded with medium priority (with delay)
  mediumPriorityRoutes: [
    'fr/nos-secteurs', // Neighborhoods
    'en/neighborhoods', // Neighborhoods English
    'fr/blogue-immobilier', // Blog
    'en/blog-real-estate-news', // Blog English
    'fr/nos-specialistes', // Specialists
    'en/our-specialists', // Specialists English
    'fr/courtiers-immobiliers', // Team
    'en/real-estate-agents' // Team English
  ],

  // API endpoints that should be preloaded
  criticalApiEndpoints: {
    homePageContent: true,
    featuredProperties: { limit: -1, params: { featured: 1, sold: 0, sort: 'rand' } },
    newProperties: { limit: -1, params: { sold: 0, sort: 'newest' } },
    openHouseProperties: { limit: -1, params: { featured: 0, flags: ['openhouse'] } },
    neighborhoods: { limit: 10 },
    blogPosts: { limit: 5 },
    testimonials: { limit: 5, random: 1 }
  },

  // Image preloading settings
  imagePreload: {
    maxConcurrentLoads: 3,
    propertyImageLimit: 6, // Number of property images to preload per list
    aboveFoldThreshold: 800, // Pixels from top to consider "above the fold"
    intersectionObserverMargin: '50px 0px' // Margin for intersection observer
  },

  // Cache settings
  cache: {
    maxImageCacheEntries: 100,
    maxApiCacheEntries: 50,
    apiCacheTimeout: 3000, // 3 seconds
    fontCacheEntries: 30
  },

  // Timing settings
  timing: {
    mediumPriorityDelay: 2000, // 2 seconds delay for medium priority routes
    apiPreloadDelay: 500, // 500ms delay before starting API preload
    imagePreloadDelay: 1000 // 1 second delay before starting image preload
  },

  // Feature flags
  features: {
    enableRoutePreloading: true,
    enableImagePreloading: true,
    enableApiPreloading: true,
    enableServiceWorkerCache: true,
    enableIntersectionObserver: true,
    enableNativeLazyLoading: true
  }
};

/**
 * Get all critical resources as a flat array
 */
export function getAllCriticalResources(): string[] {
  return [
    ...PreloadConfig.criticalResources.fonts,
    ...PreloadConfig.criticalResources.images,
    ...PreloadConfig.criticalResources.icons
  ];
}

/**
 * Check if a route should be preloaded with high priority
 */
export function isHighPriorityRoute(path: string): boolean {
  if (!path) return false;
  
  return PreloadConfig.highPriorityRoutes.some(route => {
    return path === route || path.startsWith(route + '/');
  });
}

/**
 * Check if a route should be preloaded with medium priority
 */
export function isMediumPriorityRoute(path: string): boolean {
  if (!path) return false;
  
  return PreloadConfig.mediumPriorityRoutes.some(route => {
    return path === route || path.startsWith(route + '/');
  });
}

/**
 * Get preload configuration for environment
 */
export function getPreloadConfigForEnvironment(production: boolean) {
  return {
    ...PreloadConfig,
    // In development, we might want to disable some features for faster builds
    features: {
      ...PreloadConfig.features,
      enableServiceWorkerCache: production,
      enableRoutePreloading: production
    }
  };
}
