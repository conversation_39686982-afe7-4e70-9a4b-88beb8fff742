import { Component, OnInit, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';
import { PreloadService } from '../../services/v3/preload/preload.service';
import { ImagePreloadService } from '../../services/v3/preload/image-preload.service';

@Component({
  selector: 'lib-preload-status',
  template: `
    <div class="preload-status" *ngIf="showStatus">
      <div class="preload-indicator" [class.loading]="isPreloading">
        <div class="spinner" *ngIf="isPreloading"></div>
        <span class="status-text">
          {{ isPreloading ? 'Chargement des ressources...' : 'Ressources chargées' }}
        </span>
      </div>
      
      <div class="preload-details" *ngIf="showDetails">
        <div class="data-status">
          <h4>Données préchargées:</h4>
          <ul>
            <li [class.loaded]="preloadedData.homePageContent">Contenu de la page d'accueil</li>
            <li [class.loaded]="preloadedData.featuredProperties">Propriétés vedettes</li>
            <li [class.loaded]="preloadedData.newProperties">Nouvelles propriétés</li>
            <li [class.loaded]="preloadedData.openHouseProperties">Visites libres</li>
            <li [class.loaded]="preloadedData.neighborhoods">Secteurs</li>
            <li [class.loaded]="preloadedData.blogPosts">Articles de blog</li>
            <li [class.loaded]="preloadedData.testimonials">Témoignages</li>
          </ul>
        </div>
        
        <div class="image-status">
          <h4>Images préchargées: {{ loadedImagesCount }} / {{ totalImagesCount }}</h4>
          <div class="progress-bar">
            <div class="progress" [style.width.%]="imageLoadProgress"></div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .preload-status {
      position: fixed;
      top: 10px;
      right: 10px;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 10px;
      border-radius: 5px;
      font-size: 12px;
      z-index: 9999;
      max-width: 300px;
    }
    
    .preload-indicator {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .spinner {
      width: 16px;
      height: 16px;
      border: 2px solid #ffffff33;
      border-top: 2px solid #ffffff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .preload-details {
      margin-top: 10px;
      padding-top: 10px;
      border-top: 1px solid #ffffff33;
    }
    
    .preload-details h4 {
      margin: 0 0 5px 0;
      font-size: 11px;
      color: #ccc;
    }
    
    .preload-details ul {
      margin: 0;
      padding: 0;
      list-style: none;
    }
    
    .preload-details li {
      font-size: 10px;
      padding: 2px 0;
      color: #999;
    }
    
    .preload-details li.loaded {
      color: #4CAF50;
    }
    
    .preload-details li.loaded:before {
      content: '✓ ';
      color: #4CAF50;
    }
    
    .preload-details li:not(.loaded):before {
      content: '○ ';
      color: #999;
    }
    
    .progress-bar {
      width: 100%;
      height: 4px;
      background: #333;
      border-radius: 2px;
      overflow: hidden;
      margin-top: 5px;
    }
    
    .progress {
      height: 100%;
      background: #4CAF50;
      transition: width 0.3s ease;
    }
    
    .image-status {
      margin-top: 10px;
    }
  `]
})
export class PreloadStatusComponent implements OnInit, OnDestroy {
  isPreloading = false;
  preloadComplete = false;
  preloadedData: any = {};
  loadedImagesCount = 0;
  totalImagesCount = 0;
  imageLoadProgress = 0;
  
  showStatus = false; // Set to true in development to show status
  showDetails = false;
  
  private subscriptions: Subscription[] = [];

  constructor(
    private preloadService: PreloadService,
    private imagePreloadService: ImagePreloadService
  ) {
    // Only show in development mode
    this.showStatus = !environment.production;
  }

  ngOnInit() {
    // Subscribe to preloading status
    this.subscriptions.push(
      this.preloadService.isPreloadingData().subscribe(isLoading => {
        this.isPreloading = isLoading;
      })
    );

    // Subscribe to preload completion
    this.subscriptions.push(
      this.preloadService.isPreloadComplete().subscribe(complete => {
        this.preloadComplete = complete;
      })
    );

    // Subscribe to preloaded data
    this.subscriptions.push(
      this.preloadService.getPreloadedData().subscribe(data => {
        this.preloadedData = data;
      })
    );

    // Subscribe to image preload status
    this.subscriptions.push(
      this.imagePreloadService.getPreloadStatus().subscribe(imageStatus => {
        this.updateImageProgress(imageStatus);
      })
    );
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private updateImageProgress(imageStatus: Map<string, any>) {
    this.totalImagesCount = imageStatus.size;
    this.loadedImagesCount = Array.from(imageStatus.values()).filter(status => status.loaded).length;
    this.imageLoadProgress = this.totalImagesCount > 0 ? (this.loadedImagesCount / this.totalImagesCount) * 100 : 0;
  }

  toggleDetails() {
    this.showDetails = !this.showDetails;
  }
}

// Note: You'll need to import environment
declare const environment: any;
