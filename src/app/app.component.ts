import { Component, OnInit  } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { PreloadService } from './services/v3/preload/preload.service';
import { AppInitializationService } from './services/v3/initialization/app-initialization.service';
import { PreloadDebugService } from './services/v3/preload/preload-debug.service';

declare var Cookiebot: any;

@Component({
  selector: 'e-closion',
  templateUrl: './app.component.html'
})

export class AppComponent implements OnInit {
  title = 'app';
  html;

  constructor (
    public translate: TranslateService,
    private router: Router,
    private preloadService: PreloadService,
    private appInitialization: AppInitializationService,
    private debugService: PreloadDebugService
  ) {
    this.html = document.querySelector('html');

    const defaultLang = 'fr';
    const urlLang = window.location.pathname.split('/')[1];
    const userLang = ['en', 'fr'].includes(urlLang) ? urlLang : defaultLang;

    translate.setDefaultLang(defaultLang);
    translate.use(userLang);

    router.events.subscribe(event => {
      // route change event
      if (event instanceof NavigationEnd) {
        if (event.url.indexOf('#') === -1) window.scrollTo(0, 0);
        document.querySelector('body').classList.remove('-open-menu');
        this.html.classList.remove('-no-scroll');

        // #############################################
        // ####### A CHANGER AVANT MISE EN LIGNE #######
        // #############################################

        // Google analytics -> force send new page
        // GA 3
        if((<any>window).ga) {
          (<any>window).ga('set', 'page', event.urlAfterRedirects);
          (<any>window).ga('send', 'pageview');
        }
        // GA 4
        if ((<any>window).gtag) {
          (<any>window).gtag('set', 'page', event.urlAfterRedirects);
          (<any>window).gtag('send', 'pageview');
        }
      }
    });
  }

  ngOnInit() {
		this.cookiesConsentEvents();

		// Initialiser l'application puis démarrer le preload
		this.appInitialization.initialize().subscribe(() => {
			this.startPreload();
		});
	}

	private startPreload() {
		// Diagnostic avant de démarrer le preload
		console.log('Starting preload with language:', this.translate.currentLang);

		// Test all services first
		this.debugService.testAllServices();

		// Start preloading critical data
		this.preloadService.preloadCriticalData().subscribe({
			next: (data) => {
				console.log('Critical data preloaded successfully:', Object.keys(data));
			},
			error: (error) => {
				console.error('Error preloading critical data:', error);
				// Run diagnostics to help debug the issue
				this.preloadService.diagnoseServices();
				this.debugService.testSingleServiceCall();
			}
		});
	}
	
	/**
	 * Set up event listeners for Cookiebot's "accept" and "decline" events.
	 * When users accept or decline cookies, reloads the page.
	 */
	cookiesConsentEvents() {
		if (Cookiebot) {

			window.addEventListener('CookiebotOnAccept', () => {
				if (Cookiebot.changed === true) {
					location.reload();
				}
			}, false);

			window.addEventListener('CookiebotOnDecline', () => {
				if (Cookiebot.changed === true) {
					location.reload();
				}
			}, false);
		}
  	}
}