import { Component, OnInit, Input, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import Swiper, { Navigation } from 'swiper';

@Component({
  selector: 'lib-properties-slider',
  templateUrl: './properties-slider.component.html'
})

export class PropertiesSliderComponent implements OnInit, AfterViewInit {
  @Input() properties;
  @Input() title;
  @Input() button;
  @Input() class;

  @ViewChild('swiperContainer', { static: false }) swiperContainer: ElementRef;
  @ViewChild('nextButton', { static: false }) nextButton: ElementRef;
  @ViewChild('prevButton', { static: false }) prevButton: ElementRef;

  propertiesSwiper: any;

  constructor () { }

  ngOnInit () {
    Swiper.use([Navigation]);
  }

  ngAfterViewInit() {
    this.initSlider();
  }

  initSlider () {
    setTimeout(() => {
      this.propertiesSwiper = new Swiper(this.swiperContainer.nativeElement, {
        speed: 1000,
        loop: true,
        observer: true,
        navigation: {
          nextEl: this.nextButton.nativeElement,
          prevEl: this.prevButton.nativeElement
        },
        slidesPerView: 1,
        spaceBetween: 0,
        breakpoints: {
          1024: {
            slidesPerView: 3,
            spaceBetween: 20
          },
          768: {
            slidesPerView: 2,
            spaceBetween: 10
          }
        }
      });
    }, 2500);
  }
}
