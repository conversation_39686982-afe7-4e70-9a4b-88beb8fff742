<div class="properties-slider-cpn {{ class }}" *ngIf="properties?.length > 0">

    <div class="header">
        <h2>{{ title }}</h2>
        <div class="right">
            <a [routerLink]="'urls.search-properties' | translate" class="small-link right">
                {{ button }} <i class="icon-arrow-right"></i>
            </a>

            <div class="nav-ctn">
                <a class="main-button prev" #prevButton><i class="icon-arrow-left"></i></a>
                <a class="main-button next" #nextButton><i class="icon-arrow-right"></i></a>
            </div>
        </div>
    </div>

    <div class="swiper-container properties-list-ctn" #swiperContainer>
        <div class="swiper-wrapper properties-list-inner">

            <div class="swiper-slide properties" *ngFor="let property of properties;">
                <lib-properties [property]="property"></lib-properties>
            </div>

        </div>
    </div>
</div>
