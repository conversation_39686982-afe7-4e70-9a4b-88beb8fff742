<!--<div class="blog-details-page" *ngIf="post" itemscope itemtype="http://schema.org/Article">

	<div class="main-img-ctn container" *ngIf="post.coverphoto">
		<img itemprop="image" src="{{post.coverphoto}}" alt="{{post.title}}" id="imgTop">
	</div>

	<div class="container -smaller">

		<div class="backTop">
			<a (click)="backTop()" class="icon-arrow_upward"></a>
		</div>

		<div class="-page-head">
			<h1 itemprop="name" *ngIf="post.title" class="page-title">{{post.title}}</h1>

			<div *ngIf="post.abstract">
				<p class="resume" [innerHtml]="post.abstract"></p>
			</div>

			<div class="details-info">
				<p class="categories">
					<span *ngIf="post.category">{{post.category_title}}</span>
					<span *ngFor="let tag of post.tags">, {{tag.name}}</span>
				</p>
				<p class="date" itemprop="datePublished" *ngIf="post.publication_date" >{{post.publication_date | localizedDate:'longDate'}}</p>
			</div>
		</div>
		<div class="article-share-cpn share-social-cpn no-print -desktop">
			<div>
				<a class="share-print sb-button" (click)="onPrint()"><i class="icon-print"></i></a>
				<button mat-fab shareButton="email" [url]="currentUrl"  class="share-btn">
				</button>
				<button mat-fab shareButton="facebook" [url]="currentUrl"  class="share-btn">
				</button>
				<button mat-fab shareButton="twitter"  [url]="currentUrl" class="share-btn">
				</button>
			</div>
		</div>

		<div class="main-text-ctn laraberg" [innerHtml]="contentSanitized"></div>

		&lt;!&ndash; <div *ngIf="iframeUrl" class="iframe-container .col-xs-12" >
			<iframe allowfullscreen allow="fullscreen" style="border:none;width:100%;height:350px;" class="e2e-iframe-trusted-src" [src]="iframeUrl"></iframe>
		</div> &ndash;&gt;

		<div class="article-share-cpn share-social-cpn no-print -mobile">
			<div>
				<a class="share-print sb-button" (click)="onPrint()"><i class="icon-print"></i></a>
				<button mat-fab shareButton="email" [url]="currentUrl"  class="share-btn">
				</button>
				<button mat-fab shareButton="facebook" [url]="currentUrl"  class="share-btn">
				</button>
				<button mat-fab shareButton="twitter"  [url]="currentUrl" class="share-btn">
				</button>
			</div>
		</div>


		<div class="contact-cta no-print">
			<div class="text-ctn">
				<h4 class="title">{{ 'library.blog-details.contact-cta.title' | translate }}</h4>
				<p itemprop="articleBody" class="description">{{ 'library.blog-details.contact-cta.description' | translate }}</p>
			</div>

			<div class="button-ctn">
				<a [routerLink]="['urls.contact' | translate ]" class="main-button -ghost">{{ 'library.blog-details.contact-cta.button' | translate }}</a>
			</div>
		</div>
	</div>

  <lib-centered-cta class="no-print" [url]="['urls.real-estate-blog' | translate ]" [label]="'library.blog-details.back' | translate"></lib-centered-cta>
</div>-->

<div class="blog-details-page" *ngIf="post" itemscope itemtype="http://schema.org/Article">
    <div class="container -page-head">
        <h1 itemprop="name" *ngIf="post.title" class="page-title">{{post.title}}</h1>
        <div class="details-info">
            <p class="date" itemprop="datePublished" *ngIf="post.publication_date" >{{post.publication_date | localizedDate:'longDate'}}</p>
            <p class="categories">
                <span *ngIf="post.category">{{post.category_title}}</span><!--
       --><span *ngFor="let tag of post.tags">, {{tag.name}}</span>
            </p>
        </div>
    </div>
    <div class="main-img-ctn container" *ngIf="post.coverphoto">
        <img itemprop="image" src="{{post.coverphoto}}" alt="{{post.title}}">
    </div>
    <div class="container -smaller">
        <div class="main-text-ctn" [innerHtml]="post.content"></div>
        <div class="article-share-cpn share-social-cpn no-print -mobile">
            <div>
                <a class="share-print sb-button" (click)="onPrint()"><i class="icon-print"></i></a>
                <button mat-fab shareButton="email" [url]="currentUrl"  class="share-btn">
                </button>
                <button mat-fab shareButton="facebook" [url]="currentUrl"  class="share-btn">
                </button>
                <button mat-fab shareButton="twitter"  [url]="currentUrl" class="share-btn">
                </button>
            </div>
            <a [routerLink]="['urls.real-estate-blog' | translate ]" class="small-link right">{{ 'library.blog-details.all-articles' | translate }}<i class="icon-arrow-right"></i></a>
        </div>
    </div>
</div>
