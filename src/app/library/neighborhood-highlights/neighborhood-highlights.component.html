<div class="neighborhood-highlights-cpn container" *ngIf="neighborhood" itemscope itemtype="http://schema.org/Place">
    <div class="neighborhood-description col-lg-7">
        <h2 itemprop="name" class="title" *ngIf="neighborhood.name">{{ 'library.neighborhood-interest.welcome' | translate}} {{neighborhood.name}}</h2>
        <div itemprop="description" class="main-inscription" [innerHtml]="neighborhood.description"></div>

        <div class="blocks-ctn">
            <div class="block" *ngFor="let block of neighborhood.blocks">
                <div class="icon-ctn" *ngIf="block.icon">
                    <i class="icon-{{block.icon}}"></i>
                </div>
                <div class="text-ctn">
                    <h4 class="title">{{block.title}}</h4>
                    <div class="description" [innerHtml]="block.text"></div>
                </div>
            </div>
        </div>


    </div>
    <div class="col-lg-4 col-lg-offset-1">
        <div *ngIf="neighborhood.points_of_interest" class="interest-ctn">
            <h3 class="emphase-title">{{'library.neighborhood-interest.title' | translate}}</h3>
            <ul class="small-list">
                <ng-container *ngFor="let interest of neighborhood.points_of_interest">
                    <li *ngIf="interest.name">{{interest.name}}</li>
                </ng-container>
            </ul>
        </div>
    </div>
</div>
