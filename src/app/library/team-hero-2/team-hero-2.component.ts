import { Component, OnInit } from '@angular/core';
import { BlocksService } from '@/services/v3/contentblocks/contentblocks.service';
import { PreloadService } from '@/services/v3/preload/preload.service';

@Component({
  selector: 'lib-team-hero-2',
  templateUrl: './team-hero-2.component.html'
})

export class TeamHero2Component implements OnInit {
  public blockTitle: string;
  public blockContent: string;

  constructor (
    private blocksService: BlocksService,
    private preloadService: PreloadService
  ) {
    // Defer data loading to ngOnInit to ensure all services are ready
  }

  ngOnInit () {
    // Use preloaded data if available, otherwise fallback to direct service call
    const teamBlock$ = this.preloadService.getTeamBlock();
    if (teamBlock$) {
      teamBlock$.subscribe({
        next: (data) => {
          this.blockTitle = data.title;
          this.blockContent = data.text;
        },
        error: (error) => {
          console.warn('Error loading team block from preload, using direct service:', error);
          this.blocksService.getBlock('bloc-equipe').subscribe(data => {
            this.blockTitle = data.title;
            this.blockContent = data.text;
          });
        }
      });
    } else {
      // Fallback to direct service call
      console.warn('PreloadService not ready, using direct service call for team block');
      this.blocksService.getBlock('bloc-equipe').subscribe(data => {
        this.blockTitle = data.title;
        this.blockContent = data.text;
      });
    }
  }

  onReadMore ($event: Event) {
    $event.preventDefault();
    const parentElement = ($event.currentTarget as HTMLElement).parentNode as HTMLElement;
    if (parentElement) {
      parentElement.classList.add('-opened');
    }
  }
}
