<lib-header-panel></lib-header-panel>

<header id="header" class="landing-header-cpn">
  <div class="logo-ctn"itemscope itemtype="http://schema.org/Organization">
    <a itemprop="url" [routerLink]="['urls.home' | translate]"><img src="assets/images/common/main-logo.svg" alt="{{ 'client.name' | translate }} {{ 'library.header.alt-image' | translate }}"></a>
  </div>

  <nav>
    <div *ngIf="showSwitchLang" class="language-switcher">
      <a class="switch" (click)="translateCampaign()">{{ "global.switchlang" | translate }}</a>
      <a class="english-fake" href="{{ translateUrl }}"></a>
    </div>

    <!-- <div class="share-ctn">
      <a href="{{ 'client.facebook' | translate }}" class="icon-logo-facebook" target="_blank"></a>
      <a href="{{ 'client.twitter' | translate }}" class="icon-x" target="_blank"></a>
      <a href="{{ 'client.instagram' | translate }}" class="icon-logo-instagram" target="_blank"></a>
      <a href="{{ 'client.linkedin' | translate }}" class="icon-logo-linkedin" target="_blank"></a>
      <a href="{{ 'client.youtube' | translate }}" class="icon-logo-youtube" target="_blank"></a>
      <a href="mailto:{{ 'client.email' | translate }}" class="icon-logo-mail"></a>
    </div> -->

    <!-- <div class="number-ctn" itemscope itemtype="http://schema.org/ContactPoint">
      <a itemprop="telephone" href="tel:{{ 'client.phone' | translate }}"><i class="icon-phone"></i>{{ 'client.phone' | translate }}</a>
    </div> -->

    <a itemprop="url" [routerLink]="['urls.home' | translate]" class="main-button -primary">{{ 'views.landing.btn-visit' | translate }}</a>
  </nav>
</header>
