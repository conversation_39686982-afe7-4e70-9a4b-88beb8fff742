<div class="search-simple-cpn">
	<div class="container">

		<div class="tabs-ctn col-sm-8">
			<input class="input-tab" id="tab1" type="radio" name="tabs" checked>
      <label for="tab1">{{ 'library.search-simple.tab1' | translate }}</label>

			<input class="input-tab" id="tab2" type="radio" name="tabs">
      <label for="tab2">{{ 'library.search-simple.tab2' | translate }}</label>

      <input class="input-tab" id="tab3" type="radio" name="tabs">
      <label for="tab3">{{ 'library.search-simple.tab3' | translate }}</label>

			<a class="special-search" [routerLink]="['urls.search-properties' | translate ]">{{ 'library.search-simple.advanced-search' | translate }}<i class="icon-arrow-externe"></i></a>
			
      <div id="acheter" class="tab-content">
				<form (ngSubmit)="onSubmitBuy()">
					<div class="input-ctn">
						<ng-select
              [class]="'large'"
							[searchable]="true"
							[items]="municipalites"
							bindLabel="label"
							bindValue="label"
							[(ngModel)]="search.city"
							[ngModelOptions]="{standalone: true}"
							placeholder="{{ 'library.search-simple.city-neighborhood' | translate }}" class="city-filter align-center">
						</ng-select>
						<button class="main-button -primary-small" type="submit"><span>{{ 'library.search-simple.search' | translate }}</span></button>
					</div>
				</form>
			</div>

			<div id="vendre" class="tab-content">
				<form (ngSubmit)="onSubmitSell()">
					<div class="input-ctn">
						<input type="search"
              class="large"
					 		id="address-input-2"
							placeholder="{{ 'library.search-simple.city-or-address' | translate }}"
							[(ngModel)]="addressText"
							[ngModelOptions]="{ standalone: true }" />
						<button class="main-button -primary-small" type="submit"><span>{{ 'library.search-simple.evaluate' | translate }}</span></button>
					</div>
				</form>
			</div>

			<div id="centris" class="tab-content">
				<form (ngSubmit)="onSubmitCentris()">
					<div class="input-ctn">
						<ng-select
              [class]="'large'"
							[searchable]="true"
							[items]="mls"
							bindLabel="label"
							bindValue="value"
							[(ngModel)]="search.mls"
							[ngModelOptions]="{standalone: true}"
							placeholder="{{ 'library.search-simple.search-centris' | translate }}" class="city-filter">
						</ng-select>
						<button class="main-button -primary-small" type="submit"><span>{{ 'library.search-simple.search' | translate }}</span></button>
					</div>
				</form>
			</div>
		</div>

	</div>
</div>
