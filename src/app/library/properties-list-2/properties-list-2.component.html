<div *ngIf="properties" class="properties-list-cpn">
	<div class="container">
		<div class="cpn-head">
			<h2 class="title">{{ "library.properties-list-2.title" | translate }}</h2>
			<a [routerLink]="['urls.search-properties' | translate ]" class="small-link right hide-mobile">{{ "library.properties-list-2.all" | translate }}<i class="icon-arrow-right"></i></a>
		</div>
		<div class="properties-list-ctn">
			<div class="properties" *ngFor="let property of properties">
				<lib-properties [property]="property"></lib-properties>
			</div>
		</div>

		<a [routerLink]="['urls.search-properties' | translate ]" class="small-link right show-mobile">{{ "library.properties-list-2.all" | translate }}<i class="icon-arrow-right"></i></a>
	</div>
</div>
