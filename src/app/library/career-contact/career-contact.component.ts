import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormControl, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';

import { Subscription } from 'rxjs';
import { IAngularMyDpOptions } from 'angular-mydatepicker';
import { FileUploader } from 'ng2-file-upload';
import { ReCaptchaV3Service } from 'ng-recaptcha';

import { BlocksService } from '@/services/v3/contentblocks/contentblocks.service';
import { CareerService } from '@/services/v3/career/career.service';
import { environment } from 'environments/environment';

const URL = 'path_to_api';

@Component({
  selector: 'lib-career-contact',
  templateUrl: './career-contact.component.html'
})

export class CareerContactComponent implements OnInit {
  public currentStep: number = 1;
  public uploader: FileUploader = new FileUploader({ url: URL });

  roles: any[];

  careerForm: FormGroup;
  firstName: FormControl;
  lastName: FormControl;
  phone: FormControl;
  phoneExt: FormControl;
  email: FormControl;
  address: FormControl;
  city: FormControl;
  mydate: FormControl;
  role: FormControl;
  message: FormControl;
  fileInput: FormControl;
  subject: any;
  successMessage: boolean = false;
  errorMessage: boolean = false;
  formSend: boolean = false;
  formLoading: boolean = false;

  isTyping = false;
  file: any = {};

  myDatePickerOptions: IAngularMyDpOptions;

  public blockTitle: string;
  public blockContent: string;

  // Recaptcha v3 Subscription
  private submitExecutionSubscription: Subscription

  constructor (
    private recaptchaV3Service: ReCaptchaV3Service,
    private blocksService: BlocksService,
    private formBuilder: FormBuilder,
    private careerService: CareerService,
    public translateService: TranslateService
  ) {
    this.translateService.get('library.career-contact.step1.roles').subscribe(res => { this.roles = res; });

    const today: Date = new Date();
    this.myDatePickerOptions = {
      dateFormat: 'dd mmm yyyy',
      disableUntil: { year: today.getUTCFullYear(), month: today.getUTCMonth() + 1, day: today.getUTCDate() - 1 }
    };

    this.blocksService.getBlock('bloc-carriere').subscribe(data => {
      this.blockTitle = data.title;
      this.blockContent = data.text;
    });
  }

  ngOnInit () {
    this.createFormControls();
    this.createForm();
    this.delayRCDisplay();
  }

  delayRCDisplay () {
    setTimeout(() => {
      const result = document.getElementsByClassName('grecaptcha-badge');
      const recaptcha = result.item(0) as HTMLElement;
      recaptcha.style.visibility = 'visible';
    }, 2000);
  }

  ngOnDestroy () {
    const result = document.getElementsByClassName('grecaptcha-badge');
    const recaptcha = result.item(0) as HTMLElement;
    recaptcha.style.visibility = 'hidden';
    if (this.submitExecutionSubscription) { this.submitExecutionSubscription.unsubscribe(); }
  }

  onPreviousStep ($event) {
    $event.preventDefault();
    this.currentStep--;
    setTimeout(() => document.getElementById('careerForm').scrollIntoView({ behavior: 'smooth' }), 100);
  }

  onNextStep ($event) {
    $event.preventDefault();
    this.currentStep++;
    setTimeout(() => document.getElementById('careerForm').scrollIntoView({ behavior: 'smooth' }), 100);
  }

  private createFormControls () {
    this.subject = new FormControl('');
    this.firstName = new FormControl('', Validators.required);
    this.lastName = new FormControl('', Validators.required);
    this.phone = new FormControl('', [
      Validators.required,
      Validators.pattern('[0-9]+'),
      Validators.minLength(10)
    ]);
    this.email = new FormControl('', [
      Validators.required,
      Validators.pattern('^\\w+([\\.-]?\\w+)*@\\w+([\\.-]?\\w+)*(\\.\\w{2,3})+$')
    ]);
    this.phoneExt = new FormControl('');
    this.address = new FormControl('');
    this.city = new FormControl('');
    this.mydate = new FormControl({ isRange: false }, Validators.required);
    this.role = new FormControl(undefined, Validators.required);
    this.message = new FormControl('', Validators.required);
    this.fileInput = new FormControl('', Validators.required);
  }

  private createForm () {
    this.translateService.get('library.career-contact.subject').subscribe(res => { this.subject = res; });
    this.careerForm = this.formBuilder.group({
      firstName: this.firstName,
      lastName: this.lastName,
      phone: this.phone,
      phoneExt: this.phoneExt,
      email: this.email,
      mydate: this.mydate,
      address: this.address,
      city: this.city,
      role: this.role,
      message: this.message,
      file: this.file,
      subject: this.subject,
      fileInput: this.fileInput
    });
  }

  onSubmit () {
    if (!this.careerForm.valid) return false;
    this.formLoading = true;

    this.submitExecutionSubscription = this.recaptchaV3Service.execute('submit').subscribe(token => {
      this.careerForm.value.subject = this.subject;
      this.careerForm.value.file = this.file;
      this.careerForm.value.token_captcha = token;

      // Format date
      const { formatted } = this.careerForm.value.mydate.singleDate;
      this.careerForm.value.mydate.formatted = formatted;

      if(environment.webHookZapier){
        // Send Data to Zapier
        fetch(environment.webHookZapier, {
          method: 'POST',
          body: JSON.stringify(this.careerForm.value),
        }).catch(err => console.error('Erreur Zapier :', err));
      }

      this.careerService.postCareer(this.careerForm.value).subscribe(response => {
        this.formSend = true;
        this.formLoading = false;

        if (response.success === true) this.successMessage = true;
        else this.errorMessage = true;
      });
    },
    error => console.error(error));
  }

  onFileChange (event) {
    const reader = new FileReader();

    this.careerForm.controls.fileInput.setErrors(null);
    if (event.target.files && event.target.files.length > 0) {
      const upload = event.target.files[0];
      reader.readAsDataURL(upload);
      reader.onload = () => {
        document.getElementById('label-file-upload').innerText = upload.name;
        this.file.filename = upload.name;
        this.file.filetype = upload.type;
        this.file.file = reader.result;
      };
    }
  }

  resetForm () {
    this.formSend = false;
    this.careerForm.reset();
    this.translateService.get('library.career-contact.step1.upload').subscribe(res => {
      document.getElementById('label-file-upload').innerText = res;
    });

    this.file = '';
    this.currentStep = 1;
  }

  retry () {
    this.formSend = false;
  }
}
