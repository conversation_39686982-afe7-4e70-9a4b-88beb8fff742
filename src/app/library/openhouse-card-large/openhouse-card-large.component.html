<div class="openhouse-card-large-cpn">
	<div class="img-ctn col-sm-5 col-md-4">
		<img src="{{property.ext_coverphoto}}" alt="{{'library.properties.alt-image' | translate}}">
	</div>
	<div class="content col-sm-7 col-md-8">
		<div class="bloc-head">
			<ng-container *ngIf="!property.status">
				<p class="price" *ngIf="property.price_sale != false">
					{{ property.price_sale | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang}}</p>

				<p class="price" *ngIf="property.price_rental && property.price_sale == false">
					<ng-container *ngIf="property.property_category != 'Commercial'">
						<span
							class="c -inner">{{ property.price_rental | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang}}
							{{ 'library.property-hero.per-month' | translate }}</span>
					</ng-container>

					<ng-container *ngIf="property.property_category == 'Commercial'">
						<ng-container *ngIf="property.rental_period == 'A'">
							<span
								class="c a -inner">{{ property.price_rental | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang}}
								{{ 'library.property-hero.commercial-year' | translate }}</span>
						</ng-container>

						<ng-container *ngIf="property.rental_period != 'A'">
							<span
								class="c m -inner">{{ property.price_rental | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang}}
								{{ 'library.property-hero.commercial-month' | translate }}</span>
						</ng-container>
					</ng-container>
				</p>
			</ng-container>
			<p class="price" *ngIf="property.status">{{property.property_type}}</p>

			<div class="numbers">
				<div class="icon-ctn" *ngIf="property.rooms_bedroom_total_number">
					<i class="icon-bed"></i>
					<p itemprop="numberOfRooms">{{property.rooms_bedroom_total_number}}</p>
				</div>
				<div class="icon-ctn" *ngIf="property.rooms_bathroom_number">
					<i class="icon-shower"></i>
					<p itemprop="numberOfRooms">{{property.rooms_bathroom_number}}</p>
				</div>
			</div>
		</div>

		<p class="location" itemprop="addressLocality">{{property.municipality_label | shorten: 50: '...'}}</p>

		<ul class="dates-list">
			<li class="date" *ngFor="let item of property.openhouses">
				<span>{{item.start_date | localizedDate:'longDate'}}</span>
			</li>
		</ul>

		<div class="more-info" itemscope itemtype="http://schema.org/PostalAddress">
			<div class="inner">
				<p class="address" itemprop="address">
					<ng-container *ngIf="property.address_civic_start && property.address_civic_end">
						<span itemprop="streetAddress">{{property.address_civic_start}}
							-
							{{property.address_civic_end}},
						</span>
					</ng-container>
					<ng-container *ngIf="property.address_civic_start && !property.address_civic_end">
						<span itemprop="streetAddress">{{property.address_civic_start}},
						</span>
					</ng-container>
					<span *ngIf="property.address_street" itemprop="streetAddress">{{property.address_street}}</span>
					<span *ngIf="property.address_apt">
						{{ 'library.property-details.apt' | translate }}.
						{{ property.address_apt }}</span>
				</p>
				<p *ngIf="!property.status" class="type">{{property.property_type}}</p>
			</div>
			<a class="button" href="" [routerLink]="propertyUrl">
				<i class="icon-arrow-right"></i>
			</a>
		</div>
	</div>
</div>