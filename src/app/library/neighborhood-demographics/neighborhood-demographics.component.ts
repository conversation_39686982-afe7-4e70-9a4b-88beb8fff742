import { Component, OnInit, Input } from '@angular/core';

@Component({
  selector: 'lib-neighborhood-demographics',
  templateUrl: './neighborhood-demographics.component.html'
})

export class NeighborhoodDemographicsComponent implements OnInit {
  @Input() neighborhood;
  @Input() formatTitle: Function;

  public chartColors: any[] = [{ backgroundColor: ["#002758", "#004D9A", "#971A1A", "#1E2021", "#999999", "#eeeeee"] }];

  constructor () { }

  ngOnInit () {
  }

  public chartHovered (e: any): void {
    // console.log(e);
  }
}
