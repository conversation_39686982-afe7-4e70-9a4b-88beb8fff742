<div class="neighborhood-demographics-cpn" *ngIf="neighborhood">
	<div class="container">
		<h3 class="single-title">
		 	{{ formatTitle(neighborhood.name, 'library.neighborhood-demographics.title' | translate) }}
		</h3>

		<div class="chart-wrap">
			<div class="chart-ctn block">
				<div>
					<ng-container>
						<canvas baseChart
								[chartType]="'doughnut'"
								[data]="[neighborhood.perc_14, neighborhood.perc_15_24, neighborhood.perc_25_44, neighborhood.perc_45_64, neighborhood.perc_65]"
								[labels]="['library.neighborhood-demographics.age.label14less' | translate,
								'library.neighborhood-demographics.age.label1524' | translate,
								'library.neighborhood-demographics.age.label2544' | translate,
								'library.neighborhood-demographics.age.label4564' | translate,
								'library.neighborhood-demographics.age.label65more' | translate]"
								[colors]="chartColors"
								[legend]="true"
								(chartHover)="chartHovered($event)">   
						</canvas>
					</ng-container>

					<p class="label">{{'library.neighborhood-demographics.age.title' | translate}}</p>
				</div>
			</div>
			<div class="chart-ctn block">
				<div>
					<ng-container>
						<canvas baseChart
								[chartType]="'doughnut'"
								[data]="[neighborhood.perc_household_1, neighborhood.perc_household_2, neighborhood.perc_household_3, neighborhood.perc_household_4, neighborhood.perc_household_5]"
								[labels]="['library.neighborhood-demographics.groups.label1' | translate, 'library.neighborhood-demographics.groups.label2' | translate, 'library.neighborhood-demographics.groups.label3' | translate, 'library.neighborhood-demographics.groups.label4' | translate, 'library.neighborhood-demographics.groups.label5' | translate]"
								[colors]="chartColors"
								[legend]="true"
								(chartHover)="chartHovered($event)">
						</canvas>
					</ng-container>
					<p class="label">{{'library.neighborhood-demographics.groups.title' | translate}}</p>
				</div>
			
			</div>
			<div class="chart-ctn block">
				<div>
					<ng-container>
						<canvas baseChart
								[chartType]="'doughnut'"
								[data]="[neighborhood.perc_french, neighborhood.perc_english, neighborhood.perc_other]"
								[labels]="['library.neighborhood-demographics.lang.labelfrench' | translate, 'library.neighborhood-demographics.lang.labelenglish' | translate, 'library.neighborhood-demographics.lang.labelother' | translate]"
								[colors]="chartColors"
								[legend]="true"
								(chartHover)="chartHovered($event)">
						</canvas>
					</ng-container>
					<p class="label">{{'library.neighborhood-demographics.lang.title' | translate}}</p>
				</div>
			</div>
			<div class="stats-ctn block">
				<div class="stats-wrap">
					<div class="stats" *ngIf="neighborhood.area">
						<p class="name">{{'library.neighborhood-demographics.area' | translate}}</p>
						<p class="value">{{neighborhood.area}} <span>km<sup>2</sup></span></p>
					</div>
					<div class="stats" *ngIf="neighborhood.population">
						<p class="name">{{'library.neighborhood-demographics.number' | translate}}</p>
						<p class="value">{{neighborhood.population}}</p>
					</div>
					<div class="stats" *ngIf="neighborhood.density">
						<p class="name">{{'library.neighborhood-demographics.density' | translate}}</p>
						<p class="value">{{neighborhood.density}} <span>hab./km<sup>2</sup></span></p>
					</div>
				</div>
			</div>
		</div>
	</div>	
</div>
