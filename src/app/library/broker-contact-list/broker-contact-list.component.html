<div class="broker-contact-list-cpn">
    <div class="container">
        <h3 class="emphase-title">{{ 'library.broker-contact-1.real-estate-brokers' | translate }}</h3>
        <div class="broker-ctn">
            <lib-broker-card class="broker" *ngFor="let member of brokers" [member]="member" itemscope itemtype="http://schema.org/Person"></lib-broker-card>
        </div>
        <h3 class="emphase-title">Administration</h3>
        <div class="broker-ctn">
            <lib-broker-card class="broker" *ngFor="let member of administrators" [member]="member" itemscope itemtype="http://schema.org/Person"></lib-broker-card>
        </div>
        <div class="button-ctn -center">
            <a [routerLink]="['urls.real-estate-agents' | translate]" class="main-button -primary">{{ 'library.broker-contact-1.learn-more-about-the-team' | translate }}</a>
        </div>
    </div>
</div>
