import { Component, Inject, OnInit, Renderer2 } from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { TranslateService } from "@ngx-translate/core";
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from "@angular/forms";
import { DOCUMENT } from "@angular/common";

import { EvaluationService } from "@/services/v3/evaluation/evaluation.service";
import { BlocksService } from "@/services/v3/contentblocks/contentblocks.service";

import { Subscription } from "rxjs";
import { FileUploader } from "ng2-file-upload";
import { ReCaptchaV3Service } from "ng-recaptcha";

import { environment } from "@/../environments/environment";

@Component({
  selector: "lib-evaluation-form",
  templateUrl: "./evaluation-form.component.html",
})
export class EvaluationFormComponent implements OnInit {
  public currentStep: number = 1;

  // Map & streetview values
  public GoogleMaps: any;
  public currentLocation: any;
  public hasStreetView: boolean;
  private gmapUrl: string;

  // Form values
  public selectedPropertyType: any;
  public selectedPropertySurface: any;
  public selectedPropertyDimensions: any;
  public nbBedRooms: any;
  public nbBathRooms: any;
  public nbGarage: any;
  public nbBasement: any;
  public nbPool: any;
  public nbCarParks: any;
  public evaluateAgree: any;
  public fileInput: FormControl;
  public evaluateForm: FormGroup;
  public constructYear: FormControl;
  public reasonBuy: FormControl;
  public renovations: FormControl;
  public fPropertyDimensions: FormControl;
	public fPropertySurface: FormControl;
  public purpose: any;
	public fPurposeOther: FormControl;
	public moveDelay: any;
	public delayToSold: any;
  public firstName: FormControl;
  public lastName: FormControl;
  public phoneNumber: FormControl;
  public phoneExt: FormControl;
  public email: FormControl;

  public showInputGoal: Boolean = false;

  photos: any = [];
  file: any = {};
  textButton: any = "";
  public uploader: FileUploader = new FileUploader({ isHTML5: true });

  // Form helpers
  showForm: boolean = false;
  isTyping: boolean = false;
  formSend: boolean = false;
  formLoading: boolean = false;
  successMessage: boolean = false;
  errorMessage: boolean = false;
  mapsNoResult: boolean = false;

  // Array label selects
  propertySurfaces: any[] = [];
  propertyDimensions: any[] = [];
  propertyTypes: any[] = [
    { id: 'Maison à étages', name: 'Two or more storey' },
    { id: 'Maison à paliers multiples', name: 'Split-level' },
    { id: 'Maison à un étage et demi', name: 'One-and-a-half-storey house' },
    { id: 'Maison mobile', name: 'Mobile home' },
    { id: 'Maison de plain-pied', name: 'Bungalow' },
    { id: 'Loft/Studio', name: 'Loft / Studio' },
    { id: 'Duplex', name: 'Duplex' },
    { id: 'Triplex', name: 'Triplex' },
    { id: 'Quadruplex', name: 'Quadruplex' },
    { id: 'Quintuplex', name: 'Quintuplex' },
    { id: 'Appartement', name: 'Apartment' },
    { id: 'Chalet', name: 'Chalet' },
    { id: 'Fermette', name: 'Hobby Farm' },
    { id: 'Ferme', name: 'Farm' },
    { id: 'Terrain vacant', name: 'Vacant lot' },
    { id: 'Terre à  Bois', name: 'Wood lot' },
    { id: 'Propriété à revenu', name: 'Income property' },
    { id: 'Condo commercial', name: 'Commercial condo' },
    { id: 'Condo industriel', name: 'Industrial condo' },
    { id: 'Bâtisse commerciale/Bureau', name: 'Commercial building/Office' },
    { id: 'Bâtisse industrielle', name: 'Industrial building' },
    { id: 'Bâtisse et terrain commerciaux', name: 'Commercial building and land' },
    { id: 'Bâtisse et terrain industriels', name: 'Industrial building and land' }
  ];

	propertyBasement: any[] = [];
	propertySwimmingPool: any[] = [];
	propertyGoal: any[] = [];
	propertyMoveDelay: any[] = [];

  propertyBedRooms: any[] = [
    { id: 1, name: "0" },
    { id: 2, name: "1" },
    { id: 3, name: "2" },
    { id: 4, name: "3" },
    { id: 5, name: "4" },
    { id: 6, name: "5" },
    { id: 7, name: "5+" },
  ];

  propertyRooms: any[] = [
    { id: 1, name: "0" },
    { id: 2, name: "1" },
    { id: 3, name: "2" },
    { id: 4, name: "3" },
    { id: 5, name: "3+" },
  ];

  public blockTitle: string;
  public blockContent: string;

  // Recaptcha v3 Subscription
  private submitExecutionSubscription: Subscription;

  constructor(
    @Inject(DOCUMENT) private document: Document,
    private recaptchaV3Service: ReCaptchaV3Service,
    private formBuilder: FormBuilder,
    private evaluationService: EvaluationService,
    private translate: TranslateService,
    private route: ActivatedRoute,
    private blocksService: BlocksService,
    private renderer2: Renderer2
  ) {
    this.translate
      .get("library.evaluation-form.form.surface_values")
      .subscribe((res) => {
        this.propertySurfaces = res;
      });

    this.translate
      .get("library.evaluation-form.form.dimension_values")
      .subscribe((res) => {
        this.propertyDimensions = res;
      });

    this.translate.get('library.evaluation-form.form.basement_values').subscribe(res => {
      this.propertyBasement = res;
    });

    this.translate.get('library.evaluation-form.form.swimmingpool_values').subscribe(res => {
      this.propertySwimmingPool = res;
    });

    this.translate.get('library.evaluation-form.form.demand_goal_values').subscribe(res => {
      this.propertyGoal = res;
    });

    this.translate.get('library.evaluation-form.form.move_delay_values').subscribe(res => {
      this.propertyMoveDelay = res;
    });

    this.blocksService
      .getBlock("bloc-evaluation-immobiliere")
      .subscribe((data) => {
        this.blockTitle = data.title;
        this.blockContent = data.text;
      });
  }

  async ngOnInit() {
    this.createFormControls();
    this.createForm();

    // Dynamically inject GoogleMaps script inside DOM
    // This is kind of ugly, but so far it's the easiest way to use all GoogleMaps features inside Angular
    await this.loadScript(
      "https://maps.googleapis.com/maps/api/js?key=" +
        environment.gmapToken +
        "&libraries=places"
    );
    this.GoogleMaps = window.google.maps;

    // Initialize address field with auto-complete
    const addressElement = document.getElementById("address-input");
    const addressOptions = { componentRestrictions: { country: "ca" } };
    const addressField = new this.GoogleMaps.places.Autocomplete(
      addressElement,
      addressOptions
    );

    addressElement.addEventListener("blur", () => {
      this.document.querySelector(".pac-container").innerHTML = "";
    });

    addressField.addListener("place_changed", () => {
      const address = addressField.getPlace();
      if (address) {
        this.currentLocation = address;
        // Re-focus on field to force variable update (prevents Google bug)
        document.getElementById("address-input").focus();
      }
    });

    // Fetch place info if address is present in URL params
    const address = this.route.snapshot.paramMap.get("address");
    if (address && address.length) {
      const geo = await this.getGeocode(address);
      if (geo) {
        this.currentLocation = geo;
        this.onSearchLocation();
      }
    }

    this.delayRCDisplay();
  }

  // Inject script element into DOM
  private loadScript(url) {
    // Ignore if script already exists
    if (document.querySelector('script[data-name="gmap-script"]')) return;

    return new Promise((resolve, reject) => {
      const script = this.renderer2.createElement("script");
      script.setAttribute("data-name", "gmap-script");
      script.type = "text/javascript";
      script.src = url;
      script.text = "";
      script.async = true;
      script.defer = true;
      script.onload = resolve;
      script.onerror = reject;
      this.renderer2.appendChild(this.document.body, script);
    });
  }

  delayRCDisplay() {
    setTimeout(() => {
      const result = document.getElementsByClassName("grecaptcha-badge");
      const recaptcha = result.item(0) as HTMLElement;
      recaptcha.style.visibility = "visible";
    }, 2000);
  }

  ngOnDestroy() {
    const result = document.getElementsByClassName("grecaptcha-badge");
    const recaptcha = result.item(0) as HTMLElement;
    recaptcha.style.visibility = "hidden";

    if (this.submitExecutionSubscription) {
      this.submitExecutionSubscription.unsubscribe();
    }
  }

  private createFormControls() {
    this.constructYear = new FormControl("", Validators.pattern("[0-9]+"));
    this.reasonBuy = new FormControl("");
    this.renovations = new FormControl("");
    this.fPropertyDimensions = new FormControl('');
		this.fPropertySurface = new FormControl('');
    this.fPurposeOther = new FormControl('');
    this.firstName = new FormControl("", Validators.required);
    this.lastName = new FormControl("", Validators.required);
    this.phoneNumber = new FormControl("", [
      Validators.required,
      Validators.pattern("[0-9]+"),
      Validators.minLength(10),
    ]);
    this.email = new FormControl("", [
      Validators.required,
      Validators.pattern(
        "^\\w+([\\.-]?\\w+)*@\\w+([\\.-]?\\w+)*(\\.\\w{2,3})+$"
      ),
    ]);
    this.phoneExt = new FormControl("");
    this.fileInput = new FormControl("");
  }

  private createForm() {
    this.evaluateForm = this.formBuilder.group({
      constructYear: this.constructYear,
      reasonBuy: this.reasonBuy,
      renovations: this.renovations,
      purpose: this.purpose,
			fPurposeOther: this.fPurposeOther,
			moveDelay: this.moveDelay,
			evaluateAgree: this.evaluateAgree,
			delayToSold: this.delayToSold,
      fileInput: this.fileInput,
      name: new FormGroup({
        firstName: this.firstName,
        lastName: this.lastName,
      }),
      phone: new FormGroup({
        phoneNumber: this.phoneNumber,
        phoneExt: this.phoneExt,
      }),
      email: this.email,
      type: this.selectedPropertyType,
      surface: this.selectedPropertySurface,
      dimensions: this.selectedPropertyDimensions,
      fPropertyDimensions:this.fPropertyDimensions,
			fPropertySurface:this.fPropertySurface,
      bedrooms: this.nbBedRooms,
      bathrooms: this.nbBathRooms,
      garage: this.nbGarage,
      pool: this.nbPool,
			basement: this.nbBasement,
      car_park: this.nbCarParks,
      street_view_link: "",
      // address: this.currentLocation
    });
  }

  // Fetch GoogleMap geocoding from address string, returns null if none
  async getGeocode(address) {
    return new this.GoogleMaps.Geocoder()
      .geocode({ address })
      .then(({ results: [firstResult] }) => firstResult)
      .catch((e) => null);
  }

  // Find current address gmap location and render its streetview
  async onSearchLocation() {
    // Convert to geo object if the location is a string
    if (typeof this.currentLocation === "string")
      this.currentLocation = await this.getGeocode(this.currentLocation);

    // Ignore if location is empty or invalid
    if (!this.currentLocation) return false;

    let heading = 0;
    this.hasStreetView = false;

    const SV = new this.GoogleMaps.StreetViewService();
    const { location } = this.currentLocation.geometry;
    const { lat, lng } = location;

    SV.getPanorama({ location }, (result) => {
      if (result) {
        this.hasStreetView = true;

        // Compute streetview rotation
        const { latLng, pano } = result.location;
        heading = this.GoogleMaps.geometry.spherical.computeHeading(
          latLng,
          location
        );
        const settings = { pano, pov: { heading, pitch: 0 } };

        // Render streetview
        // eslint-disable-next-line no-unused-vars
        const sv = new this.GoogleMaps.StreetViewPanorama(
          document.getElementById("street-view"),
          settings
        );
      }

      // Set email URL
      this.gmapUrl =
        "https://maps.google.com/maps?q=&layer=c&cbll=" +
        lat() +
        "," +
        lng() +
        "&cbp=11," +
        heading +
        ",0,0,0,5";
    });

    this.showForm = true;
  }

  onPreviousStep($event) {
    $event.preventDefault();
    this.currentStep--;
    setTimeout(() => {
      document
        .getElementById("evaluateForm")
        .scrollIntoView({ behavior: "smooth" });
    }, 100);
  }

  onNextStep($event) {
    $event.preventDefault();
    this.currentStep++;
    setTimeout(() => {
      document
        .getElementById("evaluateForm")
        .scrollIntoView({ behavior: "smooth" });
    }, 100);
  }

  onReturnToSearch() {
    this.showForm = false;
    document.getElementById("streetNoResult").classList.remove("show");
  }

  changeGoal($event) {
		if($event.id == 4)
			this.showInputGoal = true;
		else
			this.showInputGoal = false;
	}

  onSubmit() {
    if (!this.evaluateForm.valid) return false;
    this.formLoading = true;

    this.submitExecutionSubscription = this.recaptchaV3Service
      .execute("submit")
      .subscribe(
        (token) => {
          this.evaluateForm.value.type = this.selectedPropertyType;
          this.evaluateForm.value.surface = this.selectedPropertySurface;
          this.evaluateForm.value.dimensions = this.selectedPropertyDimensions;
          this.evaluateForm.value.bedrooms = this.nbBedRooms;
          this.evaluateForm.value.bathrooms = this.nbBathRooms;
          this.evaluateForm.value.garage = this.nbBathRooms;
          this.evaluateForm.value.garage = this.nbGarage;
          this.evaluateForm.value.car_park = this.nbCarParks;
          this.evaluateForm.value.basement = this.nbBasement;
			    this.evaluateForm.value.swimming_pool = this.nbPool;
          this.evaluateForm.value.address = this.currentLocation.formatted_address;
          this.evaluateForm.value.photos = this.photos;
          this.evaluateForm.value.professional_evaluation = this.evaluateAgree;
          this.evaluateForm.value.purpose = this.purpose;
          this.evaluateForm.value.delayToSold = this.delayToSold;
          this.evaluateForm.value.moveDelay = this.moveDelay;
          // this.evaluateForm.value.surface = this.evaluateForm.value.fPropertySurface + ' ' + this.selectedPropertySurfaceUnit;
          // this.evaluateForm.value.dimensions = this.evaluateForm.value.fPropertyDimensions + ' ' + this.selectedPropertyDimensionsUnit;
          this.evaluateForm.value.token_captcha = token;
          this.evaluateForm.value.street_view_link = this.gmapUrl;

          if(environment.webHookZapier){
            // Send Data to Zapier
            fetch(environment.webHookZapier, {
              method: 'POST',
              body: JSON.stringify(this.evaluateForm.value),
            }).catch(err => console.error('Erreur Zapier :', err));
          }

          this.evaluationService
            .postEvaluation(this.evaluateForm.value)
            .subscribe((response) => {
              this.formSend = true;
              this.formLoading = false;

              setTimeout(function () {
                document
                  .getElementById("evaluateForm")
                  .scrollIntoView({
                    behavior: "smooth",
                    block: "start",
                    inline: "nearest",
                  });
              }, 100);

              if (response.success) this.successMessage = true;
              else this.errorMessage = true;
            });
        },
        (error) => console.error(error)
      );
  }

  onFileChange(event) {
    for (let j = 0; j < this.uploader.queue.length; j++) {
      if (this.uploader.queue[j].file.size > 2048 * 1024) {
        document.getElementById("file-upload-error").innerText =
          this.uploader.queue[j].file.name +
          " : " +
          this.translate.instant("library.evaluation-form.form.label.error");
        this.uploader.queue = this.uploader.queue.filter(
          (file) => file.file.name !== this.uploader.queue[j].file.name
        );
      } else {
        const reader = new FileReader();
        const fileItem = this.uploader.queue[j]._file;
        document.getElementById("file-upload-error").innerText = "";
        reader.readAsDataURL(fileItem);
        reader.onload = () => {
          const newFile: any = {};
          if (j === this.uploader.queue.length - 1) {
            this.textButton = this.textButton + fileItem.name;
          } else {
            this.textButton = this.textButton + fileItem.name + ", ";
          }

          newFile.filename = fileItem.name;
          newFile.filetype = fileItem.type;
          newFile.file = reader.result;
          this.photos.push(newFile);
        };
      }
    }

    const totalFileSize = this.uploader.queue.reduce((total, file) => {
      return total + file.file.size;
    }, 0);
    if (totalFileSize >= 16000000) {
      document.getElementById("file-upload").setAttribute("disabled", "true");
    }
  }

  removeFile(event) {
    this.uploader.queue = this.uploader.queue.filter(
      (file) => file.file.name !== event.target.getAttribute("data-file")
    );
  }

  resetForm() {
    this.formSend = false;
    this.evaluateForm.reset();
    this.currentStep = 1;
    this.file = "";
    this.showForm = false;
  }

  retry() {
    this.formSend = false;
    this.currentStep = 1;
  }

  onChangeEvaluate(event){
		this.evaluateAgree = event.target.value;
	}
}
