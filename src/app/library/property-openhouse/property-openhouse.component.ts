import { Component, OnInit, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'lib-property-openhouse',
  templateUrl: './property-openhouse.component.html'
})

export class PropertyOpenhouseComponent implements OnInit {
  @Input() openHouses;
  @Input() mls;
  @Input() address;

  openhouseDates: any;

  constructor (
    public translate: TranslateService
  ) { }

  ngOnInit () {
  }
}
