import { Component, OnInit } from '@angular/core';
import { BlocksService } from '@/services/v3/contentblocks/contentblocks.service';

@Component({
  selector: 'lib-cta-broker',
  templateUrl: './cta-broker.component.html'
})

export class CtaBrokerComponent implements OnInit {

  public blockTitleBloc1: string;
  public blockContentBloc1: string;

  public blockTitleBloc2: string;
  public blockContentBloc2: string;

  constructor (private blocksService: BlocksService) {
    this.blocksService.getBlock('bloc-accueil').subscribe(data => {
      if(data != null){
        this.blockTitleBloc1 = data.title;
        this.blockContentBloc1 = data.text;
      }
    });

    this.blocksService.getBlock('bloc-team-home').subscribe(data => {
      if(data != null){
        this.blockTitleBloc2 = data.title;
        this.blockContentBloc2 = data.text;
      }
    });
  }

  ngOnInit () {
  }
}
