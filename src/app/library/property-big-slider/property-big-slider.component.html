<div itemscope itemtype="http://schema.org/House" class="jsProperty" [routerLink]="['urls.property-single' | translate] + propertyUrl " style="height: 100%">
    <img itemprop="image" src="{{property.ext_coverphoto}}"
         (load)="setImageSize($event)"
         alt="{{ ['library.properties.alt-image' | translate] + property?.ext_address || '' }}" onerror="this.src='assets/images/placeholder/propriete-nb.jpg';">

	<div class="properties-info">
		<div class="bloc-head">
			<ng-container *ngIf="!property.status">
				<p class="price" *ngIf="property.price_sale && !rentalSearch; else showRental">
					<ng-container *ngIf="property.price_sale_unit">
						{{ property.price_sale | currency:'CAD':'symbol-narrow':'1.2-2':this.currentLang }} /
						{{ property.price_sale_unit | lowercase }}
					</ng-container>

					<ng-container *ngIf="!property.price_sale_unit" class="s -inner">
						{{ property.price_sale | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang }}
						<span *ngIf="property.taxes_sale_not_included" class="small-tps">+tps/tvq</span>
					</ng-container>
				</p>

				<ng-template #showRental>
					<p class="price">
						<span class="c -inner" *ngIf="property.property_category !== 'Commercial'">
							{{ property.price_rental | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang }}
							{{ 'library.property-hero.per-month' | translate }}
						</span>

						<span class="c a -inner" *ngIf="property.property_category === 'Commercial' && property.rental_period === 'A'">
							{{ property.price_rental | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang }}
							{{ 'library.property-hero.commercial-year' | translate }}
							<span *ngIf="property.taxes_rental_not_included" class="small-tps">+tps/tvq</span>
						</span>

						<span class="c m -inner" *ngIf="property.property_category === 'Commercial' && property.rental_period !== 'A'">
							{{ property.price_rental | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang }}
							{{ 'library.property-hero.commercial-month' | translate }}
							<span *ngIf="property.taxes_rental_not_included" class="small-tps">+tps/tvq</span>
						</span>
					</p>
				</ng-template>
			</ng-container>
		</div>

		<div class="more-info" itemscope itemtype="http://schema.org/PostalAddress">
			<a [routerLink]="['urls.property-single' | translate] + propertyUrl " class="address" itemprop="address">
				{{ property.displayAddress }}
			</a>
			<div class="align">
                <p class="type">{{property.property_type}}</p>
				<p class="location" itemprop="addressLocality">
					{{property.municipality.description | shorten: 25: '...'}}</p>
			</div>
		</div>
	</div>
</div>
