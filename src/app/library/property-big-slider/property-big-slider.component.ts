import { Component, OnInit, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { InscriptionsService } from '@/services/v3/inscriptions/inscriptions.service';

@Component({
  selector: 'lib-property-big-slider',
  templateUrl: './property-big-slider.component.html'
})

export class PropertyBigSliderComponent implements OnInit {
  @Input() property;
  @Input() rentalSearch = false; // Used to hide rental label when search type is 'rental'

  public currentLang: any;
  propertyUrl: any;

  constructor (
    public translate: TranslateService,
    private inscriptionsService: InscriptionsService
  ) {
    const userLang = window.location.pathname.split('/')[1];
    this.currentLang = userLang || 'fr';
  }

  ngOnInit () {
    if (this.property.neighborhood_slug) {
      this.propertyUrl = '/' + this.property.municipality_slug +
        '/' + this.property.neighborhood_slug +
        '/' + this.property.ext_address_slug +
        '/' + this.property.mls;
    } else {
      this.propertyUrl = '/' + this.property.municipality_slug +
        '/' + this.property.ext_address_slug +
        '/' + this.property.mls;
    }


    this.inscriptionsService.getPhotos(this.property.mls).subscribe(response => {
      if (response && response.data && response.data.length > 0) {
        this.property.ext_coverphoto = response.data[0].url.fullsize;
      }
    });

    // Build display address string
    const { address_civic_start: civic1, address_civic_end: civic2, address_street: street, address_apt: apt } = this.property;
    this.property.displayAddress =
      (civic1 || '') +
      (civic1 && civic2 ? ' - ' + civic2 : '') +
      (street ? ' ' + street : '') +
      (apt ? ' ' + this.translate.instant('library.property-details.apt') + '.' + apt : '');
  }

  setImageSize ({ target }) {
    const img = target as HTMLImageElement;
    img.width = img.naturalWidth;
    img.height = img.naturalHeight;
  }
}
