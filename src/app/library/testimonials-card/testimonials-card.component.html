<div class="testimonial-wrap" itemscope itemtype="http://schema.org/Review">
	<div class="testimonial-content" itemscope itemtype="http://schema.org/Quotation">

		<ng-container *ngIf="!testimonial.video_embed">
			<div *ngIf="testimonial.image" class="img-ctn">
				<img itemprop="image" src="{{testimonial.image}}" alt="{{testimonial.title}}">
			</div>
		</ng-container>

		<ng-container *ngIf="testimonial.video_embed">
			<div class="img-ctn">
				<div class="iframe-container">
					<iframe class="e2e-iframe-trusted-src" [src]="videoUrl" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
				</div>
			</div>
		</ng-container>
		<div class="inner">
			<h3 itemprop="name" class="title">{{testimonial.title}}</h3>
			<p itemprop="reviewBody" class="description" [innerHtml]="testimonial.description"></p>

			<div class="user-ctn">
				<!--<img *ngIf="testimonial.imageAuthor" src="http://via.placeholder.com/42x42" alt="">-->
				<p itemprop="spokenByCharacter">
					<span><i class="icon-user"></i></span>
					{{testimonial.author}}
				</p>
			</div>
		</div>

	</div>
</div>
