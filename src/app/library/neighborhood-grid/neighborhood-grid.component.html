<div class="neighborhood-grid-cpn">
	<div class="container">
		<div class="page-head">
			<h1 class="title">{{ 'library.neighborhood-grid.title' | translate }}</h1>
			 <p class="text">{{ 'library.neighborhood-grid.text' | translate }}</p>
		</div>
    </div>

    <div class="nbh-grid" *ngIf="neighborhoods">
        <div class="nbh-item" *ngFor="let nbh of neighborhoods" [routerLink]="['urls.neighborhoods' | translate, nbh.id]">
            <div class="content">
                <div class="item-img"><img [src]="nbh.header_image" [alt]="nbh.name"></div>
                <a [routerLink]="['urls.neighborhoods' | translate, nbh.id]"><h2 class="item-title">{{ nbh.name }}</h2></a>
                <p [innerHtml]="nbh.description"></p>
            </div>
            <!--<a [routerLink]="['urls.neighborhoods' | translate, nbh.id]" class="main-button -primary">{{ 'library.neighborhood-grid.btn' | translate }}</a>-->
        </div>
    </div>

    <div class="loading -center" *ngIf="!neighborhoods">
        <div class="lds-ripple"><div></div><div></div></div>
    </div>
</div>
