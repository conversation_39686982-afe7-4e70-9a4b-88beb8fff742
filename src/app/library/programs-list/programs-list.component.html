<div class="blog-list-cpn prog-list-ctn" *ngIf="programs?.length>0">
    <div class="container">
        <div class="cpn-head -center">
            <h2 class="title custom">{{ 'library.prog-list.main-title' | translate }}</h2>
        </div>

        <div class="article-list-ctn">
            <!-- BLOG POST -->
            <div class="article" *ngFor="let program of programs">
                <ng-container *ngIf="program.external_link">
                    <a class="img-ctn" href="{{program.link}}" target="_blank">
                        <div class="filter"></div>
                        <div class="prog-illu">
                            <img src="{{program.photo}}" alt="{{ 'library.prog-list.alt-image' | translate }}">
                        </div>
                    </a>
                </ng-container>
                <ng-container *ngIf="!program.external_link">
                    <a class="img-ctn" href="{{program.link}}">
                        <div class="filter"></div>
                        <div class="prog-illu">
                            <img src="{{program.photo}}" alt="{{ 'library.prog-list.alt-image' | translate }}">
                        </div>
                    </a>
                </ng-container>

                <div class="article-info">
                    <h3 class="title">
                        <ng-container *ngIf="program.external_link">
                            <a href="{{program.link}}"
                               target="_blank">{{program.title}}</a>
                        </ng-container>
                        <ng-container *ngIf="!program.external_link">
                            <a href="{{program.link}}">{{program.title}}</a>
                        </ng-container>
                    </h3>
                    <p class="description">{{program.description}}</p>
                </div>
            </div>
            <!-- END BLOG POST -->
        </div>
    </div>
</div>
