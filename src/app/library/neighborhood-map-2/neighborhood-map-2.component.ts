import { Component, OnInit, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { MapService } from '@/services/v3/map/map.service';

@Component({
  selector: 'lib-neighborhood-map-2',
  templateUrl: './neighborhood-map-2.component.html'
})

export class NeighborhoodMap2Component implements OnInit {
  @Input() neighborhood;

  map: any;
  mapColor: string;

  constructor (
    private mapService: MapService,
    private translate: TranslateService
  ) {}

  async ngOnInit () {
    // Fetch map color from client infos
    const { color } = await this.translate.get('client.map').toPromise();
    this.mapColor = color;
  }

  async initMap () {
    // Create map object
    this.map = this.mapService.createMap();

    // Render map and add events
    this.map.on('load', () => {
      // Attach map geojson data
      this.map.addSource('quartiers-data', { type: 'geojson', data: this.neighborhood.geoJSON });

      // Build polygons using conditionnal opacity
      this.map.addLayer({
        id: 'quartiers',
        type: 'fill',
        source: 'quartiers-data',
        paint: {
          'fill-color': this.mapColor,
          'fill-opacity': ['case', ['boolean', ['feature-state', 'selected'], false], 0.6, 0.3]
        }
      });

      // Center map on neighborhood
      const bounds = this.mapService.getBoundaries(this.neighborhood.geoJSON.features);
      this.map.fitBounds(bounds, { padding: 50 });
    });
  }
}
