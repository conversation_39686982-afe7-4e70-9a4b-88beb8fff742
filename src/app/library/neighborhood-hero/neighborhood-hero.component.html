<div *ngIf="loading" class="hero-loader">
    <div class="loading-circle"></div>
</div>
<div class="property-hero-cpn classic neighborhood-hero-cpn" itemscope itemtype="http://schema.org/Place" [class.loading]="loading" *ngIf="neighborhood">
    <!-- Slider main container -->
    <div class="swiper js-swiper">
        <div class="swiper-container">
            <div class="swiper-wrapper">
                <div class="swiper-slide">
                    <div class="filter"></div>
                    <img itemprop="photo" src="{{neighborhood.header_image}}" alt="{{neighborhood.name}}">
                    <div class="container">
                        <div class="inner">
                            <h1 itemprop="name" *ngIf="neighborhood.name" class="name">{{neighborhood.name}}</h1>
                            <p class="open-video" *ngIf="neighborhood.video_url">
                                <span (click)="onOpenFullScreen($event)" class="icon-play"></span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<ng-container *ngIf="neighborhood">
    <div class="full-screen-cpn" id="fullSlider" *ngIf="neighborhood.video_url">
        <div class="close" (click)="onCloseFullScreen()">
            <span class="icon-close"></span>
        </div>
        <div class="background" (click)="onCloseFullScreen()"></div>
        <div class="video-ctn container">
            <div class="video-inner">
                <p class="e2e-iframe-trusted-src">{{neighborhood.video_url.title}}</p>
                <iframe id="video-yt" class="e2e-iframe-trusted-src" [src]="videoUrl" frameborder="0" allow="autoplay; encrypted-media" allowfullscreen></iframe>
            </div>
        </div>
    </div>
</ng-container>
