<div class="location-pane-cpn container-fluid" [class.open]="settings.propertiesPaneIsOpen">
	<a class="toggle-pane" (click)="togglePanel()">
		<div><span class="icon-arrow-left"></span></div>
	</a>

	<div class="pane-content-ctn">
		<h3 class="pane-title">{{ 'library.property-map.location-pane.where-you-would-live' | translate }}</h3>

		<div class="info-ctn" *ngIf="neighborhood?.ext_address">
			<!--<p class="sub-section-title">{{ 'library.property-map.location-pane.address' | translate }}</p>-->
			<p class="address">{{ neighborhood.ext_address }} <br/>{{ neighborhood.municipality_label }}</p>
            <a [href]="getGoogleMapsLink(neighborhood.ext_address + ' ' + neighborhood.municipality_label)" target="_blank" class="small-link direction">
                {{ 'library.property-map.location-pane.direction' | translate }}
            </a>
		</div>

		<div class="info-ctn" *ngIf="neighborhood?.sites_neighborhood_name">
			<!--<p class="sub-section-title">{{ 'library.property-map.location-pane.neighborhood' | translate }}</p>-->
			<p class="location">{{ neighborhood.sites_neighborhood_name }}</p>
			<p class="description" [innerHtml]="neighborhood.sites_neighborhood_descrtiption  | shorten: 200: '...'"></p>

			<a [routerLink]="['urls.neighborhoods' | translate , neighborhood.sites_neighborhood_slug]" class="small-link">
        {{ 'library.property-map.location-pane.discover-neighborhood' | translate }}
      </a>
		</div>

	</div>
</div>
