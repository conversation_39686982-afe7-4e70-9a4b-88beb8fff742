<div class="blog-list-cpn blog-list-1" *ngIf="blogPosts?.length > 0">
    <div class="container">
        <div class="cpn-head">
            <h2 class="title">{{ 'library.blog-list-1.title' | translate }}</h2>
            <a [routerLink]="['urls.real-estate-blog' | translate ]" class="small-link right hide-mobile">{{ 'library.blog-list-1.all-posts' | translate }}
                <i class="icon-arrow-right"></i>
            </a>
        </div>

        <div class="article-list-ctn">
            <!-- BLOG POST -->
            <div  itemscope itemtype="http://schema.org/Blog" *ngFor="let blogPost of blogPosts | orderBy: '-publication_date'" class="article">
                <lib-article-1 itemprop="blogPost" [blogPost]="blogPost"></lib-article-1>
            </div>
            <!-- END BLOG POST -->
        </div>
        <a [routerLink]="['urls.real-estate-blog' | translate ]" class="small-link right show-mobile">{{ 'library.blog-list-1.all-posts' | translate }}
            <i class="icon-arrow-right"></i>
        </a>
    </div>
</div>
