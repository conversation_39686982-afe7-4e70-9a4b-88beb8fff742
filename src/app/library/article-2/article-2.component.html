<div class="flex-wrap">
	<a *ngIf="blogPost.coverphoto" class="img-ctn col-xs-12 col-sm-5" [routerLink]="['urls.real-estate-blog' | translate, blogPost.slug ]">
		<img src="{{ blogPost.coverphoto }}" alt="{{'library.article.alt-image' | translate}}{{ blogPost.title }}">
	</a>
	<div [ngClass]="blogPost.coverphoto ? 'col-sm-7' : 'col-sm-12'" class="article-info col-xs-12">
		<h3 class="title"><a [routerLink]="['urls.real-estate-blog' | translate, blogPost.slug ]">{{ blogPost.title }}</a></h3>
		<p class="date">{{ blogPost.publication_date | localizedDate:'longDate'}}</p>
		<p class="description">{{ blogPost.abstract | shorten: 600: '...' }}</p>
	</div>
	<a [routerLink]="['urls.real-estate-blog' | translate, blogPost.slug ]" class="blog-button"><i class="icon-arrow-right"></i></a>
</div>
