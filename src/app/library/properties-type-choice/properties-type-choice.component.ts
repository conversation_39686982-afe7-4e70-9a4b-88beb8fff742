import { Component, OnInit, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
@Component({
  selector: 'lib-properties-type-choice',
  templateUrl: './properties-type-choice.component.html'
})
export class PropertiesTypeChoiceComponent implements OnInit {

  @Input() collection;
  @Input() property;
  @Input() openhouse;

  constructor(public translate: TranslateService) { }

  ngOnInit() {
  }
}
