<div class="properties-type-choice-cpn">
    <div class="grid-ctn" [class.no-open-house]="!openhouse">
        <div class="column">
            <div class="cta-bloc collection-bloc" [routerLink]="['urls.search-properties' | translate]" [queryParams]="{ collection: '1' }" *ngIf="collection">
                <div class="image-ctn">
                    <div class="abs-img">
                        <img src="{{ 'library.property-type-choice.collection-img' | translate }}" alt="">
                    </div>
                    <img class="property-img" src="{{collection.ext_coverphoto}}" alt="">
                    <div class="filter"></div>
                </div>
                <div class="info">
                    <a class="main-cta">{{ 'library.property-type-choice.collection' | translate }} <span class="icon icon-arrow-right-full"></span></a>
                    <p class="property-info">
                        <ng-container *ngIf="collection.address_civic_start && collection.address_civic_end">
                            <span itemprop="streetAddress">{{collection.address_civic_start}}
                                -
                                {{collection.address_civic_end}},
                            </span>
                        </ng-container>
                        <ng-container *ngIf="collection.address_civic_start && !collection.address_civic_end">
                            <span itemprop="streetAddress">{{collection.address_civic_start}},
                            </span>
                        </ng-container>
                        <span *ngIf="collection.address_street" itemprop="streetAddress">{{collection.address_street}}</span>
                        <span *ngIf="collection.address_apt">
                            {{ 'library.property-details.apt' | translate }}.
                            {{ collection.address_apt }}</span>

                            <span class="municipality" *ngIf="collection.municipality_label">
                                {{collection.municipality_label | shorten: 50: '...'}}
                            </span>
                    </p>
                </div>
            </div>

            <div class="cta-bloc" [routerLink]="['urls.search-properties' | translate ]" *ngIf="property">
                <div class="image-ctn">
                    <img class="property-img" src="{{property.ext_coverphoto}}" alt="">
                    <div class="filter"></div>
                </div>
                <div class="info">
                    <a class="main-cta">{{ 'library.property-type-choice.property' | translate }} <span class="icon icon-arrow-right-full"></span></a>
                    <p class="property-info">
                        <ng-container *ngIf="property.address_civic_start && property.address_civic_end">
                            <span itemprop="streetAddress">{{property.address_civic_start}}
                                -
                                {{property.address_civic_end}},
                            </span>
                        </ng-container>
                        <ng-container *ngIf="property.address_civic_start && !property.address_civic_end">
                            <span itemprop="streetAddress">{{property.address_civic_start}},
                            </span>
                        </ng-container>
                        <span *ngIf="property.address_street" itemprop="streetAddress">{{property.address_street}}</span>
                        <span *ngIf="property.address_apt">
                            {{ 'library.property-details.apt' | translate }}.
                            {{ property.address_apt }}</span>

                            <span class="municipality" *ngIf="property.municipality_label">
                                {{property.municipality_label | shorten: 50: '...'}}
                            </span>
                    </p>
                </div>
            </div>

            <div class="cta-bloc visites-bloc" [routerLink]="['urls.open-house' | translate ]" *ngIf="openhouse">
                <div class="image-ctn">
                    <img class="property-img" src="{{openhouse.ext_coverphoto}}" alt="">
                    <div class="filter"></div>
                </div>
                <div class="info">
                    <a class="main-cta">{{ 'library.property-type-choice.openhouse' | translate }} <span class="icon icon-arrow-right-full"></span></a>
                    <p class="property-info">
                        <ng-container *ngIf="openhouse.address_civic_start && openhouse.address_civic_end">
                            <span itemprop="streetAddress">{{openhouse.address_civic_start}}
                                -
                                {{openhouse.address_civic_end}},
                            </span>
                        </ng-container>
                        <ng-container *ngIf="openhouse.address_civic_start && !openhouse.address_civic_end">
                            <span itemprop="streetAddress">{{openhouse.address_civic_start}},
                            </span>
                        </ng-container>
                        <span *ngIf="openhouse.address_street" itemprop="streetAddress">{{openhouse.address_street}}</span>
                        <span *ngIf="openhouse.address_apt">
                            {{ 'library.property-details.apt' | translate }}.
                            {{ openhouse.address_apt }}</span>

                            <span class="municipality" *ngIf="openhouse.municipality_label">
                                {{openhouse.municipality_label | shorten: 50: '...'}}
                            </span>
                    </p>
                </div>
            </div>

            <div class="cta-bloc openhouse-bloc" [routerLink]="['urls.reason' | translate]"[queryParams]="{ home: '1' }">
                <div class="image-ctn">
                    <img class="property-img" src="/assets/images/mab-50.jpg" alt="">
                    <div class="filter"></div>
                </div>
                <div class="info">
                    <a class="main-cta"><span [innerHTML]="'library.property-type-choice.50-reasons' | translate"></span><span class="icon icon-arrow-right-full"></span></a>
                </div>
            </div>
        </div>

        <!-- En -->
        <!-- <div class="col-2" *ngIf="translate.currentLang == 'en'">
            <div class="cta-bloc openhouse-bloc" [routerLink]="['urls.search-properties' | translate]" [queryParams]="{ openhouse: '1' }">
                <div class="image-ctn">
                    <img class="property-img" src="/assets/images/common/home-visite_libre.jpg" alt="">
                </div>
                <div class="info">
                    <a class="main-cta">{{ 'library.property-type-choice.openhouse' | translate }} <span class="icon icon-arrow-right-full"></span></a>
                </div>
            </div>
        </div> -->
    </div>
</div>
