<div class="facebook-posts-cpn" *ngIf="posts?.length">
    <div class="container">
        <h2 class="title">{{ "library.facebook-posts.title" | translate }} <span class="icon-logo-facebook"></span></h2>

        <div class="facebook-posts-container">
            <ng-container *ngFor="let post of posts">
                <div class="post">
                    <a [href]="post.link" target="_blank">
                        <img class="post-image" src="{{post.image}}" alt="Post Image">
                    </a>
                    <p>{{ cutText(post.message, 550) }}</p>
                </div>
            </ng-container>
        </div>

        <div class="button-ctn">
            <a [href]="['client.facebook-posts' | translate ]" target="_blank" class="main-button -primary">{{ "library.facebook-posts.all" | translate }}</a>
        </div>
    </div>
</div>
