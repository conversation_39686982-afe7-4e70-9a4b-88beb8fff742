import { Component, OnInit } from '@angular/core';
import { FacebookPostsService } from '@/services/v3/facebook-posts/facebook-posts.service';

@Component({
  selector: 'lib-facebook-posts',
  templateUrl: './facebook-posts.component.html',
})

export class FacebookPostsComponent implements OnInit {
  posts: any[] = [];

  constructor(private facebookService: FacebookPostsService) {}

  ngOnInit() {
      // On garde en mémoire les posts pendant 24h
        if (!localStorage.getItem('facebookPosts') || new Date().getTime() - parseInt(localStorage.getItem('facebookPostsDate'), 10) > 86400000) {
            this.getAllPosts();
            localStorage.setItem('facebookPostsDate', new Date().getTime().toString());
        } else {
            this.posts = JSON.parse(localStorage.getItem('facebookPosts'));
        }
  }

  getAllPosts() {
      this.facebookService.getPosts().subscribe((data: any) => {
          this.posts = data.data.slice(0, 3); // Affiche les trois premières publications

          this.posts.forEach((post, index) => {
              this.facebookService.getPostImage(post.id).subscribe((image: any) => {
                  this.posts[index].image = image.data[0].media.image.src;
              });

              this.posts[index].link = `https://www.facebook.com/${post.id}`;
          });
      });
  }

    cutText(text: string, length: number): string {
        return text.length > length ? `${text.substring(0, length)}...` : text;
    }
}
