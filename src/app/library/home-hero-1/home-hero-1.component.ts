import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

import { InscriptionsService } from '@/services/v3/inscriptions/inscriptions.service';

import Swiper, { Navigation } from 'swiper';

@Component({
  selector: 'lib-home-hero-1',
  templateUrl: './home-hero-1.component.html'
})

export class HomeHero1Component implements OnInit {
  public loading: boolean = true;
  public homeHeroSwiper: any;
  public properties: any;

  constructor (
    public translateService: TranslateService,
    private inscriptionsService: InscriptionsService
  ) { }

  ngOnInit () {
    this.getProperties();
  }

  initSlider () {
    Swiper.use([Navigation]);
    this.homeHeroSwiper = new Swiper('.swiper-container', {
      speed: 1000,
      rewind: true,
      simulateTouch: false,
      navigation: {
        nextEl: '.swiper-button-next',
        prevEl: '.swiper-button-prev'
      }
    });

    this.loading = false;
  }

  getProperties () {
    this.inscriptionsService.getInscriptions(10, { cover: 1 }).subscribe(({ data }) => {
      this.properties = data; // [...data, ...data, ...data]; // To test slider if there is only 1 property
      this.initSlider();
    });
  }
}
