<div class="reviews-slider-cpn" *ngIf="reviews.reviews?.length">
    <div class="container">
        <h2 class="title">{{ "library.reviews.title" | translate }}</h2>
        <div class="rating-stars --head">
            <i class="icon-star"></i>
            <i class="icon-star"></i>
            <i class="icon-star"></i>
            <i class="icon-star"></i>
            <i class="icon-star"></i>
        </div>

        <div class="swiper-container reviews-list-ctn">
            <div class="swiper-wrapper reviews-list-inner">

                <ng-container *ngFor="let review of reviews.reviews | slice:0:10">
                    <div class="swiper-slide review" *ngIf="getNumericRating(review.starRating) == 5">
                        <div class="review-header">
                            <div class="review-header-left">
                                <img class="reviewer-image" src="{{review.reviewer.profilePhotoUrl}}" alt="Reviewer Image">
                            </div>
                            <div class="review-header-right">
                                <div class="reviewer-name">{{review.reviewer.displayName}}</div>
                                <div class="reviewer-rating">
                                    <div class="rating-stars">
                                        <i *ngFor="let star of [1, 2, 3, 4, 5]" class="icon-star" [ngClass]="{'checked': star <= getNumericRating(review.starRating)}"></i>
                                    </div>
                                    <div class="rating-value">{{review.rating}}</div>
                                </div>
                                <div class="reviewer-date">{{ formatDate(review.createTime) }}</div>
                            </div>
                        </div>
                        <div class="review-body">
                            <div class="review-text">{{ cutText(filterText(review.comment), 250) }}</div>
                        </div>
                    </div>
                </ng-container>
            </div>

            <div class="swiper-button-prev swiper-btn"><i></i></div>
            <div class="swiper-button-next swiper-btn"><i></i></div>

            <div class="swiper-pagination"></div>
        </div>
    </div>
</div>

