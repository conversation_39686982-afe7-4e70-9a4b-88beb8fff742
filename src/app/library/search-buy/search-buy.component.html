<div class="search-buy-cpn">
	<div class="container">
		<div class="tabs-ctn col-md-8 col-sm-10">

			<h2 class="title"><span class="icon icon-eval-1"></span>{{ 'library.search-buy.title' | translate }}</h2>

			<form class="form-ctn" (ngSubmit)="onSubmitBuy()">
				<div id="acheter" class="input-ctn">
					<ng-select
            			[class]="'large'"
						[searchable]="true"
						[items]="municipalites"
						bindLabel="label"
						bindValue="label"
						[(ngModel)]="search.city"
						[ngModelOptions]="{standalone: true}"
						placeholder="{{ 'library.search-buy.placeholder' | translate }}" class="city-filter align-center">
					</ng-select>

					<button class="main-button -primary-small" type="submit"><span>{{ 'library.search-buy.button' | translate }}</span></button>
				</div>
			</form>
      
			<a class="special-search" [routerLink]="['urls.search-properties' | translate ]">{{ 'library.search-buy.advanced-search' | translate }}<i class="icon-arrow-externe"></i></a>
		
    </div>
	</div>
</div>
