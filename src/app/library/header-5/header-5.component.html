<lib-panel-5></lib-panel-5>
<header id="header5" [ngClass]="{'transparent': transparent === 'true', 'special': specialColor === 'true'}">
	<div class="main-header">
		<div class="logo-ctn" itemscope itemtype="http://schema.org/Organization">
			<a itemprop="url" [routerLink]="['urls.home' | translate]"><img src="assets/images/common/main-logo.svg" alt="{{ 'client.name' | translate }} {{ 'library.header.alt-image' | translate }}"></a>
			<img class="special-logo" src="assets/images/common/kryzalid.svg" alt="">
		</div>

		<div class="right">
			<li class="number-ctn" itemscope itemtype="http://schema.org/ContactPoint">
				<a itemprop="telephone" href="tel:{{ 'client.phone' | translate }}"><i class="icon-phone"></i>{{ 'client.phone' | translate }}</a>
			</li>
			
			<li class="favorite">
				<a [routerLink]="['urls.favorites' | translate]"><i class="icon-favorite"></i> <span id="favorite-number">0</span></a>
			</li>

			<a href="#" class="header-menu-toggle" (click)="onOpenPanel($event)"><span></span> <span></span> <span></span></a>
		</div>
	</div>
</header>
