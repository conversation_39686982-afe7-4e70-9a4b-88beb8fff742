<div class="dual-wrap">
	<div class="stats-wrap">
		<div class="stats">
			<p class="name">{{ 'library.property-tools.tool-income.residential' | translate }}</p>
			<p class="value" *ngIf="property.potential_income_res">{{property.potential_income_res | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang}}</p>
			<p class="value" *ngIf="!property.potential_income_res">N/D</p>
		</div>
		<div class="stats">
			<p class="name">{{ 'library.property-tools.tool-income.commercial' | translate }}</p>
			<p class="value" *ngIf="property.potential_income_comm">{{property.potential_income_comm | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang}}</p>
			<p class="value" *ngIf="!property.potential_income_comm">N/D</p>
		</div>
		<div class="stats">
			<p class="name">{{ 'library.property-tools.tool-income.car-park' | translate }}</p>
			<p class="value" *ngIf="property.potential_income_stat">{{property.potential_income_stat | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang}}</p>
			<p class="value" *ngIf="!property.potential_income_stat">N/D</p>
		</div>
		<div class="stats -no-border">
			<p class="name">{{ 'library.property-tools.tool-income.other' | translate }}</p>
			<p class="value" *ngIf="property.potential_income_au">{{property.potential_income_au | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang}}</p>
			<p class="value" *ngIf="!property.potential_income_au">N/D</p>
		</div>
		<div class="stats -total">
			<p class="name">Total</p>
			<p class="value" *ngIf="property.potential_income_total">{{property.potential_income_total | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang}}</p>
			<p class="value" *ngIf="!property.potential_income_total">N/D</p>
		</div>

	</div>
	<div class="graph-ctn">
		<ng-container>
			<canvas baseChart
			  	[chartType]="'doughnut'"
			    [data]="[property.potential_income_res, property.potential_income_comm, property.potential_income_stat, property.potential_income_au]"
			    [labels]="chartLabels"
			    [options]="chartOptions"
			    [colors]="chartColors"
			    [legend]="true"
			    (chartHover)="chartHovered($event)"
			    (chartClick)="chartClicked($event)"></canvas>
		</ng-container>    
	</div>
</div>
