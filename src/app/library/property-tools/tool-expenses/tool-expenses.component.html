<div class="dual-wrap">
	<form class="form-payments">
		<div class="input-ctn">
			<label>{{ 'library.property-tools.tool-expenses.property-price' | translate }}</label>
			<input type="text" name="salesprice" [ngModel]="salesprice | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang" [ngModelOptions]="{standalone: true}" disabled>
		</div>
		<div class="input-ctn">
			<label>{{ 'library.property-tools.tool-expenses.downpayment' | translate}}</label>
			<input type="text" name="cashdown" mask="999999999" [(ngModel)]=cashdown (ngModelChange)="calcPayment()"  [ngModelOptions]="{standalone: true}">
		</div>
	<div class="input-ctn">
	  <label>{{ 'library.property-tools.tool-expenses.interest-rate' | translate }}*</label>
	  <input type="text" name="interestRate" dropSpecialCharacters="false" mask="9.99" [(ngModel)]=interestRate (ngModelChange)="calcPayment()" [ngModelOptions]="{standalone: true}">
	</div>
		<div class="input-ctn">
			<label>{{ 'library.property-tools.tool-expenses.mortgage' | translate }}</label>
			<ng-select
				[searchable]="false"
				[items]="mortgage"
				bindLabel="name"
				bindValue="value"
				[(ngModel)]="mortgageLength"
				[ngModelOptions]="{standalone: true}"
				(change)="calcPayment()"
				placeholder="{{ 'library.property-tools.tool-expenses.mortgage' | translate }}" class="hypotheque-filter align-center">
			  </ng-select>
		</div>
		<div class="value-ctn">
			<p *ngIf="payments">{{ 'library.property-tools.tool-expenses.payments' | translate }}</p>
			<p class="value" *ngIf="payments">{{ payments | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang}}</p>
		</div>
	</form>
	<div class="graph-ctn">
		<ng-container *ngIf="chartData">
			<canvas baseChart
				[chartType]="'doughnut'"
				[data]="chartData"
				[labels]="chartLabels"
				[options]="chartOptions"
				[colors]="chartColors"
				[legend]="true"
				(chartHover)="chartHovered($event)"
				(chartClick)="chartClicked($event)"></canvas>
		</ng-container>
	</div>
</div>
