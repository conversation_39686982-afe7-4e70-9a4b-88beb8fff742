<div class="homestaging-slider-cpn">
    <div class="container">
        <div class="row" *ngIf="homestagingsItems?.length > 0">
            <div class="col-md-5 col-lg-4">
                <h2 class="title">{{blockTitle}}</h2>
                <div class="description page-description" [innerHTML]="blockContent">

                </div>
            </div>
            <div class="col-md-7 col-lg-7 col-lg-push-1">
                <lib-slider-default [slides]="homestagingsItems" [showDesc]="true"></lib-slider-default>
            </div>
        </div>
        <div class="team-info-box -large">
            <div class="team-info-box-content">
                <div class="col-sm-8 col-md-8">
                    <p class="title">{{ 'library.homestaging-slider.infobox.title' | translate }}</p>
                    <p class="description">{{ 'library.homestaging-slider.infobox.description' | translate }}</p>
                </div>
                <div class="col-sm-4 col-md-push-1 col-md-3">
                    <a [routerLink]="['urls.contact' | translate ]" class="main-button -primary">{{ 'library.homestaging-slider.infobox.button' | translate }}</a>
                </div>
            </div>
        </div>
    </div>
</div>
