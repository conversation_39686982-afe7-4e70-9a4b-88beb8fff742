import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

import { HomestagingService } from '@/services/v3/homestaging/homestaging.service';
import { BlocksService } from '@/services/v3/contentblocks/contentblocks.service';

@Component({
  selector: 'lib-homestaging-slider',
  templateUrl: './homestaging-slider.component.html'
})

export class HomestagingSliderComponent implements OnInit {
  public homestagingsItems: any;
  public blockTitle: string;
  public blockContent: string;

  constructor (
    private homestagingService: HomestagingService,
    private blocksService: BlocksService,
    private translate: TranslateService
  ) { }

  async ngOnInit () {
    this.blocksService.getBlock('bloc-homestaging').subscribe(data => {
      this.blockTitle = data.title;
      this.blockContent = data.text;
    });

    const after = await this.translate.get('library.homestaging-slider.after').toPromise();
    const before = await this.translate.get('library.homestaging-slider.before').toPromise();

    const slides = [];
    const { data } = await this.homestagingService.getHomestaging().toPromise();

    // TODO: Optimize this?
    data.forEach(item => {
      const title = item.title || ''; // Si item.title est null ou undefined, on met une chaîne vide
      title ? title  + ' ' : title; // Si item.title est null ou undefined, on met une chaîne vide

      if (item.image_before && item.image_after) {
        slides.push({ slideImg: item.image_before, slideDesc: title + before });
        slides.push({ slideImg: item.image_after, slideDesc: title + after });
      } else {
        if (item.image_before) {
          slides.push({ slideImg: item.image_before, slideDesc: title });
        } else if (item.image_after) {
          slides.push({ slideImg: item.image_after, slideDesc: title });
        }
      }
    });

    this.homestagingsItems = slides;
  }
}
