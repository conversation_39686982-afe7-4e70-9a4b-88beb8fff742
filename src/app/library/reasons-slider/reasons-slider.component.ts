import { Component, OnInit } from '@angular/core';
import Swiper, { Controller, Navigation, Pagination, Autoplay } from 'swiper';
import { TranslateService } from '@ngx-translate/core';
import { ActivatedRoute} from '@angular/router';

@Component({
  selector: 'lib-reasons-slider',
  templateUrl: './reasons-slider.component.html'
})
export class ReasonsSliderComponent implements OnInit {

  slidesContent: any;

  constructor(
    private translate: TranslateService,private route: ActivatedRoute
  ) { }

  ngOnInit() {
    this.initSlider();

    this.translate.get('library.reasons-slider-text.slides').subscribe(res => {
      this.slidesContent = res;
		});
  }


  initSlider() {
    Swiper.use([Navigation, Pagination, Controller, Autoplay]);

    setTimeout(function(){
      let contentSwiper = new Swiper('.swiper-content .swiper-container', {
        speed: 1000,
        spaceBetween: 40,
        observer: true,
        simulateTouch: false,
        runCallbacksOnInit: false,
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev',
        },
      });


      let imageSwiper = new Swiper('.swiper-content-img .swiper-container', {
        speed: 1000,
        observer: true,
        simulateTouch: false,
        runCallbacksOnInit: false,
        effect: 'fade'
      });


      contentSwiper.controller.control = imageSwiper;
      imageSwiper.controller.control = contentSwiper;


    }, 1000);
	}

}
