<form class="search-filters-cpn" autocomplete="off">
  <!-- SEARCH BAR -->
  <div class="section-search-bar">
    <div class="form-group-search">
      <!-- SEARCH ICON AND CLEAR BUTTON -->
      <span *ngIf="!search.keyword" class="icon-search"></span>
      <span
        *ngIf="search.keyword"
        class="icon-close"
        (click)="clearSearch()"
        [title]="'library.search-full.filters.clear-search' | translate"
      ></span>

      <!-- INPUT FIELD -->
      <input
        id="search"
        type="text"
        [(ngModel)]="searchString"
        [ngModelOptions]="{ standalone: true }"
        (keyup)="onSearch($event)"
        (blur)="delayDropdownClose()"
        placeholder="{{ searchPlaceholder }}"
      />

      <!-- DROPDOWN SEARCH RESULTS -->
      <ul
        class="search-list-dropdown"
        *ngIf="searchboxProperties?.length && searchModified"
      >
        <li *ngFor="let property of searchboxProperties">
          <a [routerLink]="getPropertyUrl(property)" [innerHTML]="property.fullAddress"></a>
        </li>
      </ul>
    </div>
  </div>

  <!-- BOUTON TOGGLE FILTRES -->
  <div class="filters-toggle-ctn">
    <div class="view-toggle-ctn">
      <a
        class="btn-toggle-list"
        [class.active]="!showMapOnly"
        (click)="toggleMapPane(false)"
        >{{ "library.toggler.list" | translate }}</a
      >
      <a
        class="btn-toggle-map"
        [class.active]="showMapOnly"
        (click)="toggleMapPane(true)"
        >{{ "library.toggler.map" | translate }}</a
      >
    </div>
    <a class="main-button -primary filters-toggle" (click)="toggleFilters()">
      <img
        src="/assets/images/SVG/UI/icon-{{
          filtersVisible ? 'minus' : 'plus'
        }}.svg"
        class="white"
        alt=""
      />
      <img
        src="/assets/images/SVG/UI/icon-{{
          filtersVisible ? 'minus' : 'plus'
        }}-blue.svg"
        class="blue"
        alt=""
      />
      {{ "library.search-full.filters.filters-toggle" | translate }}
    </a>
  </div>

  <!-- DROPDOWNS RÉSIDENTIEL/COMMERCIAL / VENDRE/LOUER -->
  <div class="base-filters" [class.hidden]="isMobile && !filtersVisible">
    <ng-select
      *ngIf="commercialEnabled"
      name="property-category"
      [searchable]="false"
      [clearable]="false"
      [items]="propertyCategories"
      bindValue="id"
      bindLabel="label"
      [(ngModel)]="search.searchType"
      (change)="onChangeCategory()"
      class="property-category-filter"
    >
    </ng-select>

    <ng-select
      name="types"
      [searchable]="false"
      [clearable]="false"
      [items]="inscriptionTypes"
      bindValue="id"
      bindLabel="label"
      [(ngModel)]="search.selectedInscriptionType"
      (change)="submitFilters(false)"
      class="types-filter"
    >
    </ng-select>
  </div>

  <!-- FILTRES -->
  <div class="section-filters" [hidden]="!filtersVisible">
    <div class="filter-fields">
      <!-- COLONNE 1 RÉSIDENTIEL SLIDER PRIX/ALERTE -->
      <div class="filter-column">
        <div [hidden]="!(search.selectedInscriptionType !== 2)">
          <h5 class="slider">
            {{ "library.search-full.filters.groups.price" | translate }}
          </h5>
          <div class="slider-wrapper">
            <div id="price-picker" class="slider-picker"></div>
          </div>
        </div>
        <div [hidden]="!(search.selectedInscriptionType !== 1)">
          <h5 class="slider">
            {{ "library.search-full.filters.groups.rental-price" | translate }}
          </h5>
          <div class="slider-wrapper">
            <div id="rental-price-picker" class="slider-picker"></div>
          </div>
        </div>
        <div
          *ngIf="search.selectedInscriptionType !== 2 && !isMobile"
          class="real-estate-alert"
        >
          <img src="/assets/images/SVG/icons/bell.svg" />
          <div>
            {{ "library.search-full.alert" | translate }}
            <a [routerLink]="'urls.real-estate-alert' | translate">{{
              "library.search-full.alert-link" | translate
            }}</a
            >.
          </div>
        </div>
        <!-- <div [hidden]="!(search.searchType === 1 && search.selectedInscriptionType === 2) && !(search.searchType === 2)">
          <h5 class="slider" style="margin-top: 20px;">{{ 'library.search-full.filters.groups.surface' | translate }}</h5>
          <div id="surface-picker" class="slider-picker"></div>
        </div>  -->
      </div>

      <!-- COLONNE 2 RESIDENTIEL/COMMERCIAL TYPES -->
      <div class="filter-column" *ngIf="propertyTypes">
        <h5>
          {{ "library.search-full.filters.groups.property-type" | translate }}
        </h5>
        <ng-container *ngFor="let category of propertyTypesKeys()">
          <div
            class="custom-checkbox-group"
            *ngIf="propertyTypes[category].length"
          >
            <div *ngFor="let type of propertyTypes[category]">
              <input
                class="custom-checkbox"
                type="checkbox"
                id="checkbox-{{ slugify(type.property_type) }}"
                [(ngModel)]="activeFilters.types[type.property_type]"
                (change)="submitFilters()"
                [ngModelOptions]="{ standalone: true }"
              />
              <label for="checkbox-{{ slugify(type.property_type) }}"
                ><span>{{ type.description }}</span></label
              >
            </div>
          </div>
        </ng-container>
      </div>

      <!-- COLONNE 3 RESIDENTIEL CARACTÉRISTIQUES/AUTRES -->
      <div class="filter-column">
        <h5 *ngIf="search.searchType !== 'commercial'">
          {{ "library.search-full.filters.groups.carac" | translate }}
        </h5>
        <div
          class="caracteristiques-group"
          *ngIf="search.searchType !== 'commercial'"
        >
          <ng-select
            name="list-rooms-bedroom"
            placeholder="{{
              'library.search-full.filters.fields.bedroom' | translate
            }}"
            [searchable]="false"
            [items]="filterLabels.rooms_bedroom_total_number"
            [(ngModel)]="activeFilters.rooms_bedroom_total_number"
            (change)="submitFilters()"
            class="caracteristiques-filter"
          >
          </ng-select>
          <!-- <ng-select
            name="list-parking"
            placeholder="{{ 'library.search-full.filters.fields.parking' | translate }}"
            [searchable]="false"
            [items]="filterLabels.parking"
            [(ngModel)]="activeFilters.parking"
            class="caracteristiques-filter">
          </ng-select> -->
          <ng-select
            name="list-rooms-bathroom"
            placeholder="{{
              'library.search-full.filters.fields.bathroom' | translate
            }}"
            [searchable]="false"
            [items]="filterLabels.rooms_bathroom_number"
            [(ngModel)]="activeFilters.rooms_bathroom_number"
            (change)="submitFilters()"
            class="caracteristiques-filter"
          >
          </ng-select>
          <!-- <ng-select
            name="list-garage"
            placeholder="{{ 'library.search-full.filters.fields.garage' | translate }}"
            [searchable]="false"
            [items]="filterLabels.garage"
            [(ngModel)]="activeFilters.garage"
            class="caracteristiques-filter">
          </ng-select> -->
        </div>

        <h5
          *ngIf="
            !(
              search.searchType == 'commercial' &&
              search.selectedInscriptionType == 2
            )
          "
        >
          {{ "library.search-full.filters.groups.crit" | translate }}
        </h5>
        <div
          class="custom-checkbox-group"
          *ngIf="search.searchType !== 'commercial'"
        >
          <input
            class="custom-checkbox"
            type="checkbox"
            id="checkbox-open-houses"
            [(ngModel)]="activeFilters.openhouses"
            (change)="submitFilters()"
            [ngModelOptions]="{ standalone: true }"
          />
          <label for="checkbox-open-houses"
            ><span>{{
              "library.search-full.filters.fields.openhouses" | translate
            }}</span></label
          >
        </div>
        <div
          class="custom-checkbox-group"
          *ngIf="
            search.selectedInscriptionType !== 2 &&
            search.searchType !== 'residential'
          "
        >
          <div>
            <input
              class="custom-checkbox"
              type="checkbox"
              id="checkbox-revenues"
              [(ngModel)]="activeFilters.types['NA']"
              (change)="submitFilters()"
              [ngModelOptions]="{ standalone: true }"
            />
            <label for="checkbox-revenues"
              ><span>{{
                "library.search-full.filters.revenues" | translate
              }}</span></label
            >
          </div>
        </div>
      </div>

      <!-- COLONNE 3 COMMERCIAL CRITÈRES -->
      <!-- <div
        class="filter-column"
        [hidden]="!(search.searchType !== 'residential')"
      >
        <div *ngIf="search.selectedInscriptionType !== 2">
          <h5>{{ "library.search-full.filters.groups.crit" | translate }}</h5>
          <div class="custom-checkbox-group">
            <div>
              <input
                class="custom-checkbox"
                type="checkbox"
                id="checkbox-revenues"
                [(ngModel)]="activeFilters.types['NA']"
                (change)="submitFilters()"
                [ngModelOptions]="{ standalone: true }"
              />
              <label for="checkbox-revenues"
                ><span>{{
                  "library.search-full.filters.revenues" | translate
                }}</span></label
              >
            </div>
          </div>
        </div>
      </div> -->
    </div>

    <!-- CONTRÔLES FILTRES -->
    <div class="filter-footer">
      <button class="main-button -secondary clear" (click)="resetFilters()">
        {{ "library.search-full.filters.filters-reset" | translate }}
        <span *ngIf="!isMobile" class="icon-refresh"></span>
      </button>
      <button class="main-button -primary apply" (click)="resultFilters()">
        {{ "library.search-full.filters.filters-submit" | translate }}
      </button>
    </div>
  </div>
</form>
