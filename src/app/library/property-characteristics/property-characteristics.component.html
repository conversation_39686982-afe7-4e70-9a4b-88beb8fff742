<div class="property-characteristics-cpn" *ngIf="characteristics">
	<h3 class="emphase-title">{{ "library.property-characteristics.title" | translate }}</h3>

	<div class="characteristics-ctn table-ctn -has-button">
		<div class="table-wrap">
			<div class="characteristics table-row -head" *ngFor="let characteristic of characteristics;">
				<p>{{characteristic.type}}</p>
				<p>
					<span *ngFor="let sub_type of characteristic.subtypecharacteristics;">{{sub_type.description}}</span>
				</p>
			</div>
		</div>

		<div class="gradient"></div>
		<a class="small-link" href="" (click)="onOpenTable($event)"><i class="icon-map-plus"></i> {{ "library.property-characteristics.btn" | translate }}</a>
	</div>
</div>
