<div class="properties-big-slider-cpn">

    <div class="swiper-container properties-list-ctn" #swiperContainer>
        <div class="swiper-wrapper properties-list-inner">

            <div class="swiper-slide properties" *ngFor="let property of properties;">
                <lib-property-big-slider [property]="property"></lib-property-big-slider>
            </div>

        </div>
    </div>

    <a class="main-button nav prev" #prevButton><i class="icon-arrow-left"></i></a>
    <a class="main-button nav next" #nextButton><i class="icon-arrow-right"></i></a>

    <div class="logo-remax-collection">
        <img src="assets/images/common/collection-remax_MAB.png" alt="RE/MAX Collection">
    </div>
</div>
