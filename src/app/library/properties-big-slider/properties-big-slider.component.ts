import {Component, OnInit, Input, ViewChild, ElementRef, AfterViewInit} from '@angular/core';
import Swiper, { Navigation } from 'swiper';

@Component({
  selector: 'lib-properties-big-slider',
  templateUrl: './properties-big-slider.component.html'
})

export class PropertiesBigSliderComponent implements OnInit, AfterViewInit {
  @Input() properties;
  propertiesBigSwiper: any;

  @ViewChild('swiperContainer', { static: false }) swiperContainer: ElementRef;
  @ViewChild('nextButton', { static: false }) nextButton: ElementRef;
  @ViewChild('prevButton', { static: false }) prevButton: ElementRef;

  constructor () { }

  ngOnInit () {
    this.initSlider();
  }

  initSlider () {
    Swiper.use([Navigation]);

    setTimeout(() => {
      this.propertiesBigSwiper = new Swiper(this.swiperContainer.nativeElement, {
        speed: 1000,
        loop: true,
        observer: true,
        simulateTouch: true,
        autoplay: true,
        navigation: {
          nextEl: this.nextButton.nativeElement,
          prevEl: this.prevButton.nativeElement
        },
        slidesPerView: 1,
        spaceBetween: 0,
      });
    }, 2500);
  }

  ngAfterViewInit(): void {
  }
}
