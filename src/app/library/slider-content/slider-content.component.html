<div class="slider-content-cpn">
    <div class="container">
        <h2 class="title">{{ 'library.slider-content.title' | translate }}</h2>
        <div class="swipers-ctn">
            <div class="swiper swiper-content-img">
                <div class="swiper-container">
                    <div class="swiper-wrapper">
                        <!-- Slides -->
                        <div class="swiper-slide">
                            <img src="assets/images/common/mab-outils-01.jpg">
                        </div>
                        <div class="swiper-slide">
                            <img src="assets/images/common/mab-outils-02.jpg">
                        </div>
                        <div class="swiper-slide">
                            <img src="assets/images/common/mab-outils-03.jpg">
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-push-4 col-md-8 col-lg-push-6 col-lg-6 content-col">
                    <div class="swiper swiper-content">
                        <div class="swiper-container">
                            <div class="swiper-wrapper">
                                <!-- Slides -->
                                <div class="swiper-slide" *ngFor="let slide of slidesContent; let i = index">
                                    <div class="head">
                                        <p class="title">{{slide.title}}</p>
                                        <div class="step">
                                            <p>{{i + 1}} / {{slidesContent.length}}</p>
                                        </div>
                                    </div>
                                    <p class="content" [innerHTML]="slide.text">
                                    </p>
                                </div>
                            </div>
                            <div class="swiper-nav">
                                <div class="swiper-button-prev swiper-btn"></div>
                                <div class="swiper-button-next swiper-btn"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
