import { Component, OnInit, Input } from '@angular/core';
import { CurrencyPipe } from '@angular/common';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'lib-neighborhood-avgcosts',
  templateUrl: './neighborhood-avgcosts.component.html'
})

export class NeighborhoodAvgcostsComponent implements OnInit {
  @Input() neighborhood;
  @Input() formatTitle: Function;

  public lineChartOptions: any;

  public lineChartColors: Array<any> = [
    { // grey

      backgroundColor: '#002758',
      borderColor: '#002758',
      pointBackgroundColor: '#002758',
      pointBorderColor: '#fff',
      pointHoverBackgroundColor: '#002758',
      pointHoverBorderColor: '#fff'
    },
    { // dark green
      backgroundColor: '#971A1A',
      borderColor: '#971A1A',
      pointBackgroundColor: '#971A1A',
      pointBorderColor: '#fff',
      pointHoverBackgroundColor: '#971A1A',
      pointHoverBorderColor: '#fff'
    },
    { // dark grey
      backgroundColor: '#004D9A',
      borderColor: '#004D9A',
      pointBackgroundColor: '#004D9A',
      pointBorderColor: '#fff',
      pointHoverBackgroundColor: '#004D9A',
      pointHoverBorderColor: '#fff'
    }
  ];

  constructor (private translate: TranslateService) { }

  ngOnInit () {
    const scope = this;
    const currencyPipe = new CurrencyPipe(scope.translate.currentLang);
    this.lineChartOptions = {
      responsive: true,
      multiTooltipTemplate: '<%=datasetLabel%> : <%= value %> $',
      scales: {
        yAxes: [{
          ticks: {
            userCallback: function (item) {
              return currencyPipe.transform(item, 'CAD', 'symbol-narrow', '2.0-0');
            }
          }
        }]
      },
      tooltips: {
        enabled: true,
        mode: 'single',
        callbacks: {
          label: function (tooltipItems, data) {
            return currencyPipe.transform(tooltipItems.yLabel, 'CAD', 'symbol-narrow', '2.0-0');
          }
        }
      }
    };
  }

  public chartHovered (e: any): void {
    // console.log(e);
  }
}
