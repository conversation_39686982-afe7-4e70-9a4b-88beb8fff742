<div class="neighborhood-avgcost-cpn" *ngIf="neighborhood?.averageCostData">
  <div class="container">
    <h3 class="single-title" *ngIf="neighborhood.averageCostData">
      {{ formatTitle(neighborhood.name, 'library.neighborhood-avgcost.title' | translate) }}
    </h3>

    <div class="chart-ctn">
      <ng-container>
        <canvas baseChart
                [chartType]="'bar'"
                [datasets]="neighborhood.averageCostData"
                [labels]="neighborhood.averageCostLabels"
                [options]="lineChartOptions"
                [colors]="lineChartColors"
                [legend]="true"
                (chartHover)="chartHovered($event)">
        </canvas>
      </ng-container>
    </div>

  </div>
</div>
