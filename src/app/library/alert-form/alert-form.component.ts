import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';

import { ReCaptchaV3Service } from 'ng-recaptcha';
import { Subscription } from 'rxjs';

import { AlertService } from '@/services/v3/alert/alert.service';
import { BlocksService } from '@/services/v3/contentblocks/contentblocks.service';
import { environment } from 'environments/environment';

@Component({
  selector: 'lib-alert-form',
  templateUrl: './alert-form.component.html'
})

export class AlertFormComponent implements OnInit {
  public currentStep: number = 1;

  // All cities, sorted by neighborhood
  citiesList = {
    null: [
      {value: "Boucherville"},
      {value: "Longueuil"},
      {value: "Saint-Lambert"},
      {value: "Saint-Bruno-de-Montarville"},
      {value: "Beloeil"},
      {value: "McMasterville"},
      {value: "Mont Saint-Hilaire"},
      {value: "Sainte-Julie"},
      {value: "Saint-Antoine-sur-Richelieu"},
      {value: "Saint-Charles-sur-Richelieu"},
      {value: "Saint-Marc-sur-Richelieu"},
      {value: "Contrecoeur"},
      {value: "Varennes"},
      {value: "Verchères"},
    ]
  };

  // Helper for neighborhood names
  neighborhoodList = Object.keys(this.citiesList);

  priceRange = [
    { id: 1, name: '1+' },
    { id: 2, name: '2+' },
    { id: 3, name: '3+' },
    { id: 4, name: '4+' },
    { id: 5, name: '5+' }
  ];

  propertyTypes: any = ['NA'];
  cities: any = ['NA'];

  alertForm: FormGroup;
  firstName: FormControl;
  lastName: FormControl;
  phone: FormControl;
  phoneExt: FormControl;
  email: FormControl;
  other: FormControl;
  budget: FormControl;

  successMessage: boolean = false;
  errorMessage: boolean = false;

  isTyping = false;
  formSend: boolean = false;
  formLoading: boolean = false;

  public blockTitle: string;
  public blockContent: string;

  // Recaptcha v3 Subscription
  private submitExecutionSubscription: Subscription

  constructor (
    private recaptchaV3Service: ReCaptchaV3Service,
    private formBuilder: FormBuilder,
    private alertService: AlertService,
    private blocksService: BlocksService
  ) {
    this.blocksService.getBlock('bloc-alerte-immobiliere').subscribe(data => {
      this.blockTitle = data.title;
      this.blockContent = data.text;
    });
  }

  ngOnInit () {
    this.createFormControls();
    this.createForm();
    this.delayRCDisplay();
  }

  delayRCDisplay () {
    setTimeout(() => {
      const result = document.getElementsByClassName('grecaptcha-badge');
      const recaptcha = result.item(0) as HTMLElement;
      recaptcha.style.visibility = 'visible';
    }, 2000);
  }

  ngOnDestroy () {
    const result = document.getElementsByClassName('grecaptcha-badge');
    const recaptcha = result.item(0) as HTMLElement;
    recaptcha.style.visibility = 'hidden';

    if (this.submitExecutionSubscription) {
      this.submitExecutionSubscription.unsubscribe();
    }
  }

  onPreviousStep ($event) {
    $event.preventDefault();
    this.currentStep--;
    setTimeout(() => document.getElementById('alertForm').scrollIntoView({ behavior: 'smooth' }), 100);
  }

  onNextStep ($event) {
    $event.preventDefault();
    this.currentStep++;
    setTimeout(() => document.getElementById('alertForm').scrollIntoView({ behavior: 'smooth' }), 100);
  }

  private createFormControls () {
    this.firstName = new FormControl('', Validators.required);
    this.lastName = new FormControl('', Validators.required);
    this.phone = new FormControl('', [
      Validators.required,
      Validators.pattern('[0-9]+'),
      Validators.minLength(10)
    ]);
    this.email = new FormControl('', [
      Validators.required,
      Validators.pattern('^\\w+([\\.-]?\\w+)*@\\w+([\\.-]?\\w+)*(\\.\\w{2,3})+$')
    ]);
    this.phoneExt = new FormControl('');
    this.other = new FormControl('');
    this.budget = new FormControl('');
  }

  private createForm () {
    this.alertForm = this.formBuilder.group({
      name: new FormGroup({
        firstName: this.firstName,
        lastName: this.lastName,
        phone: this.phone,
        phoneExt: this.phoneExt
      }),
      email: new FormGroup({
        email: this.email
      }),
      other: this.other,
      propertyTypes: this.propertyTypes,
      cities: this.cities,
      budget: this.budget
    });
  }

  onSubmit () {
    if (!this.alertForm.valid) return false;
    this.formLoading = true;

    this.submitExecutionSubscription = this.recaptchaV3Service.execute('submit').subscribe(token => {
      if (this.cities.length >= 2) this.cities.shift();
      if (this.propertyTypes.length >= 2) this.propertyTypes.shift();

      this.alertForm.value.cities = this.cities;
      this.alertForm.value.propertyTypes = this.propertyTypes;
      this.alertForm.value.token_captcha = token;

      if(environment.webHookZapier){
        // Send Data to Zapier
        fetch(environment.webHookZapier, {
          method: 'POST',
          body: JSON.stringify(this.alertForm.value),
        }).catch(err => console.error('Erreur Zapier :', err));
      }

      this.alertService.postAlert(this.alertForm.value).subscribe(response => {
        // console.log(response);

        this.formSend = true;
        this.formLoading = false;

        setTimeout(() => { document.getElementById('alertForm').scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'nearest' }); }, 100);

        if (response.success) this.successMessage = true;
        else this.errorMessage = true;
      });
    },
    error => console.error(error));
  }

  onChangeCities ({ target }) {
    if (target.checked) this.cities.push(target.value);
    else this.cities.splice(this.cities.indexOf(target.value), 1);
  }

  onChangePropertyTypes ({ target }) {
    if (target.checked) this.propertyTypes.push(target.value);
    else this.propertyTypes.splice(this.propertyTypes.indexOf(target.value), 1);
  }

  onChangeRange (event) {
    this.budget = event.name;
  }

  resetForm () {
    this.formSend = false;
    this.alertForm.reset();
    this.currentStep = 1;
  }

  retry () {
    this.formSend = false;
    this.currentStep = 1;
  }
}
