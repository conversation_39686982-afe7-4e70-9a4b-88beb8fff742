<div class="alert-form-cpn" id="alertForm">
	<div class="container -small" [ngClass]="{'send': formSend}">
		<form [formGroup]="alertForm" [ngClass]="{'loading-inner': formLoading}" class="alert-form" (ngSubmit)="onSubmit()">
			<div class="form-head">
				<h1 class="-page-title">{{ blockTitle }}</h1>
				<!--<div class="step">
					<p>{{ currentStep }} / 3</p>
				</div>-->
			</div>

			<!--<div id="step1" class="form-step"[class.show]="currentStep == 1">
				<div class="-page-description" [innerHtml]="blockContent"></div>

				<h3 class="emphase-title">{{ 'library.alert-form.step1.title' | translate }}</h3>

				<div class="location-ctn">
					<div *ngFor="let neighborhood of neighborhoodList; let i = index;">
						<ng-container *ngIf="neighborhood != 'null'">
							<div class="separator">
								<p class="emphase-title">{{ neighborhood }}</p>
							</div>
						</ng-container>

						<div class="custom-checkbox-group" *ngFor="let city of citiesList[neighborhood] | orderBy: 'value'; let j = index">
							<input class="custom-checkbox" (change)="onChangeCities($event)" type="checkbox" [id]="'custom-checkbox-'+i+''+j" [value]="city.value">
							<label [for]="'custom-checkbox-'+i+''+j">{{ city.value }}</label>
						</div>
					</div>
				</div>

				<div class="button-ctn">
					<a class="main-button -primary" (click)="onNextStep($event)">{{ 'library.alert-form.next' | translate }}</a>
				</div>
			</div>-->

			<!--<div id="step2" class="form-step"[class.show]="currentStep == 2">
				<h3 class="emphase-title">{{ 'library.alert-form.step2.title' | translate }}</h3>

				<div class="type-ctn">
					<div class="custom-checkbox-group">
						<input class="custom-checkbox" (change)="onChangePropertyTypes($event)" type="checkbox" id="type-checkbox-0" value="{{ 'library.alert-form.bungalow' | translate }}">
						<label for="type-checkbox-0">{{ 'library.alert-form.bungalow' | translate }}</label>
					</div>
					<div class="custom-checkbox-group">
						<input class="custom-checkbox" (change)="onChangePropertyTypes($event)" type="checkbox" id="type-checkbox-1" value="{{ 'library.alert-form.semi-detached-bungalow' | translate }}">
						<label for="type-checkbox-1">{{ 'library.alert-form.semi-detached-bungalow' | translate }}</label>
					</div>
					<div class="custom-checkbox-group">
						<input class="custom-checkbox" (change)="onChangePropertyTypes($event)" type="checkbox" id="type-checkbox-2" value="Condo">
						<label for="type-checkbox-2">Condo</label>
					</div>
					<div class="custom-checkbox-group">
						<input class="custom-checkbox" (change)="onChangePropertyTypes($event)" type="checkbox" id="type-checkbox-3" value="{{ 'library.alert-form.undivided-co-ownership' | translate }}">
						<label for="type-checkbox-3">{{ 'library.alert-form.undivided-co-ownership' | translate }} </label>
					</div>
					<div class="custom-checkbox-group">
						<input class="custom-checkbox" (change)="onChangePropertyTypes($event)" type="checkbox" id="type-checkbox-4" value="Cottage">
						<label for="type-checkbox-4">Cottage</label>
					</div>
					<div class="custom-checkbox-group">
						<input class="custom-checkbox" (change)="onChangePropertyTypes($event)" type="checkbox" id="type-checkbox-5" value="{{ 'library.alert-form.semi-detached-cottage' | translate }}">
						<label for="type-checkbox-5">{{ 'library.alert-form.semi-detached-cottage' | translate }}</label>
					</div>
					<div class="custom-checkbox-group">
						<input class="custom-checkbox" (change)="onChangePropertyTypes($event)" type="checkbox" id="type-checkbox-6" value="Duplex">
						<label for="type-checkbox-6">Duplex</label>
					</div>
					<div class="custom-checkbox-group">
						<input class="custom-checkbox" (change)="onChangePropertyTypes($event)" type="checkbox" id="type-checkbox-7" value="{{ 'library.alert-form.income-property' | translate }}">
						<label for="type-checkbox-7">{{ 'library.alert-form.income-property' | translate }}</label>
					</div>
					<div class="custom-checkbox-group">
						<input class="custom-checkbox" (change)="onChangePropertyTypes($event)" type="checkbox" id="type-checkbox-8" value="{{ 'library.alert-form.commercial-building' | translate }}">
						<label for="type-checkbox-8">{{ 'library.alert-form.commercial-building' | translate }}</label>
					</div>
					<div class="custom-checkbox-group">
						<input class="custom-checkbox" (change)="onChangePropertyTypes($event)" type="checkbox" id="type-checkbox-9" value="{{ 'library.alert-form.townhouse' | translate }}">
						<label for="type-checkbox-9">{{ 'library.alert-form.townhouse' | translate }}</label>
					</div>
					<div class="custom-checkbox-group">
						<input class="custom-checkbox" (change)="onChangePropertyTypes($event)" type="checkbox" id="type-checkbox-10" value="{{ 'library.alert-form.quadruplex' | translate }}">
						<label for="type-checkbox-10">{{ 'library.alert-form.quadruplex' | translate }}</label>
					</div>
					<div class="custom-checkbox-group">
						<input class="custom-checkbox" (change)="onChangePropertyTypes($event)" type="checkbox" id="type-checkbox-11" value="{{ 'library.alert-form.split-level' | translate }}">
						<label for="type-checkbox-11">{{ 'library.alert-form.split-level' | translate }}</label>
					</div>
					<div class="custom-checkbox-group">
						<input class="custom-checkbox" (change)="onChangePropertyTypes($event)" type="checkbox" id="type-checkbox-12" value="{{ 'library.alert-form.plot' | translate }}">
						<label for="type-checkbox-12">{{ 'library.alert-form.plot' | translate }}</label>
					</div>
					<div class="custom-checkbox-group">
						<input class="custom-checkbox" (change)="onChangePropertyTypes($event)" type="checkbox" id="type-checkbox-13" value="Triplex">
						<label for="type-checkbox-13">Triplex</label>
					</div>
					<div class="custom-checkbox-group">
						<input class="custom-checkbox" (change)="onChangePropertyTypes($event)" type="checkbox" id="type-checkbox-14" value="{{ 'library.alert-form.business-sale' | translate }}">
						<label for="type-checkbox-14">{{ 'library.alert-form.business-sale' | translate }}</label>
					</div>
				</div>

				<div class="input-ctn -full">
					<label>{{ 'library.alert-form.step2.other_label' | translate }}</label>
					<input type="text" name="other" formControlName="other" >
				</div>

				<div class="input-ctn -half">
					<label>{{ 'library.alert-form.step2.budget_label' | translate }}</label>
					<input type="text" name="budget" formControlName="budget" >
				</div>

				<div class="button-ctn">
					<a class="main-button -previous" (click)="onPreviousStep($event)">{{ 'library.alert-form.before' | translate }}</a>
					<a class="main-button -primary" (click)="onNextStep($event)">{{ 'library.alert-form.next' | translate }}</a>
				</div>
			</div>-->

			<div id="step3" class="form-step" [class.show]="currentStep == 1">
				<h3 class="emphase-title">{{ 'library.alert-form.step3.title' | translate }}</h3>

				<div class="form-row form-group" formGroupName="name">
					<div class="input-ctn -dual">
						<label>{{ 'library.alert-form.step3.firstname' | translate }}</label>
						<input type="text" class="form-control" [ngClass]="{'-error': firstName.invalid && (firstName.dirty || firstName.touched)}" formControlName="firstName" name="first_name">
						<div class="form-control-feedback"
						*ngIf="firstName.errors && (firstName.dirty || firstName.touched)">
							<p>{{ "library.alert-form.errors.firstname_required" | translate }}</p>
						</div>
					</div>
					<div class="input-ctn -dual">
						<label>{{ 'library.alert-form.step3.name' | translate }}</label>
						<input type="text" class="form-control" [ngClass]="{'-error': lastName.invalid && (lastName.dirty || lastName.touched)}" formControlName="lastName" name="last_name">
						<div class="form-control-feedback"
						*ngIf="lastName.errors && (lastName.dirty || lastName.touched)">
							<p *ngIf="lastName.errors.required">{{ "library.alert-form.errors.lastname_required" | translate }}</p>
						</div>
					</div>
					<div class="form-row -bottom">
						<div class="input-ctn -dual">
							<label>{{ 'library.alert-form.step3.phone' | translate }}</label>
							<input type="text" class="form-control" mask="************" [ngClass]="{'-error': phone.invalid && (phone.dirty || phone.touched)}" formControlName="phone" name="phone">
						</div>
						<div class="input-ctn -dual -small">
							<label></label>
							<input type="text" name="ext" class="form-control" formControlName="phoneExt"  placeholder="Ext">
						</div>
						<div class="form-control-feedback"
						*ngIf="phone.errors && (phone.dirty || phone.touched)">
							<p *ngIf="phone.errors.required">{{ "library.alert-form.errors.phone_required" | translate }}</p>
							<p *ngIf="phone.errors.pattern">{{ "library.alert-form.errors.phone_invalid" | translate }}</p>
							<p *ngIf="phone.errors.minlength">{{ "library.alert-form.errors.phone_invalid" | translate }}</p>
						</div>
					</div>
				</div>
				<div class="form-row">
					<div class="input-ctn form-group" formGroupName="email">
						<label>{{ 'library.alert-form.step3.email' | translate }}</label>
						<input type="email" class="form-control" (focus)="isTyping = true" (blur)="isTyping = false" [ngClass]="{'-error': email.invalid && (email.dirty || email.touched) && !isTyping}" formControlName="email" name="email">
						<div class="form-control-feedback"
						*ngIf="email.errors && (email.dirty || email.touched) && !isTyping">
							<p *ngIf="email.errors.required">{{ "library.alert-form.errors.email_required" | translate }}</p>
							<p *ngIf="email.errors.pattern">{{ "library.alert-form.errors.email_invalid" | translate }}</p>
						</div>
					</div>
				</div>
                <div class="input-ctn -full">
                    <label>{{ 'library.alert-form.step3.other' | translate }}</label>
                    <input type="text" name="other" formControlName="other" >
                </div>

                <p class="warning-message">{{ 'library.alert-form.warning_message' | translate }}</p>
        <div class="button-ctn">
          <!--<a class="main-button -previous" (click)="onPreviousStep($event)">{{ 'library.alert-form.before' | translate }}</a>-->
          <button class="main-button -primary" [disabled]="!alertForm.valid" type="submit"><span>{{ "library.alert-form.submit" | translate }}</span></button>
        </div>
        <p class="-page-required">{{ "library.alert-form.required" | translate }}</p>
			</div>
		</form>

		<div *ngIf="formLoading" class="form-loader">
			<div class="loading-circle"></div>
		</div>

		<div class="form-response" [ngClass]="{'show': formSend}">
		  <h1 class="-page-title">{{ 'library.alert-form.title' | translate }}</h1>

      <ng-container *ngIf="successMessage">
        <p class="message">{{ "library.alert-form.submit-message" | translate }}</p>
        <div class="button-ctn">
        <a class="main-button -primary" (click)="resetForm()">{{ 'library.alert-form.back' | translate }}</a>
        </div>
      </ng-container>

      <ng-container *ngIf="errorMessage">
        <p class="message">{{ "library.alert-form.errors.submit-message" | translate }}</p>
        <div class="button-ctn">
        <a class="main-button -primary" (click)="retry()">{{ 'library.alert-form.back' | translate }}</a>
        </div>
      </ng-container>
		</div>
	</div>
</div>
