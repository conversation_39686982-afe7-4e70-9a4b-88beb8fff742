import { Component, OnInit, Input, ElementRef } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'lib-properties',
  templateUrl: './properties.component.html'
})

export class PropertiesComponent implements OnInit {
  @Input() property;
  @Input() rentalSearch = false; // Used to hide rental label when search type is 'rental'

  public currentLang: any;
  propertyUrl: any;

  constructor (
    private hostElement: ElementRef,
    public translate: TranslateService
  ) {
    const userLang = window.location.pathname.split('/')[1];
    this.currentLang = userLang || 'fr';
  }

  ngOnInit () {
    if (this.property.neighborhood_slug) {
      this.propertyUrl = '/' + this.property.municipality_slug +
        '/' + this.property.neighborhood_slug +
        '/' + this.property.ext_address_slug +
        '/' + this.property.mls;
    } else {
      this.propertyUrl = '/' + this.property.municipality_slug +
        '/' + this.property.ext_address_slug +
        '/' + this.property.mls;
    }

    // Build display address string
    const { address_civic_start: civic1, address_civic_end: civic2, address_street: street, address_apt: apt } = this.property;
    this.property.displayAddress =
      (civic1 || '') +
      (civic1 && civic2 ? ' - ' + civic2 : '') +
      (street ? ' ' + street : '') +
      (apt ? ' ' + this.translate.instant('library.property-details.apt') + '.' + apt : '');
  }

  ngAfterViewInit () {
    const elems = this.hostElement.nativeElement.querySelectorAll('.jsProperty');
    const scope = this;
    const currentElem = Array.prototype.slice.call(elems)[0];

    currentElem.addEventListener('mouseover', function () {
      const addressHeight = Array.prototype.slice.call(scope.hostElement.nativeElement.querySelectorAll('.jsProperty .address'))[0].offsetHeight;
      const alignHeight = Array.prototype.slice.call(scope.hostElement.nativeElement.querySelectorAll('.jsProperty .align'))[0].offsetHeight;
      const maxHeight = alignHeight + addressHeight + Array.prototype.slice.call(scope.hostElement.nativeElement.querySelectorAll('.jsProperty .bloc-head'))[0].offsetHeight;
      Array.prototype.slice.call(scope.hostElement.nativeElement.querySelectorAll('.jsProperty .properties-info'))[0].style.maxHeight = maxHeight + 'px';
    });

    currentElem.addEventListener('mouseleave', function () {
      Array.prototype.slice.call(scope.hostElement.nativeElement.querySelectorAll('.jsProperty .properties-info'))[0].style.maxHeight = Array.prototype.slice.call(scope.hostElement.nativeElement.querySelectorAll('.jsProperty .bloc-head'))[0].offsetHeight + 'px';
    });
  }

  setImageSize ({ target }) {
    const img = target as HTMLImageElement;
    img.width = img.naturalWidth;
    img.height = img.naturalHeight;
  }
}
