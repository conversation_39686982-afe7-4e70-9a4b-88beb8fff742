<div class="location-pane-cpn neighborhood-pane-cpn container-fluid" [class.open]="settings.propertiesPaneIsOpen" [class.show]="showMapOnly">

	<a class="toggle-pane" (click)="togglePanel()">
		<div><span class="icon-arrow-left"></span></div>
	</a>

	<div class="pane-content-ctn">
		<h1 class="pane-title">{{ 'library.neighborhood-map.neighborhood-pane.title' | translate }}</h1>

		<div class="hidden-links" style="display: none;">
			<a *ngFor="let neighborhood of features" [routerLink]="['urls.neighborhoods' | translate, neighborhood.properties.id]">{{ neighborhood.properties.name }}</a>
		</div>

		<!-- <div class="dropdown more-filter" dropdown>
			<div class="dropdown-wrapper" dropdown-not-closable-zone>
				<ng-select
					[searchable]="false"
					[items]="features"
					bindLabel="properties.name"
					bindValue="properties.id"
					[(ngModel)]="search.feature"
					(change)="onChangeFeatures($event)"
					placeholder="{{ 'library.neighborhood-map.neighborhood-pane.placeholder' | translate }}" 
          			class="rooms-filter">
				</ng-select>
			</div>
		</div> -->

		<div class="info-ctn">
			<p class="sub-section-title">{{ 'library.neighborhood-map.neighborhood-pane.description' | translate }}</p>
		</div>

		<div class="selection-list">
			<a
			  *ngFor="let neighborhood of features"
			  attr.data-id="{{ neighborhood.properties.id }}"
			  (click)="onChangeFeatures(neighborhood.properties.id)"
			  [routerLink]="['urls.neighborhoods' | translate, neighborhood.properties.id]"
			  class="neighborhood-card"
			  [(ngModel)]="search.feature"
			  [ngClass]="{ active: search.feature === neighborhood.properties.id }"
			>
				<img src="{{ neighborhood.properties.header_image }}" alt="" />
				<div class="link-ctn">
					<p>{{ neighborhood.properties.name }}</p>
				</div>
			</a>
		  </div>
	</div>
</div>