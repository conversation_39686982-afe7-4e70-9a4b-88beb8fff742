<div class="property-sheet container" *ngIf="property">
	<div class="property-sheet-content col-sm-8">
		<lib-property-description [property]='property'></lib-property-description>
		<div *ngIf="detectMobile" class="property-mobile-sidebar">
			<div *ngFor="let broker of brokers;">
				<lib-property-broker-card-1 [broker]='broker'></lib-property-broker-card-1>
			</div>
			<lib-property-share [shareUrl]='shareUrl'></lib-property-share>
		</div>
        <lib-property-details [property]='property'></lib-property-details>
        <lib-property-inclusion [included]='property.included' [excluded]='property.excluded'></lib-property-inclusion>
        <lib-property-addenda [addenda]='addenda'></lib-property-addenda>
        <lib-property-characteristics [characteristics]='characteristics'></lib-property-characteristics>
		<lib-property-openhouse [openHouses]="openHouses" [address]='property.ext_address' [mls]='property.mls'></lib-property-openhouse>
		<lib-property-rooms [rooms]='rooms'></lib-property-rooms>
		<lib-property-downloads [documentsList]='documentsList'></lib-property-downloads>
	</div>
	<div *ngIf="!detectMobile" class="property-sheet-sidebar col-sm-4">
		<div *ngFor="let broker of brokers;">
			<lib-property-broker-card-1 [broker]='broker'></lib-property-broker-card-1>
		</div>
		<lib-property-share></lib-property-share>
	</div>
	<lib-property-form-contact [mls]="property.mls"></lib-property-form-contact>
</div>
