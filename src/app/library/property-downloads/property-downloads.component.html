<div class="property-downloads-cpn" *ngIf="documentsList?.length > 0">
	<h3 class="emphase-title">{{ 'library.property-downloads.title' | translate }}</h3>
	<div class="downloadlist-ctn table-ctn -between -center">
		<div class="downloads table-row" *ngFor="let document of documentsList">
			<p><span>{{ document.title }}</span></p>
			<a href="{{ document.path }}" target="_blank" download class="main-button -primary-small">{{ 'library.property-downloads.button' | translate }}</a>
		</div>
	</div>
</div>
