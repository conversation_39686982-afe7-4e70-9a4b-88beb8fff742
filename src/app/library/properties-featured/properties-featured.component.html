<div class="properties-featured-cpn">
    <div class="container">
        <div class="row">
            <div class="col-md-3">
                <h2 class="title">{{ 'library.properties-featured.title' | translate }}</h2>
                <div class="description">
                    <p>{{ 'library.properties-featured.text' | translate }}</p>
                    <a class="small-link right" [routerLink]="['urls.search-properties' | translate ]">{{ 'library.properties-featured.btn' | translate }}<i class="icon-arrow-right"></i></a>
                </div>

            </div>
            <div class="col-md-8 col-md-push-1 col-sm-12 slider-container">
                <div class="properties-featured-swiper swiper-container">
                    <div class="swiper-wrapper">
                        <!-- Slides -->
                        <div class="swiper-slide" *ngFor="let property of properties">
                            <lib-property-featured [property]="property"></lib-property-featured>
                        </div>
                    </div>
                    <div class="swiper-button-prev swiper-btn" [ngClass]="{hidden: properties.length == 1}"><i></i></div>
                    <div class="swiper-button-next swiper-btn" [ngClass]="{hidden: properties.length == 1}"><i></i></div>
                </div>
            </div>
        </div>
    </div>
</div>
