import { Component, OnInit, Input } from '@angular/core';
import Swiper from 'swiper';

@Component({
  selector: 'lib-properties-featured',
  templateUrl: './properties-featured.component.html'
})

export class PropertiesFeaturedComponent implements OnInit {
  @Input() properties;

  constructor () { }

  ngOnInit () {}

  ngAfterViewInit () {
    this.initSlider();
  }

  initSlider () {
    setTimeout(() => {
      const swiper = new Swiper('.properties-featured-swiper.swiper-container', {
        speed: 1000,
        observer: true,
        simulateTouch: false,
        autoplay: true,
        runCallbacksOnInit: false,
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        }
      });
    }, 1000);
  }
}
