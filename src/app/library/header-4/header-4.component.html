<lib-header-panel></lib-header-panel>

<header id="header4" [ngClass]="{'transparent': transparent === 'true', 'special': specialColor === 'true'}">
	<div class="main-header">
		<div class="logo-ctn" itemscope itemtype="http://schema.org/Organization"                                               >
			<a itemprop="logo" [routerLink]="['urls.home' | translate]"><img src="assets/images/common/main-logo.svg" alt="{{ 'client.name' | translate }} {{ 'library.header.alt-image' | translate }}"></a>			
			
			<li class="number-ctn" itemscope itemtype="http://schema.org/ContactPoint">
				<a itemprop="telephone" href="tel:{{ 'client.phone' | translate }}"><i class="icon-phone"></i>{{ 'client.phone' | translate }}</a>
			</li>
		</div>
		<div class="main-menu-ctn">
			<nav class="main-menu">
				<li class="item-menu" routerLinkActive="active-child" [routerLinkActiveOptions]="{exact: true}">
					<a>{{ "library.header.team" | translate }} <i class="icon-dropdown-menu"></i></a>
					<ul class="secondary-ul">
						<li>
							<a [routerLink]="['urls.real-estate-agents' | translate]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.our-team" | translate }} (1)</a>
						</li>
						<li>
						  <a [routerLink]="['urls.real-estate-agents-2' | translate]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.our-team" | translate }} (2)</a>
						</li>
						<li>
						  <a [routerLink]="['urls.real-estate-agents-3' | translate]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.our-team" | translate }} (3)</a>
						</li>
						<li>
						  <a [routerLink]="['urls.real-estate-agents-4' | translate]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.our-team" | translate }} (4)</a>
						</li>
						<li>
						  <a [routerLink]="['urls.real-estate-agents-5' | translate]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.our-team" | translate }} (5)</a>
						</li>
						<li>
						  <a [routerLink]="['urls.real-estate-agents-6' | translate]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.our-team" | translate }} (6)</a>
						</li>
						<li>
							<a [routerLink]="['urls.specialists' | translate]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.specialists" | translate }}</a>
						</li>
						<li>
							<a [routerLink]="['urls.testimonials' | translate]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.testimonials" | translate }}</a>
						</li>
			            <li>
			              <a [routerLink]="['urls.career' | translate]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.career" | translate }}</a>
			            </li>
					</ul>
				</li>
			   <!--  <li class="item-menu">
					<a href="">{{ "library.header.neighborhoods" | translate }}</a>
				</li> -->
				<li class="item-menu" routerLinkActive="active-child" [routerLinkActiveOptions]="{exact: true}">
					<a>{{ "library.header.properties" | translate }}  <i class="icon-dropdown-menu"></i></a>
					<div class="secondary-ul multi-column">
						<ul>
							<li class="item-link">
								<a [routerLink]="['urls.search-properties' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.search-properties" | translate }}</a>
							</li>
							<li class="item-link">
								<a [routerLink]="['urls.property-group' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.property-groups" | translate }}</a>
							</li>
							<li class="item-link">
								<a [routerLink]="['urls.neighborhoods' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.neighborhoods" | translate }}</a>
							</li>
						</ul>
						<lib-header-last-properties></lib-header-last-properties>
					</div>
				</li>
				<li class="item-menu" routerLinkActive="active-child" [routerLinkActiveOptions]="{exact: true}">
					<a>{{ "library.header.buy" | translate }}  <i class="icon-dropdown-menu"></i></a>
					<ul class="secondary-ul">
						<li class="item-link">
							<a [routerLink]="['urls.real-estate-alert' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.alert" | translate }}</a>
						</li>
						<li class="item-link">
							<a [routerLink]="['urls.buy-house' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.buying-tips" | translate }}</a>
						</li>
						<li class="item-link">
							<a [routerLink]="['urls.open-house' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.open-house" | translate }}</a>
						</li>
					</ul>
				</li>
				<li class="item-menu" routerLinkActive="active-child" [routerLinkActiveOptions]="{exact: true}">
					<a>{{ "library.header.sell" | translate }}  <i class="icon-dropdown-menu"></i></a>
					<ul class="secondary-ul">
						<li class="item-link">
							<a [routerLink]="['urls.real-estate-online-evaluation' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.evaluate-online" | translate }}</a>
						</li>
						<li class="item-link">
							<a [routerLink]="['urls.sell-house' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.selling-tips" | translate }}</a>
						</li>
						<li class="item-link">
							<a [routerLink]="['urls.home-staging' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.home-staging" | translate }}</a>
						</li>
					</ul>
				</li>
				<li class="item-menu">
					<a [routerLink]="['urls.real-estate-blog' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.blog" | translate }}</a>
				</li>

				<li class="item-menu">
					<a [routerLink]="['urls.contact' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.contact" | translate }}</a>
				</li>

				<li class="item-menu -lang">
					<a class="switch" (click)="switchLang(translate.currentLang == 'fr' ? 'en' : 'fr')">{{ "global.switchlang" | translate }}</a>
					<a class="english-fake" href="/{{ translate.currentLang == 'fr' ? 'en' : 'fr' }}"></a>
				</li>
				<li class="item-menu favorite">
					<a [routerLink]="['urls.favorites' | translate]"><i class="icon-favorite"></i> <span id="favorite-number">0</span></a>
				</li>
				<li class="item-menu">
					<img class="special-logo" src="assets/images/common/kryzalid.svg" alt="">
				</li>
			</nav>
		</div>

		<a href="#" class="header-menu-toggle" (click)="onOpenPanel($event)"><span></span> <span></span> <span></span></a>

	</div>
</header>
