<div *ngIf="teamMember" class="team-card-wrap team-card-cpn" itemscope itemtype="http://schema.org/Person">
	<div class="team-card-1">
		<div class="card-content">
			<div class="img-ctn">
				<ng-template [ngIf]="teamMember.photo" [ngIfElse]="defaultImage">
				<img itemprop="image" src="{{teamMember.photo}}" alt="{{teamMember.firstname}} {{teamMember.lastname}} - {{teamMember.job}}">
				</ng-template>

				<ng-template #defaultImage>
				<img itemprop="image" src="/assets/images/placeholder/default_image.jpeg" alt="">
				</ng-template>
			</div>
			<div class="info-ctn">
				<p itemprop="name" class="name" *ngIf="teamMember.firstname && teamMember.lastname">{{teamMember.lastname}} {{teamMember.lastname}}</p>
				<p itemprop="jobTitle" class="role" *ngIf="teamMember.job">{{teamMember.job}}</p>
				<p itemprop="knowsLanguage" *ngIf="teamMember.languages_value" class="lang">{{ 'library.team-card-1.spoken' | translate }} {{teamMember.languages_value}}</p>
				<a itemprop="telephone" *ngIf="teamMember.phone" href="tel:{{teamMember.phone}}" class="number"><i class="icon-mobile"></i>{{teamMember.phone}}</a>
				<a itemprop="email" *ngIf="teamMember.email" href="mailto:{{teamMember.email}}" class="social-icon icon-logo-mail"></a>
				<a *ngIf="teamMember.facebook" target="_blank" href="{{teamMember.facebook}}" class="social-icon icon-logo-facebook"></a>
			    <a *ngIf="teamMember.linkedin" target="_blank" href="{{teamMember.linkedin}}" class="social-icon icon-logo-linkedin"></a>
			    <a *ngIf="teamMember.instagram" target="_blank" href="{{teamMember.instagram}}" class="social-icon icon-logo-instagram"></a>
			    <a *ngIf="teamMember.youtube" target="_blank" href="{{teamMember.youtube}}" class="social-icon icon-logo-youtube"></a>
			    <a *ngIf="teamMember.twitter" target="_blank" href="{{teamMember.twitter}}" class="social-icon icon-x"></a>
			</div>
		</div>
		<a class="main-button -primary" href="">{{ 'library.team-card-1.join' | translate }} {{teamMember.first_name}} {{teamMember.last_name}}</a>
	</div>
</div>
