<div class="broker-contact-header-1-cpn">
	<div class="container">

		<div class="contact-form-ctn no-padding col-sm-6">

      <h1 class="-page-title">{{ blockTitle }}</h1>
      <div class="-page-description" [innerHtml]="blockContent"></div>
      <p class="-page-required">{{ "library.broker-contact-1.required" | translate }}</p>

      <lib-broker-contact-form [narrow]="true"></lib-broker-contact-form>
    </div>

        <div class="info-box-ctn col-sm-5 col-sm-offset-1">
            <div class="info-box" itemscope itemtype="http://schema.org/Organization">
                <img itemprop="image" src="/assets/images/common/home-marc-andre-bourdon.png" alt="{{ 'library.broker-contact-1.info.title' | translate }} {{ 'library.broker-contact-1.info.alt-image' | translate }}">
                <div class="info-text-ctn">
                    <div class="logo-ctn">
                        <img src="/assets/images/common/remax-signature-MAB.svg" alt="Remax Signature Marc André <PERSON>urdon">
                    </div>
                    <div class="location-ctn block">
                        <i class="icon-pin"></i>
                        <div itemprop="address" itemscope itemtype="http://schema.org/PostalAddress" class="text">
                            <p itemprop="streetAddress">{{ 'library.broker-contact-1.info.adress' | translate }}</p>
                            <p itemprop="addressLocality">{{ 'library.broker-contact-1.info.state' | translate }}</p>
                            <div class="links">
                                <a itemprop="location" href="{{ 'client.map.url' | translate}}" target="_blank">{{ 'library.broker-contact-1.info.see-maps' | translate }}</a>
                                <a itemprop="url" href="{{ 'client.map.url' | translate}}" target="_blank">{{ 'library.broker-contact-1.info.see-direction' | translate }}</a>
                            </div>
                        </div>
                    </div>
                    <div class="email-ctn block">
                        <i class="icon-mail"></i>
                        <div class="text">
                            <a itemprop="email" href="mailto:{{ 'library.broker-contact-1.info.email' | translate }}">{{ 'library.broker-contact-1.info.email' | translate }}</a>
                        </div>
                    </div>
                    <div class="phone-ctn block">
                        <i class="icon-phone"></i>
                        <div class="text">
                            <a itemprop="telephone" href="tel:{{ 'client.phone' | translate }}">{{ 'client.phone' | translate }}</a>
                            <a itemprop="telephone" href="tel:{{ 'client.phone2' | translate }}">{{ 'client.phone2' | translate }}</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

	</div>
</div>
