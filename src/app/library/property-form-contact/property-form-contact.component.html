<div class="full-screen-cpn" id="contact-pop-up">
  <div class="background" (click)="onCloseFullScreen()"></div>
  <div class="property-contact-form no-padding col-xs-12 col-sm-10 col-lg-7">
    <div class="property-contact-form-ctn" [ngClass]="{'send': formSend}">
      <a class="close-contact" (click)="onCloseFullScreen()">
        <span class="icon-close"></span>
      </a>
      <div [ngClass]="{'loading-inner': formLoading}">
        <div class="col-sm-4">
          <div class="property-contact-form-broker-card">
            <div class="img-ctn" id="dynamic-broker-image-ctn">
              <img id="dynamic-broker-image" src="" alt="">
            </div>
            <div class="broker-info-ctn">
              <p class="name" id="dynamic-broker-name"></p>
              <p class="role" id="dynamic-broker-role"></p>
              <a href="" class="number" id="dynamic-broker-phone"><i class="icon-mobile"></i></a>
            </div>
          </div>
        </div>
        <div class="col-sm-7 col-sm-push-1">
          <form class="contact-form" [formGroup]="contactForm" (ngSubmit)="onSubmit()">
            <div class="form-row">
              <div class="input-ctn -dual">
                <input type="text" name="name" formControlName="firstName" [ngClass]="{'-error': firstName.invalid && (firstName.dirty || firstName.touched)}" placeholder="{{ 'library.property-form-contact.firstname' | translate }}">
                <div class="form-control-feedback"
                    *ngIf="firstName.errors && (firstName.dirty || firstName.touched)">
                  <p>{{ "library.property-form-contact.errors.firstname_required" | translate }}</p>
                </div>
              </div>
              <div class="input-ctn -dual">
                <input type="text" name="name" formControlName="lastName" [ngClass]="{'-error': lastName.invalid && (lastName.dirty || lastName.touched)}" placeholder="{{ 'library.property-form-contact.lastname' | translate }}">
                <div class="form-control-feedback"
                    *ngIf="lastName.errors && (lastName.dirty || lastName.touched)">
                  <p>{{ "library.property-form-contact.errors.lastname_required" | translate }}</p>
                </div>
              </div>
            </div>
            <div class="form-row">
              <div class="input-ctn">
                <input type="text" name="email" (focus)="isTyping = true" (blur)="isTyping = false" [ngClass]="{'-error': email.invalid && (email.dirty || email.touched) && !isTyping}" formControlName="email" placeholder="{{ 'library.property-form-contact.email' | translate }}">
                <div class="form-control-feedback"
                    *ngIf="email.errors && (email.dirty || email.touched) && !isTyping">
                  <p *ngIf="email.errors.required">{{ "library.property-form-contact.errors.email_required" | translate }}</p>
                  <p *ngIf="email.errors.pattern">{{ "library.property-form-contact.errors.email_invalid" | translate }}</p>
                </div>
              </div>
            </div>
            <div class="form-row">
              <div class="input-ctn">
                <input type="text" mask="************" [ngClass]="{'-error': phone.invalid && (phone.dirty || phone.touched)}" formControlName="phone" name="phone" placeholder="{{ 'library.property-form-contact.phone' | translate }}">
                <div class="form-control-feedback"
                    *ngIf="phone.errors && (phone.dirty || phone.touched)">
                  <p *ngIf="phone.errors.required">{{ "library.property-form-contact.errors.phone_required" | translate }}</p>
                  <p *ngIf="phone.errors.pattern">{{ "library.property-form-contact.errors.phone_invalid" | translate }}</p>
                  <p *ngIf="phone.errors.minlength">{{ "library.property-form-contact.errors.phone_invalid" | translate }}</p>
                </div>
              </div>
            </div>
            <div class="form-row">
              <div class="input-ctn datepicker-ctn">
                  <input angular-mydatepicker
                    class="datepicker"
                    placeholder="{{ 'library.property-form-contact.placeholder-date' | translate }}" 
                    #dp="angular-mydatepicker"
                    name="mydate" 
                    (click)="dp.toggleCalendar()" 
                    (dateChanged)="changeDate($event)"
                    [(ngModel)]="selectedDate"
                    [ngModelOptions]="{standalone: true}"
                    [locale]="translate.currentLang"
                    [options]="myDatePickerOptions" />
              </div>
            </div>
            <div class="form-row" *ngIf="hoursIsShow">
              <div class="input-ctn">
                <ng-select
                  [searchable]="true"
                  [items]="timeRange"
                  [(ngModel)]="selectedTime"
                  [ngModelOptions]="{standalone: true}"
                  bindLabel="name"
                  bindValue="value"
                  placeholder="{{ 'library.property-form-contact.placeholder-time' | translate }}" class="rooms-filter align-center">
                </ng-select>
              </div>
            </div>
            <div class="form-row">
              <div class="input-ctn">
                <textarea name="message" placeholder="{{ 'library.property-form-contact.message' | translate }}" [ngClass]="{'-error': message.invalid && (message.dirty || message.touched)}" formControlName="message"></textarea>
                <div class="form-control-feedback"
                    *ngIf="message.errors && (message.dirty || message.touched)">
                  <p *ngIf="message.errors.required">{{ "library.property-form-contact.errors.message_required" | translate }}</p>
                </div>
              </div>
            </div>
            <div class="button-ctn">
              <button class="main-button -primary" [disabled]="!contactForm.valid" type="submit"><span>{{ "library.property-form-contact.submit" | translate }}</span></button>
            </div>
          </form>
        </div>
      </div>

      <div *ngIf="formLoading" class="form-loader">
        <div class="loading-circle"></div>
      </div>
    </div>

    <div class="form-response" [ngClass]="{'show': formSend, 'loading': formLoading}" >
      <a class="close-contact" (click)="resetForm()">
        <span class="icon-close"></span>
      </a>
      <ng-container *ngIf="successMessage">
        <h1 class="-page-title">{{ 'library.property-form-contact.submit-title' | translate }}</h1>
        <p class="message">{{ 'library.property-form-contact.submit-text' | translate }}</p>
        <div class="button-ctn">
          <a class="main-button -secondary" (click)="resetForm()">{{ 'library.property-form-contact.back' | translate }}</a>
        </div>
      </ng-container>
      <ng-container *ngIf="errorMessage">
        <p class="message">{{ "library.property-form-contact.errors.errorMessage" | translate }}</p>
        <div class="button-ctn">
          <a class="main-button -secondary" (click)="retry()">{{ 'library.property-form-contact.retry' | translate }}</a>
        </div>
      </ng-container>
    </div>
  </div>

</div>
