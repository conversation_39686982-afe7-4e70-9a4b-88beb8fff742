<div class="slider-default-cpn">
    <div class="swiper swiper-default">
        <div class="swiper-container">
            <div class="swiper-wrapper">
                <!-- Slides -->
                <div class="swiper-slide" *ngFor="let slide of slides">
                    <img class="image" src="{{slide.slideImg}}">
                    <div class="slide-info" *ngIf="showDesc && slide.slideDesc">
                        <p>{{slide.slideDesc}}</p>
                    </div>
                </div>
            </div>
            <div class="swiper-button-prev swiper-btn" [ngClass]="{hidden: slides?.length == 1}"></div>
            <div class="swiper-button-next swiper-btn" [ngClass]="{hidden: slides?.length == 1}"></div>
        </div>
    </div>
</div>
