/* eslint-disable no-unused-vars */
import { Component, OnInit, Input } from '@angular/core';
import Swiper from 'swiper';
import SwiperCore, { Autoplay } from 'swiper';

@Component({
  selector: 'lib-slider-default',
  templateUrl: './slider-default.component.html'
})

export class SliderDefaultComponent implements OnInit {
  @Input() slides;
  @Input() showDesc: boolean;

  constructor () { }

  ngOnInit () {
  }

  ngAfterViewInit () {
    this.initSlider();
  }

  initSlider () {
    SwiperCore.use([Autoplay]);
    setTimeout(function () {
      const slider = new Swiper('.swiper-default .swiper-container', {
        speed: 1000,
        observer: true,
        simulateTouch: false,
        spaceBetween: 0,
        autoplay: {
          delay: 5000
        },
        // autoplayDisableOnInteraction: true,
        runCallbacksOnInit: false,
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        }
      });
    }, 1000);
  }
}
