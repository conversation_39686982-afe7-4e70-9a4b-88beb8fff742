import { Component, OnInit } from '@angular/core';
import { TestimonialsService } from '@/services/v3/testimonials/testimonials.service';
import Swiper, { Navigation, Pagination } from 'swiper';
import SwiperCore, { Autoplay } from 'swiper';
@Component({
  selector: 'lib-testimonials-slider',
  templateUrl: './testimonials-slider.component.html'
})

export class TestimonialsSliderComponent implements OnInit {
  testimonialSwiper: any;
  testimonials = [];

  constructor (
    private testimonialsService: TestimonialsService
  ) { }

  ngOnInit () {}

  ngAfterViewInit () {
    this.testimonialsService.getTestimonials(3, 'rdm').subscribe(({ data }) => {
      this.testimonials = data;
      this.initSlider();
    });
  }

  initSlider () {
    Swiper.use([Navigation, Pagination]);
    SwiperCore.use([Autoplay]);
    setTimeout(() => {
      this.testimonialSwiper = new Swiper('.swiper-testimonials .swiper-container', {
        speed: 1000,
        observer: true,
        loop: true,
        autoplay: true,
        autoHeight: false,
        runCallbacksOnInit: false,
        pagination: {
          el: '.swiper-pagination',
          type: 'bullets',
        },
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev',
        },
        breakpoints: {
          992: {
            autoHeight: true,
          },
        }
      });
    }, 1000);
  }
}
