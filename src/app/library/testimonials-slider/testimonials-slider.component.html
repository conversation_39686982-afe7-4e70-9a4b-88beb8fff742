<div class="testimonials-slider-cpn" *ngIf="testimonials?.length > 0">
    <div class="swiper swiper-testimonials">
        <h3 class="title">{{ 'library.testimonials-slider.title' | translate }}</h3>
        <div class="swiper-container">
            <div class="swiper-wrapper">
                <!-- Slides -->
                <div class="swiper-slide" itemscope itemtype="http://schema.org/Review" *ngFor="let testimony of testimonials">

                    <div class="container" itemscope itemtype="http://schema.org/Quotation">
                        <ng-container *ngIf="testimony.video_embed">
                            <div class="row">
                                <div class="col col-md-6">
                                    <div class="iframe-container">
                                        <iframe class="e2e-iframe-trusted-src" [src]="videoUrl" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                                    </div>
                                </div>
                                <div class="col col-md-6">
                                    <p itemprop="reviewBody" class="description -left" [innerHtml]="testimony.description | shorten: 300: '...'"></p>
                                    <p itemprop="spokenByCharacter" class="name -left"><span class="line"></span>{{testimony.author}}</p>
                                </div>
                            </div>
                        </ng-container>
                        <ng-container *ngIf="!testimony.video_embed">
                            <p itemprop="reviewBody" class="description" [innerHtml]="testimony.description | shorten: 300: '...'"></p>
                            <p itemprop="spokenByCharacter" class="name"><span class="line"></span>{{testimony.author}}</p>
                        </ng-container>
                    </div>

                </div>
            </div>
            <div class="swiper-button-prev swiper-btn"><i></i></div>
            <div class="swiper-button-next swiper-btn"><i></i></div>
        </div>
        <div class="swiper-pagination"></div>
    </div>
</div>
