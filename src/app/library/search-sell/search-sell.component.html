<div class="search-sell-cpn">
    <div class="container">
        <div class="tabs-ctn col-sm-8">
            <span class="icon icon-eval-1"></span>
            <h2 class="title">{{ 'library.search-sell.title' | translate }}</h2>
            <p>{{ 'library.search-sell.description' | translate }}</p>
            <form class="form-ctn" (ngSubmit)="onSubmitSell()">
                <div class="input-ctn">
                    <input type="search"
                           id="address-input"
                           placeholder="{{ 'library.search-sell.input-placeholder' | translate }}"
                           [(ngModel)]="address"
                           [ngModelOptions]="{standalone: true}" />
                </div>

                <button class="main-button -ghost" type="submit"><span>{{ 'library.search-sell.button' | translate }}</span></button>
            </form>
        </div>
    </div>
</div>
