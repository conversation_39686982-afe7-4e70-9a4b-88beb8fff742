<div class="evaluation-start-cpn" [class.-hide]="showForm">
	<div class="container">

		<h1 class="-page-title">{{ blockTitle }}</h1>
		<div class="-page-description" [innerHtml]="blockContent"></div>

    <form class="form-row">
      <div class="input-ctn">
	      <input type="search" id="address-input" class="large" placeholder="{{ 'library.evaluation-form.placeholder' | translate }}" />
      </div>
    </form>

		<div class="button-ctn -center">
			<button class="main-button -primary" (click)="onSearchLocation()" [disabled]="!currentLocation">
        {{ 'library.evaluation-form.btn-evaluation' | translate }}
      </button>
		</div>

	</div>
</div>

<div class="evaluation-form-single-page-cpn" [class.-hide]="!showForm" id="evaluateForm">
	<div class="container -small" [ngClass]="{'send': formSend}">
		<form class="alert-form" [formGroup]="evaluateForm" [ngClass]="{'loading-inner': formLoading}" (ngSubmit)="onSubmit()">

			<div class="form-head">
				<h1 class="-page-title">{{ 'library.evaluation-form.form.title' | translate }}</h1>
			</div>

				<div class="map-ctn">
					<div id="street-view">
						<div id="streetNoResult" class="no-results">
						  <p class="no-results-inner">{{ 'library.evaluation-form.form.no-result' | translate }}</p>
						</div>
						<div id="streetNoView" class="no-view" *ngIf="!hasStreetView">
						  <p class="">{{ 'library.evaluation-form.form.no-view' | translate }}</p>
						</div>
					</div>
					<div class="info-map-ctn" *ngIf="currentLocation">
						<p class="found">{{ 'library.evaluation-form.form.property-found' | translate }}</p>
						<p class="address">{{ currentLocation.formatted_address }}  <span (click)="onReturnToSearch()">{{ "library.evaluation-form.form.edit" | translate }}</span></p>
					</div>
				</div>

				<h3 class="emphase-title">{{ "library.evaluation-form.form.property_about" | translate }}</h3>

				<div class="form-row">
					<div class="input-ctn -dual">
						<label>{{ "library.evaluation-form.form.label.type" | translate }}</label>
						 <div *ngIf="propertyTypes.length > 0">
							<ng-container *ngIf="translate.currentLang == 'fr'">
						 		<ng-select [searchable]="false"
									[items]="propertyTypes"
									bindLabel="id"
									bindValue="id"
									[(ngModel)]="selectedPropertyType"
									[ngModelOptions]="{standalone: true}"
									placeholder="{{ 'library.evaluation-form.form.placeholder_choose' | translate }}" class="rooms-filter align-center">
								</ng-select>
							</ng-container>

							<ng-container *ngIf="translate.currentLang == 'en'">
						 		<ng-select [searchable]="false"
									[items]="propertyTypes"
									bindLabel="name"
									bindValue="name"
									[(ngModel)]="selectedPropertyType"
									[ngModelOptions]="{standalone: true}"
									placeholder="{{ 'library.evaluation-form.form.placeholder_choose' | translate }}" class="rooms-filter align-center">
						 		</ng-select>
							</ng-container>
						</div>
					</div>

					<div class="input-ctn -dual">
						<label>{{ 'library.evaluation-form.form.label.years' | translate }}</label>
						<input mask="0000" [ngClass]="{'-error': constructYear.invalid && (constructYear.dirty || constructYear.touched)}" formControlName="constructYear" type="text" name="construction_year">
						<div class="form-control-feedback" *ngIf="constructYear.errors && (constructYear.dirty || constructYear.touched)">
						  <p>{{ "library.evaluation-form.form.errors.constructYear_invalid" | translate }}</p>
						</div>
					</div>
				</div>

				<div class="form-row">
					<div class="input-ctn -dual">
						<label>{{ 'library.evaluation-form.form.label.surface' | translate }}</label>
						<ng-select
							[searchable]="false"
							[items]="propertySurfaces"
							bindLabel="name"
							bindValue="name"
							[(ngModel)]="selectedPropertySurface"
							[ngModelOptions]="{standalone: true}"
							placeholder="{{ 'library.evaluation-form.form.placeholder_choose' | translate }}" class="rooms-filter align-center">
						</ng-select>
					</div>
					<div class="input-ctn -dual">
						<label>{{ 'library.evaluation-form.form.label.dimension' | translate }}</label>
						<ng-select
							[searchable]="false"
							[items]="propertyDimensions"
							bindLabel="name"
							bindValue="name"
							[(ngModel)]="selectedPropertyDimensions"
							[ngModelOptions]="{standalone: true}"
							placeholder="{{ 'library.evaluation-form.form.placeholder_choose' | translate }}" class="rooms-filter align-center">
						</ng-select>
					</div>
				</div>

				<div class="form-row">
					<div class="input-ctn -half">
						<label class="block">{{ 'library.evaluation-form.form.label.upload' | translate }}</label>
						<p class="img-weight-warning">{{"library.evaluation-form.evaluation-image-weight" | translate}}</p>
						<label id="label-file-upload" for="file-upload" class="main-button -primary-small">{{ 'library.evaluation-form.form.label.upload-btn' | translate }}</label>
						<input class="-hide" id="file-upload" type="file" name="file" ng2FileSelect formControlName="fileInput" [uploader]="uploader" multiple (change)="onFileChange($event)" accept="image/*" />
						
					</div>
				</div>

				<!-- <h3 class="emphase-title">{{ "library.evaluation-form.form.property_about_2" | translate }}</h3> -->
				<div class="form-row">
					<div class="input-ctn -dual">
						<label>{{ 'library.evaluation-form.form.label.bedroom' | translate }}</label>
						<ng-select
							[searchable]="false"
							[items]="propertyBedRooms"
							bindLabel="name"
							bindValue="name"
							[(ngModel)]="nbBedRooms"
							[ngModelOptions]="{standalone: true}"
							placeholder="{{ 'library.evaluation-form.form.placeholder_choose' | translate }}" class="rooms-filter align-center">
						</ng-select>
					</div>
					<div class="input-ctn -dual">
						<label>{{ 'library.evaluation-form.form.label.bathroom' | translate }}</label>
						<ng-select
							[searchable]="false"
							[items]="propertyRooms"
							bindLabel="name"
							bindValue="name"
							[(ngModel)]="nbBathRooms"
							[ngModelOptions]="{standalone: true}"
							placeholder="{{ 'library.evaluation-form.form.placeholder_choose' | translate }}" class="rooms-filter align-center">
						</ng-select>
					</div>
				</div>

				<div class="form-row">
					<div class="input-ctn -dual">
						<label>{{ 'library.evaluation-form.form.label.car_park' | translate }}</label>
						<ng-select
							[searchable]="false"
							[items]="propertyRooms"
							bindLabel="name"
							bindValue="name"
							[(ngModel)]="nbCarParks"
							[ngModelOptions]="{standalone: true}"
							placeholder="{{ 'library.evaluation-form.form.placeholder_choose' | translate }}" class="rooms-filter align-center">
						</ng-select>
					</div>
					<div class="input-ctn -dual">
						<label>{{ 'library.evaluation-form.form.label.garages' | translate }}</label>
						<ng-select
							[searchable]="false"
							[items]="propertyRooms"
							bindLabel="name"
							bindValue="name"
							[(ngModel)]="nbGarage"
							[ngModelOptions]="{standalone: true}"
							placeholder="{{ 'library.evaluation-form.form.placeholder_choose' | translate }}" class="rooms-filter align-center">
						</ng-select>
					</div>
				</div>

				<div class="form-row">
					<div class="input-ctn -full">
						<label>{{ 'library.evaluation-form.form.label.buy_reason' | translate }}</label>
						<input type="text" formControlName="reasonBuy" name="reason">
					</div>
				</div>

				<div class="form-row">
					<div class="input-ctn -full">
						<label>{{ 'library.evaluation-form.form.label.renovation' | translate }}</label>
						<input type="text" formControlName="renovations" name="renovations">
					</div>
				</div>

				<h3 class="emphase-title">{{ 'library.evaluation-form.form.emphase-title' | translate }}</h3>

				<div class="form-row" formGroupName="name">
					<div class="input-ctn -dual">
						<label>{{ "library.evaluation-form.form.label.firstname" | translate }} <span class="required-input">*</span></label>
						<input type="text" name="first_name" [ngClass]="{'-error': firstName.invalid && (firstName.dirty || firstName.touched)}" formControlName="firstName">
						<div class="form-control-feedback"
							 *ngIf="firstName.errors && (firstName.dirty || firstName.touched)">
						  <p>{{ "library.evaluation-form.form.errors.firstname_required" | translate }}</p>
						</div>
					</div>
					<div class="input-ctn -dual">
						<label>{{ "library.evaluation-form.form.label.lastname" | translate }} <span class="required-input">*</span></label>
						<input type="text" [ngClass]="{'-error': lastName.invalid && (lastName.dirty || lastName.touched)}" formControlName="lastName" name="last_name">
						<div class="form-control-feedback"
							 *ngIf="lastName.errors && (lastName.dirty || lastName.touched)">
						  <p *ngIf="lastName.errors.required">{{ "library.evaluation-form.form.errors.lastname_required" | translate }}</p>
						</div>
					</div>
				</div>
				<div class="form-row -bottom" formGroupName="phone">
					<div class="input-ctn -dual">
						<label>{{ "library.evaluation-form.form.label.phone" | translate }} <span class="required-input">*</span></label>
						<input type="text" mask="************" [ngClass]="{'-error': phoneNumber.invalid && (phoneNumber.dirty || phoneNumber.touched)}" formControlName="phoneNumber" name="phone">
					</div>
					<div class="input-ctn -dual -small">
						<label></label>
						<input type="text" name="ext" placeholder="Ext" formControlName="phoneExt">
					</div>
					<div class="form-control-feedback" *ngIf="phoneNumber.errors && (phoneNumber.dirty || phoneNumber.touched)">
						<p *ngIf="phoneNumber.errors.required">{{ "library.evaluation-form.form.errors.phone_required" | translate }}</p>
						<p *ngIf="phoneNumber.errors.pattern">{{ "library.evaluation-form.form.errors.phone_invalid" | translate }}</p>
						<p *ngIf="phoneNumber.errors.minlength">{{ "library.evaluation-form.form.errors.phone_invalid" | translate }}</p>
					</div>
				</div>
				<div class="form-row">
					<div class="input-ctn">
						<label>{{ "library.evaluation-form.form.label.email" | translate }} <span class="required-input">*</span></label>
						<input type="text" (focus)="isTyping = true" (blur)="isTyping = false" [ngClass]="{'-error': email.invalid && (email.dirty || email.touched) && !isTyping}" formControlName="email" name="email">
						<div class="form-control-feedback"
						 *ngIf="email.errors && (email.dirty || email.touched) && !isTyping">
							<p *ngIf="email.errors.required">{{ "library.evaluation-form.form.errors.email_required" | translate }}</p>
							<p *ngIf="email.errors.pattern">{{ "library.evaluation-form.form.errors.email_invalid" | translate }}</p>
						</div>
					</div>
				</div>

				<p class="warning-message">{{ "library.evaluation-form.form.submit-validation" | translate }}</p>
				<p class="-page-required">{{ "library.evaluation-form.form.required" | translate }}</p>

				<div class="button-ctn">
					<button class="main-button -primary" [disabled]="!evaluateForm.valid">{{ "library.evaluation-form.form.submit" | translate }}</button>
				</div>
		</form>
		<div *ngIf="formLoading" class="form-loader">
			<div class="loading-circle"></div>
		</div>
		<div class="form-response" [ngClass]="{'show': formSend}">
	  		<h1 class="-page-title">{{ "library.evaluation-form.form.title" | translate }}</h1>
			<ng-container *ngIf="successMessage">
				<p class="message">{{ "library.evaluation-form.form.submit-message" | translate }}</p>
				<div class="button-ctn">
		  			<a class="main-button -primary" (click)="resetForm()">{{ 'library.evaluation-form.form.submit-another' | translate }}</a>
				</div>
			</ng-container>
			<ng-container *ngIf="errorMessage">
				<p class="message">{{ "library.evaluation-form.form.errors.submit-message" | translate }}</p>
				<div class="button-ctn">
					<a class="main-button -primary" (click)="retry()">{{ 'library.evaluation-form.form.errors.back' | translate }}</a>
				</div>
			</ng-container>
		</div>
	</div>
</div>
