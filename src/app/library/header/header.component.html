<lib-header-panel></lib-header-panel>
<header id="header" [ngClass]="{'transparent': transparent === 'true', 'special': specialColor === 'true'}">
    <div class="sub-header">
        <ul class="sub-head-menu">
            <div class="share-ctn">
                <ul>
                    <li><a class="icon-logo-facebook" href="https://www.facebook.com/Marc-Andr%C3%A9-Bourdon-Remax-149671451749827/" target="_blank"></a></li>
                    <li><a class="icon-logo-instagram" href="https://www.instagram.com/equipebourdonremax/" target="_blank"></a></li>
                    <li><a class="icon-tiktok" href="https://www.tiktok.com/@collectionbourdon" target="_blank"></a></li>
                </ul>
            </div>
            <li>
                <a class="switch" (click)="switchLang(translate.currentLang == 'fr' ? 'en' : 'fr')">{{ "global.switchlang" | translate }}</a>
                <a class="english-fake" href="/en"></a>
            </li>
            <li class="number-ctn" itemscope itemtype="http://schema.org/ContactPoint">
                <a itemprop="telephone" href="tel:************">
                    <i class="icon-phone"></i>************</a>
            </li>
        </ul>
    </div>
    <div class="main-header">
        <div class="main-menu-ctn">
            <nav class="main-menu">

                <li class="item-menu" routerLinkActive="active-child" [routerLinkActiveOptions]="{exact: true}">
                    <a>{{ "library.header.properties" | translate }}
                        <i class="icon-dropdown-menu"></i>
                    </a>
                    <div class="secondary-ul multi-column">
                        <ul>
                            <li class="item-link">
                                <a [routerLink]="['urls.search-properties' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.search-properties" | translate }}</a>
                            </li>
                            <li class="item-link">
                                <a [routerLink]="['urls.neighborhoods' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.neighborhoods" | translate }}</a>
                            </li>
                        </ul>
                        <lib-header-last-properties></lib-header-last-properties>
                    </div>
                </li>
                <li class="item-menu" routerLinkActive="active-child" [routerLinkActiveOptions]="{exact: true}">
                    <a>{{ "library.header.buy" | translate }}
                        <i class="icon-dropdown-menu"></i>
                    </a>
                    <ul class="secondary-ul">
                        <li class="item-link">
                            <a [routerLink]="['urls.buy-house' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.buying-tips" | translate }}</a>
                        </li>
                        <li class="item-link">
                            <a [routerLink]="['urls.real-estate-alert' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.alert" | translate }}</a>
                        </li>
                    </ul>
                </li>
                <li class="item-menu" routerLinkActive="active-child" [routerLinkActiveOptions]="{exact: true}">
                    <a>{{ "library.header.sell" | translate }}
                        <i class="icon-dropdown-menu"></i>
                    </a>
                    <ul class="secondary-ul">
                        <li class="item-link">
                            <a [routerLink]="['urls.real-estate-online-evaluation' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.evaluate-online" | translate }}</a>
                        </li>
                        <li class="item-link">
                            <a [routerLink]="['urls.sell-house' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.selling-tips" | translate }}</a>
                        </li>

                    </ul>
                </li>
                <li class="item-menu">
                    <a [routerLink]="['urls.real-estate-blog' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.blog" | translate }}</a>
                </li>
                <li class="item-menu" routerLinkActive="active-child" [routerLinkActiveOptions]="{exact: true}">
                    <a>{{ "library.header.team" | translate }}
                        <i class="icon-dropdown-menu"></i>
                    </a>
                    <ul class="secondary-ul">
                        <li>
                            <a [routerLink]="['urls.real-estate-agents' | translate]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.our-team" | translate }}</a>
                        </li>
                        <li>
                            <a [routerLink]="['urls.specialists' | translate]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.specialists" | translate }}</a>
                        </li>
                        <li>
                            <a [routerLink]="['urls.testimonials' | translate]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.testimonials" | translate }}</a>
                        </li>
                        <li>
                            <a [routerLink]="['urls.career' | translate]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.career" | translate }}</a>
                        </li>
                    </ul>
                </li>
                <li class="item-menu">
                    <a [routerLink]="['urls.contact' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.contact" | translate }}</a>
                </li>
            </nav>
        </div>

        <a href="#" class="header-menu-toggle" (click)="onOpenPanel($event)">
            <span></span>
            <span></span>
            <span></span>
        </a>

    </div>

    <div class="logo-ctn" *ngIf="!logoSwap" itemscope itemtype="http://schema.org/Organization">
        <a itemprop="url" [routerLink]="['urls.home' | translate]"><img src="assets/images/common/logo-equipe_bourdon.svg" alt="{{ 'library.header.alt-image' | translate }}"></a>
    </div>
    <div class="logo-ctn --white" *ngIf="!logoSwap" itemscope itemtype="http://schema.org/Organization">
        <a itemprop="url" [routerLink]="['urls.home' | translate]"><img src="assets/images/common/logo-equipe_bourdon-blanc.svg" alt="{{ 'library.header.alt-image' | translate }}"></a>
    </div>
</header>
