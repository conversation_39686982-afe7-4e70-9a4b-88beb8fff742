import { Component, OnInit, Input } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { ReCaptchaV3Service } from 'ng-recaptcha';

import { ContactService } from '@/services/v3/contact/contact.service';
import { environment } from 'environments/environment';

@Component({
  selector: 'lib-broker-contact-form',
  templateUrl: './broker-contact-form.component.html'
})

export class BrokerContactFormComponent implements OnInit {
  @Input() narrow;
  @Input() formSubject;
  @Input() landing = false; // Email and phone fields are on same line on landing form

  contactForm: FormGroup;
  firstName: FormControl;
  lastName: FormControl;
  phoneNumber: FormControl;
  phoneExt: FormControl;
  email: FormControl;
  message: FormControl;

  subject: any;
  isTyping = false;

  successMessage: boolean = false;
  errorMessage: boolean = false;
  formSend: boolean = false;
  formLoading: boolean = false;

  // Recaptcha v3 Subscription
  private submitExecutionSubscription: Subscription

  constructor (
    private recaptchaV3Service: ReCaptchaV3Service,
    private formBuilder: FormBuilder,
    private contactService: ContactService,
    private translate: TranslateService
  ) {}

  ngOnInit () {
    this.createFormControls();
    this.createForm();
    this.delayRCDisplay();
  }

  delayRCDisplay () {
    setTimeout(() => {
      const result = document.getElementsByClassName('grecaptcha-badge');
      const recaptcha = result.item(0) as HTMLElement;
      recaptcha.style.visibility = 'visible';
    }, 2000);
  }

  ngOnDestroy () {
    const result = document.getElementsByClassName('grecaptcha-badge');
    const recaptcha = result.item(0) as HTMLElement;
    recaptcha.style.visibility = 'hidden';

    if (this.submitExecutionSubscription) {
      this.submitExecutionSubscription.unsubscribe();
    }
  }

  private createFormControls () {
    this.subject = new FormControl('');
    this.firstName = new FormControl('', Validators.required);
    this.lastName = new FormControl('', Validators.required);
    this.phoneNumber = new FormControl('', [
      Validators.required,
      Validators.pattern('[0-9]+'),
      Validators.minLength(10)
    ]);
    this.email = new FormControl('', [
      Validators.required,
      Validators.pattern('^\\w+([\\.-]?\\w+)*@\\w+([\\.-]?\\w+)*(\\.\\w{2,3})+$')
    ]);
    this.phoneExt = new FormControl('');
    this.message = new FormControl('', Validators.required);
  }

  private createForm () {
    this.contactForm = this.formBuilder.group({
      name: new FormGroup({
        firstName: this.firstName,
        lastName: this.lastName
      }),
      phone: new FormGroup({
        phoneNumber: this.phoneNumber,
        phoneExt: this.phoneExt
      }),
      email: this.email,
      message: this.message,
      subject: this.subject
    });
  }

  onSubmit () {
    if (!this.contactForm.valid) return false;
    this.formLoading = true;

    this.submitExecutionSubscription = this.recaptchaV3Service.execute('submit').subscribe(token => {

      const formData = {
        ...this.contactForm.value,
        subject: this.formSubject ? this.formSubject : this.translate.instant('library.broker-contact-1.subject'),
        token_captcha: token
      };

      this.contactService.postContact(formData).subscribe(response => {
        this.formSend = true;
        this.formLoading = false;
  
        if (response.success) this.successMessage = true;
        else this.errorMessage = true;
      });

      if(environment.webHookZapier){
        // Send Data to Zapier
        fetch(environment.webHookZapier, {
          method: 'POST',
          body: JSON.stringify(formData),
        }).catch(err => console.error('Erreur Zapier :', err));
      }
    },
    error => console.error(error));
  }

  resetForm () {
    this.formSend = false;
    this.contactForm.reset();
  }

  retry () {
    this.formSend = false;
  }
}
