<!doctype html>
<html lang="fr">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<title></title>

	<meta name="description" content="">
	<meta property="og:title" content="" />
	<meta property="og:description" content="" />
    <meta property="og:image" content="https://www.marc-andrebourdon.com/assets/images/common/og-equipebourdon.jpg" />
	<meta property="og:image:width" content="" />
	<meta property="og:image:height" content="" />
	<meta property="og:url" content="" />
	<meta property="og:type" content="website" />
  	<link rel="canonical" href="" />
	<base href="/">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
	<link rel="icon" type="image/x-icon" href="favicon.ico">

    <!-- DNS Prefetch for external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://consent.cookiebot.com">
    <link rel="preconnect" href="https://addevent.com">

    <!-- Preload critical fonts -->
    <link rel="preload" href="assets/fonts/mem5YaGs126MiZpBA-UN7rgOUuhpKKSTjw.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="assets/fonts/mem6YaGs126MiZpBA-UFUK0Xdc1GAK6bt6o.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="assets/fonts/mem8YaGs126MiZpBA-UFVZ0bf8pkAg.woff2" as="font" type="font/woff2" crossorigin>

    <!-- Preload critical images -->
    <link rel="preload" href="assets/images/common/logo-equipe_bourdon.svg" as="image">
    <link rel="preload" href="assets/images/common/logo-equipe_bourdon-blanc.svg" as="image">
    <link rel="preload" href="assets/images/placeholder/propriete-nb.jpg" as="image">
    <link rel="preload" href="assets/images/placeholder/propriete-nb-wide.jpg" as="image">

    <!-- Preload critical CSS -->
    <link rel="preload" href="styles.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="styles.css"></noscript>

    <!-- Preload critical SVG icons -->
    <link rel="preload" href="assets/images/SVG/icons/bed.svg" as="image">
    <link rel="preload" href="assets/images/SVG/icons/sink.svg" as="image">
    <link rel="preload" href="assets/images/SVG/UI/arrow-left.svg" as="image">
    <link rel="preload" href="assets/images/SVG/UI/arrow-right.svg" as="image">
    <link href="https://fonts.googleapis.com/css2?family=Lora:ital,wght@0,400..700;1,400..700&family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">

	<!-- THIS SCRIPT MUST BE PLACED BEFORE THE COOKIEBOT SCRIPT
		 Set 'data-cookieconsent="ignore"' on main.js to prevent Cookiebot
		 from blocking it when the user doesn't accept cookies -->
    <script data-cookieconsent="ignore">
        var observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                Array.from(mutation.addedNodes).forEach(function(node) {
                    if (node.tagName === 'SCRIPT' && node.getAttribute('src') && node.getAttribute('src').startsWith('main')) {
                        node.setAttribute('data-cookieconsent', 'ignore');
                    }
                });
            });
        });
        observer.observe(document, { childList: true, subtree: true });
    </script>

    <!-- cookiebot -->
    <script id="Cookiebot" src="https://consent.cookiebot.com/uc.js" data-cbid="694f2410-2c84-4daf-b9bf-e0fe6eb4c9f6" data-blockingmode="auto" type="text/javascript"></script>

    <script data-cookieconsent="ignore">
		if (global === undefined) var global = window;
	</script>

	<!-- ############################################# -->
	<!-- ####### A CHANGER AVANT MISE EN LIGNE ####### -->
	<!-- ############################################# -->

	<!-- GA3 -->
	<script>
		(function(b,o,i,l,e,r){b.GoogleAnalyticsObject=l;b[l]||(b[l]=
		function(){(b[l].q=b[l].q||[]).push(arguments)});b[l].l=+new Date;
		e=o.createElement(i);r=o.getElementsByTagName(i)[0];
		e.src='//www.google-analytics.com/analytics.js';
		r.parentNode.insertBefore(e,r)}(window,document,'script','ga'));
        ga('create','***********-1');
	</script>

  <!-- GA4 -->
    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-EJYEV4FG2V"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());

        gtag('config', 'G-EJYEV4FG2V');
    </script>
	<style>
		.showinie { display: none; }
	</style>
</head>

<body>
	<div id="banner-ie">
		<div>
			<b>Votre navigateur Web (Internet Explorer 11 ou 10) n'est pas à jour.</b> </br>
			Mettez à jour votre navigateur pour plus de sécurité et de rapidité et la meilleure expérience sur ce site.
			<a href="https://www.microsoft.com/fr-ca/windows/microsoft-edge">Mettre à jour le navigateur</a>
			<a id="chrome" href="https://www.google.fr/chrome/">Chrome</a>
			<a id="firefox" href="https://www.mozilla.org/fr/firefox/new/">Firefox</a>
		</div>
		<div>
			<b>Your web browser (Internet Explorer 11 or 10) is not up to date.</b> </br>
			Update your browser for more security and speed and the best experience on this site.
			<a href="https://www.microsoft.com/en-ca/windows/microsoft-edge">Update browser</a>
			<a id="chrome" href="https://www.google.com/chrome/">Chrome</a>
			<a id="firefox" href="https://www.mozilla.org/en-US/firefox/new/">Firefox</a>
		</div>
	</div>

    <div id="fb-root"></div>
    <!-- Facebook Pixel Code -->
    <script>
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window,document,'script',

            'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '1424670810910252');
        fbq('track', 'PageView');
    </script>
    <noscript>
        <img height="1" width="1"
             src="https://www.facebook.com/tr?id=1424670810910252&ev=PageView
		&noscript=1"/>
    </noscript>
    <!-- End Facebook Pixel Code -->
    <script>(function(d, s, id) {
        var js, fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) return;
        js = d.createElement(s); js.id = id;
        js.src = 'https://connect.facebook.net/fr_FR/sdk.js#xfbml=1&version=v2.12';
        fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));</script>

	<e-closion></e-closion>

	<script>
		if ('serviceWorker' in navigator) {
			navigator.serviceWorker.register('/service-worker.js').then(function(registration) {
		    	console.log('Service Worker registered : '+registration.scope);
		  	}).catch(function(err) {
		    console.log('Service Worker registration failed: ', err);
		  });
		}
	</script>

	<script src="https://addevent.com/libs/atc/1.6.1/atc.min.js"></script>

	<script>
		var isIE11 = !!window.MSInputMethodContext && !!document.documentMode;
		if(!isIE11){
			var element = document.getElementById("banner-ie");
			element.classList.add("showinie");

			var elementremove = document.getElementById("notIEHide");
			if(elementremove)
				elementremove.remove();
		}
	</script>

</body>
</html>
